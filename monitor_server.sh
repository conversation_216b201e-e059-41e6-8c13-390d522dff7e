#!/bin/bash

# 服务器监控脚本
# 光伏+储能项目经济性分析系统
# 自动监控和重启服务器

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置变量
HEALTH_URL="http://localhost:3001/api/health"
CHECK_INTERVAL=15  # 减少检查间隔
MAX_FAILURES=2     # 减少最大失败次数
RESTART_COOLDOWN=60  # 减少冷却时间
LOG_FILE="monitor.log"

# 打印函数
print_info() {
    echo -e "${BLUE}[MONITOR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[MONITOR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[MONITOR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[MONITOR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 检查服务器状态
check_server() {
    # 检查进程是否存在
    if [ -f ".server.pid" ]; then
        local pid=$(cat .server.pid 2>/dev/null)
        if [ -z "$pid" ]; then
            print_warning "PID文件为空"
            return 1
        fi
        if ! ps -p "$pid" > /dev/null 2>&1; then
            print_warning "服务器进程不存在 (PID: $pid)"
            return 1
        fi
    else
        print_warning "PID文件不存在"
        return 1
    fi

    # 检查端口是否被占用
    if ! netstat -ln 2>/dev/null | grep -q ":3001 " && ! ss -ln 2>/dev/null | grep -q ":3001 "; then
        print_warning "端口3001未被监听"
        return 1
    fi

    # 检查健康状态（多次尝试）
    local health_attempts=3
    for ((i=1; i<=health_attempts; i++)); do
        if curl -s --connect-timeout 5 --max-time 10 "$HEALTH_URL" > /dev/null 2>&1; then
            return 0
        fi
        sleep 1
    done

    print_warning "健康检查失败"
    return 1
}

# 清理僵尸进程
cleanup_processes() {
    print_info "清理僵尸进程..."

    # 清理可能的僵尸Node.js进程
    pkill -f "node.*src/server.js" 2>/dev/null || true
    pkill -f "node.*src/index.js" 2>/dev/null || true

    # 清理端口占用
    local port_pid=$(lsof -ti:3001 2>/dev/null)
    if [ -n "$port_pid" ]; then
        print_info "清理端口3001占用进程: $port_pid"
        kill -9 "$port_pid" 2>/dev/null || true
    fi

    # 清理PID文件
    rm -f ".server.pid"

    sleep 2
}

# 重启服务器
restart_server() {
    print_warning "重启服务器..."

    # 清理进程
    cleanup_processes

    # 设置Node.js环境
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && source "$NVM_DIR/nvm.sh"
    nvm use 18 2>/dev/null || nvm use default 2>/dev/null || true

    # 执行启动脚本
    if [ -f "./start_server.sh" ]; then
        if timeout 60 bash ./start_server.sh; then
            print_info "启动脚本执行完成"
        else
            print_error "启动脚本执行超时或失败"
            return 1
        fi
    else
        print_error "启动脚本不存在"
        return 1
    fi

    # 等待启动完成
    sleep 5

    # 验证重启
    local attempts=6
    for ((i=1; i<=attempts; i++)); do
        if check_server; then
            print_success "服务器重启成功"
            return 0
        fi
        print_info "等待服务器启动... ($i/$attempts)"
        sleep 5
    done

    print_error "服务器重启失败"
    return 1
}

# 监控循环
monitor_loop() {
    print_info "开始监控服务器..."

    local failure_count=0
    local last_restart_time=0

    while true; do
        local current_time=$(date +%s)

        if check_server; then
            if [ $failure_count -gt 0 ]; then
                print_success "服务器恢复正常"
                failure_count=0
            fi
        else
            failure_count=$((failure_count + 1))
            print_warning "服务器检查失败 ($failure_count/$MAX_FAILURES)"

            if [ $failure_count -ge $MAX_FAILURES ]; then
                # 检查冷却期
                local time_since_restart=$((current_time - last_restart_time))
                if [ $time_since_restart -lt $RESTART_COOLDOWN ]; then
                    local wait_time=$((RESTART_COOLDOWN - time_since_restart))
                    print_warning "冷却期内，等待 $wait_time 秒后重启"
                    sleep $wait_time
                fi

                # 尝试重启
                if restart_server; then
                    last_restart_time=$(date +%s)
                    failure_count=0
                else
                    print_error "重启失败，继续监控"
                fi
            fi
        fi

        sleep $CHECK_INTERVAL
    done
}

# 状态检查
show_status() {
    print_info "服务器状态检查"

    # 检查PID文件
    if [ -f ".server.pid" ]; then
        local pid=$(cat .server.pid)
        if ps -p "$pid" > /dev/null 2>&1; then
            print_success "服务器进程运行中 (PID: $pid)"

            # 显示进程信息
            local memory=$(ps -p "$pid" -o rss= | awk '{print $1/1024 " MB"}')
            local cpu=$(ps -p "$pid" -o %cpu= | awk '{print $1 "%"}')
            print_info "内存使用: $memory"
            print_info "CPU使用: $cpu"
        else
            print_error "服务器进程不存在 (PID: $pid)"
        fi
    else
        print_error "PID文件不存在"
    fi

    # 检查健康状态
    if curl -s --max-time 5 "$HEALTH_URL" > /dev/null 2>&1; then
        print_success "健康检查通过"
    else
        print_error "健康检查失败"
    fi

    # 显示日志文件大小
    if [ -f "server.log" ]; then
        local log_size=$(du -h server.log | cut -f1)
        print_info "日志文件大小: $log_size"
    fi
}

# 停止监控
stop_monitor() {
    if [ -f ".monitor.pid" ]; then
        local monitor_pid=$(cat .monitor.pid)
        if ps -p "$monitor_pid" > /dev/null 2>&1; then
            print_info "停止监控进程 (PID: $monitor_pid)"
            kill "$monitor_pid" 2>/dev/null || true
        fi
        rm -f .monitor.pid
    fi
    print_success "监控已停止"
}

# 主函数
main() {
    # 进入服务器目录
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi

    case "${1:-monitor}" in
        "monitor")
            # 保存监控进程PID
            echo $$ > .monitor.pid
            monitor_loop
            ;;
        "status")
            show_status
            ;;
        "stop")
            stop_monitor
            ;;
        "restart")
            restart_server
            ;;
        *)
            echo "用法: $0 {monitor|status|stop|restart}"
            echo "  monitor  - 开始监控服务器"
            echo "  status   - 显示服务器状态"
            echo "  stop     - 停止监控"
            echo "  restart  - 重启服务器"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
