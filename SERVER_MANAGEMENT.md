# 服务器管理解决方案

## 概述

本解决方案彻底替代PM2，专为Hostinger共享主机环境设计，解决了PM2在共享主机中无法持续运行的问题。

## 问题分析

### Hostinger共享主机的限制
1. **环境限制** - 不支持系统级服务和进程管理
2. **NVM依赖** - SSH断开后环境变量丢失，PM2无法找到Node.js
3. **资源限制** - 共享主机可能会杀死长时间运行的进程
4. **权限限制** - 无法使用systemd等系统级进程管理工具

## 解决方案

### 核心脚本

1. **start_server.sh** - 启动Node.js服务器
   - 自动环境检测和配置
   - 智能停止现有进程
   - 健康检查验证

2. **monitor_server.sh** - 服务器监控
   - 自动监控服务器状态
   - 智能重启机制
   - 冷却期防止频繁重启

3. **status.sh** - 详细状态检查
   - 全面的系统状态报告
   - 进程、端口、API健康检查
   - 资源使用情况

4. **manage.sh** - 统一管理入口
   - 简化的命令接口
   - 完整的生命周期管理
   - 日志管理功能

## 使用方法

### 基本命令

```bash
# 启动服务器
./manage.sh start

# 停止服务器
./manage.sh stop

# 重启服务器
./manage.sh restart

# 查看状态
./manage.sh status

# 启动监控
./manage.sh monitor

# 查看日志
./manage.sh logs

# 健康检查
./manage.sh health

# 清理日志
./manage.sh clean
```

### 部署流程

1. **首次部署**
   ```bash
   # 上传所有脚本到服务器
   # 设置执行权限
   chmod +x *.sh
   
   # 启动服务器和监控
   ./manage.sh start
   ./manage.sh monitor
   ```

2. **更新部署**
   ```bash
   # 使用更新脚本
   bash update_hostinger.sh
   ```

## 主要特性

### ✅ 稳定性优化
- **智能环境检测** - 自动配置NVM和Node.js环境
- **进程保护** - 多层进程检查和清理
- **冷却机制** - 防止频繁重启导致的资源浪费

### ✅ 监控机制
- **健康检查** - API端点响应检测
- **进程监控** - PID文件和进程状态检查
- **自动重启** - 失败时智能重启服务器

### ✅ 日志管理
- **详细日志** - 服务器和监控日志分离
- **日志轮转** - 自动备份和清理旧日志
- **错误追踪** - 错误记录和统计

### ✅ 资源管理
- **内存监控** - 进程内存使用情况
- **磁盘检查** - 磁盘空间使用监控
- **端口管理** - 端口占用检查和清理

## 故障排除

### 常见问题

1. **服务器无法启动**
   ```bash
   # 检查详细状态
   ./manage.sh status
   
   # 查看日志
   ./manage.sh logs
   
   # 清理并重启
   ./manage.sh stop
   ./manage.sh start
   ```

2. **监控不工作**
   ```bash
   # 检查监控状态
   ./manage.sh status
   
   # 重新启动监控
   ./manage.sh monitor
   ```

3. **API无响应**
   ```bash
   # 健康检查
   ./manage.sh health
   
   # 重启服务器
   ./manage.sh restart
   ```

### 日志位置

- **服务器日志**: `server/server.log`
- **监控日志**: `server/monitor.log`
- **PID文件**: `server/.server.pid`, `server/.monitor.pid`

## 优势对比

| 特性 | PM2 | 新解决方案 |
|------|-----|-----------|
| Hostinger兼容性 | ❌ 不稳定 | ✅ 完全兼容 |
| 环境依赖 | ❌ 依赖NVM | ✅ 自动配置 |
| 进程管理 | ❌ 易丢失 | ✅ 原生bash |
| 监控功能 | ✅ 内置 | ✅ 自定义 |
| 日志管理 | ✅ 内置 | ✅ 增强版 |
| 资源占用 | ❌ 较高 | ✅ 极低 |
| 故障恢复 | ❌ 不可靠 | ✅ 智能重启 |

## 技术实现

### 环境检测
```bash
# 自动检测Hostinger环境
if [[ "$PWD" == *"/domains/pv-analysis.top/"* ]]; then
    # 配置NVM环境
    export NVM_DIR="$HOME/.nvm"
    source "$NVM_DIR/nvm.sh"
    nvm use node
fi
```

### 进程管理
```bash
# 使用原生bash进程管理
nohup node src/index.js > server.log 2>&1 &
echo $! > .server.pid
```

### 健康检查
```bash
# API健康检查
curl -s --max-time 10 "http://localhost:3001/api/health"
```

## 维护建议

1. **定期检查** - 每天检查一次服务器状态
2. **日志清理** - 每周清理一次日志文件
3. **监控告警** - 关注监控日志中的重启记录
4. **资源监控** - 定期检查磁盘和内存使用情况

## 总结

这个解决方案完全替代了PM2，专门针对Hostinger共享主机环境的限制进行了优化。通过原生bash脚本实现了稳定的进程管理、智能监控和自动恢复功能，确保服务器能够在共享主机环境中持续稳定运行。
