#!/bin/bash

# 服务器状态检查脚本
# 光伏+储能项目经济性分析系统

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

# 检查服务器状态
check_server_status() {
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统 - 状态检查"
    echo "=================================================="
    echo
    
    # 进入服务器目录
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi
    
    # 1. 检查Node.js环境
    print_info "1. Node.js环境检查:"
    if command -v node >/dev/null 2>&1; then
        print_success "Node.js版本: $(node -v)"
    else
        print_error "Node.js未安装或未在PATH中"
    fi
    echo
    
    # 2. 检查服务器进程
    print_info "2. 服务器进程状态:"
    if [ -f ".server.pid" ]; then
        local pid=$(cat .server.pid)
        if ps -p "$pid" > /dev/null 2>&1; then
            print_success "服务器进程运行中 (PID: $pid)"
            
            # 显示详细信息
            local memory=$(ps -p "$pid" -o rss= 2>/dev/null | awk '{print $1/1024 " MB"}')
            local cpu=$(ps -p "$pid" -o %cpu= 2>/dev/null | awk '{print $1 "%"}')
            local start_time=$(ps -p "$pid" -o lstart= 2>/dev/null)
            
            echo "   启动时间: $start_time"
            echo "   内存使用: $memory"
            echo "   CPU使用: $cpu"
        else
            print_error "服务器进程不存在 (PID文件显示: $pid)"
        fi
    else
        print_error "PID文件不存在"
    fi
    echo
    
    # 3. 检查端口占用
    print_info "3. 端口状态检查:"
    if command -v netstat >/dev/null 2>&1; then
        if netstat -ln | grep -q ":3001 "; then
            print_success "端口3001已监听"
        else
            print_error "端口3001未监听"
        fi
    elif command -v ss >/dev/null 2>&1; then
        if ss -ln | grep -q ":3001 "; then
            print_success "端口3001已监听"
        else
            print_error "端口3001未监听"
        fi
    else
        print_warning "无法检查端口状态（netstat/ss命令不可用）"
    fi
    echo
    
    # 4. 健康检查
    print_info "4. API健康检查:"
    if curl -s --max-time 5 http://localhost:3001/api/health > /dev/null 2>&1; then
        print_success "API健康检查通过"
        
        # 获取API响应
        local response=$(curl -s --max-time 5 http://localhost:3001/api/health 2>/dev/null)
        if [ -n "$response" ]; then
            echo "   响应: $response"
        fi
    else
        print_error "API健康检查失败"
    fi
    echo
    
    # 5. 检查监控进程
    print_info "5. 监控进程状态:"
    if [ -f ".monitor.pid" ]; then
        local monitor_pid=$(cat .monitor.pid)
        if ps -p "$monitor_pid" > /dev/null 2>&1; then
            print_success "监控进程运行中 (PID: $monitor_pid)"
        else
            print_error "监控进程不存在 (PID文件显示: $monitor_pid)"
        fi
    else
        print_warning "监控进程未启动"
    fi
    echo
    
    # 6. 检查日志文件
    print_info "6. 日志文件状态:"
    if [ -f "server.log" ]; then
        local log_size=$(du -h server.log 2>/dev/null | cut -f1)
        local log_lines=$(wc -l < server.log 2>/dev/null)
        print_success "服务器日志存在 (大小: $log_size, 行数: $log_lines)"
        
        # 显示最近的错误
        local error_count=$(grep -c "ERROR\|Error\|error" server.log 2>/dev/null || echo "0")
        if [ "$error_count" -gt 0 ]; then
            print_warning "发现 $error_count 个错误记录"
            echo "   最近的错误:"
            grep "ERROR\|Error\|error" server.log | tail -3 | sed 's/^/   /'
        fi
    else
        print_warning "服务器日志文件不存在"
    fi
    
    if [ -f "monitor.log" ]; then
        local monitor_log_size=$(du -h monitor.log 2>/dev/null | cut -f1)
        print_success "监控日志存在 (大小: $monitor_log_size)"
    else
        print_warning "监控日志文件不存在"
    fi
    echo
    
    # 7. 系统资源检查
    print_info "7. 系统资源状态:"
    
    # 磁盘空间
    local disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        print_success "磁盘空间充足 (已使用: ${disk_usage}%)"
    else
        print_warning "磁盘空间不足 (已使用: ${disk_usage}%)"
    fi
    
    # 内存使用
    if command -v free >/dev/null 2>&1; then
        local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        print_info "内存使用率: ${memory_usage}%"
    fi
    echo
    
    # 8. 最近重启记录
    print_info "8. 最近活动记录:"
    if [ -f "monitor.log" ]; then
        local restart_count=$(grep -c "重启服务器" monitor.log 2>/dev/null || echo "0")
        if [ "$restart_count" -gt 0 ]; then
            print_warning "最近重启次数: $restart_count"
            echo "   最近重启记录:"
            grep "重启服务器\|服务器重启" monitor.log | tail -3 | sed 's/^/   /'
        else
            print_success "无重启记录"
        fi
    fi
    echo
    
    echo "=================================================="
    echo "  状态检查完成 - $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================="
}

# 主函数
main() {
    check_server_status
}

# 执行主函数
main "$@"
