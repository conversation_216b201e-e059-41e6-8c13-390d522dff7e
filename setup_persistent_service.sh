#!/bin/bash

# 设置持久化服务脚本
# 光伏+储能项目经济性分析系统

set -e

# 配置变量
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
PERSISTENT_SCRIPT="$PROJECT_DIR/persistent_server.sh"

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 创建启动脚本
create_startup_script() {
    print_info "创建启动脚本..."
    
    cat > "$PROJECT_DIR/start_persistent_server.sh" << 'EOF'
#!/bin/bash

# 持久化服务器启动脚本
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"

# 进入项目目录
cd "$PROJECT_DIR"

# 启动持久化服务器
./persistent_server.sh start

echo "持久化服务器已启动"
echo "使用 ./persistent_server.sh status 检查状态"
echo "使用 ./persistent_server.sh stop 停止服务"
EOF

    chmod +x "$PROJECT_DIR/start_persistent_server.sh"
    print_success "启动脚本创建完成: start_persistent_server.sh"
}

# 创建停止脚本
create_stop_script() {
    print_info "创建停止脚本..."
    
    cat > "$PROJECT_DIR/stop_persistent_server.sh" << 'EOF'
#!/bin/bash

# 持久化服务器停止脚本
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"

# 进入项目目录
cd "$PROJECT_DIR"

# 停止持久化服务器
./persistent_server.sh stop

echo "持久化服务器已停止"
EOF

    chmod +x "$PROJECT_DIR/stop_persistent_server.sh"
    print_success "停止脚本创建完成: stop_persistent_server.sh"
}

# 创建状态检查脚本
create_status_script() {
    print_info "创建状态检查脚本..."
    
    cat > "$PROJECT_DIR/check_server_status.sh" << 'EOF'
#!/bin/bash

# 服务器状态检查脚本
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"

# 进入项目目录
cd "$PROJECT_DIR"

# 显示服务器状态
./persistent_server.sh status
EOF

    chmod +x "$PROJECT_DIR/check_server_status.sh"
    print_success "状态检查脚本创建完成: check_server_status.sh"
}

# 创建自动重启脚本（用于cron或手动执行）
create_auto_restart_script() {
    print_info "创建自动重启脚本..."
    
    cat > "$PROJECT_DIR/auto_restart_server.sh" << 'EOF'
#!/bin/bash

# 自动重启脚本
# 检查服务器状态，如果异常则重启

PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
LOG_FILE="$PROJECT_DIR/server/auto_restart.log"

# 记录日志
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') $1" >> "$LOG_FILE"
}

# 进入项目目录
cd "$PROJECT_DIR"

# 检查服务器状态
if ./persistent_server.sh check > /dev/null 2>&1; then
    log_message "服务器运行正常"
else
    log_message "服务器异常，尝试重启..."
    ./persistent_server.sh restart >> "$LOG_FILE" 2>&1
    
    # 等待几秒后再次检查
    sleep 10
    if ./persistent_server.sh check > /dev/null 2>&1; then
        log_message "服务器重启成功"
    else
        log_message "服务器重启失败"
    fi
fi
EOF

    chmod +x "$PROJECT_DIR/auto_restart_server.sh"
    print_success "自动重启脚本创建完成: auto_restart_server.sh"
}

# 创建.bashrc配置
setup_bashrc() {
    print_info "设置.bashrc配置..."
    
    # 检查是否已经配置过
    if ! grep -q "PV_ANALYSIS_AUTO_START" ~/.bashrc; then
        cat >> ~/.bashrc << 'EOF'

# PV Analysis Auto Start Configuration
export PV_ANALYSIS_AUTO_START=1

# 自动启动PV分析服务器（仅在交互式shell中）
if [[ $- == *i* ]] && [[ -n "$PV_ANALYSIS_AUTO_START" ]]; then
    PV_PROJECT_DIR="$HOME/domains/pv-analysis.top/public_html"
    if [[ -f "$PV_PROJECT_DIR/persistent_server.sh" ]]; then
        # 检查服务器是否运行，如果没有则启动
        cd "$PV_PROJECT_DIR"
        if ! ./persistent_server.sh check > /dev/null 2>&1; then
            echo "检测到PV分析服务器未运行，正在启动..."
            ./persistent_server.sh start > /dev/null 2>&1 &
        fi
    fi
fi
EOF
        print_success ".bashrc配置已添加"
    else
        print_info ".bashrc已经配置过"
    fi
}

# 创建简单的定时任务脚本（替代cron）
create_timer_script() {
    print_info "创建定时任务脚本..."
    
    cat > "$PROJECT_DIR/timer_monitor.sh" << 'EOF'
#!/bin/bash

# 定时监控脚本（替代cron）
# 每5分钟检查一次服务器状态

PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
TIMER_PID_FILE="$PROJECT_DIR/server/.timer.pid"

# 停止现有定时器
if [[ -f "$TIMER_PID_FILE" ]]; then
    OLD_PID=$(cat "$TIMER_PID_FILE")
    if ps -p "$OLD_PID" > /dev/null 2>&1; then
        kill "$OLD_PID" 2>/dev/null || true
    fi
    rm -f "$TIMER_PID_FILE"
fi

# 启动新的定时器
{
    while true; do
        sleep 300  # 5分钟
        cd "$PROJECT_DIR"
        ./auto_restart_server.sh
    done
} &

# 保存PID
echo $! > "$TIMER_PID_FILE"

echo "定时监控已启动，PID: $(cat "$TIMER_PID_FILE")"
echo "每5分钟检查一次服务器状态"
EOF

    chmod +x "$PROJECT_DIR/timer_monitor.sh"
    print_success "定时任务脚本创建完成: timer_monitor.sh"
}

# 创建完整的管理脚本
create_management_script() {
    print_info "创建管理脚本..."
    
    cat > "$PROJECT_DIR/manage_server.sh" << 'EOF'
#!/bin/bash

# 服务器管理脚本
# 光伏+储能项目经济性分析系统

PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"

show_help() {
    echo "光伏+储能项目经济性分析系统 - 服务器管理"
    echo
    echo "用法: $0 <命令>"
    echo
    echo "命令:"
    echo "  start     - 启动服务器和所有监控"
    echo "  stop      - 停止服务器和所有监控"
    echo "  restart   - 重启服务器和所有监控"
    echo "  status    - 显示详细状态信息"
    echo "  logs      - 显示服务器日志"
    echo "  monitor   - 启动定时监控"
    echo "  health    - 快速健康检查"
    echo "  help      - 显示此帮助信息"
    echo
}

case "${1:-help}" in
    "start")
        echo "启动服务器和监控..."
        cd "$PROJECT_DIR"
        ./persistent_server.sh start
        ./timer_monitor.sh
        echo "服务器和监控已启动"
        ;;
    "stop")
        echo "停止服务器和监控..."
        cd "$PROJECT_DIR"
        ./persistent_server.sh stop
        # 停止定时器
        if [[ -f "server/.timer.pid" ]]; then
            TIMER_PID=$(cat "server/.timer.pid")
            if ps -p "$TIMER_PID" > /dev/null 2>&1; then
                kill "$TIMER_PID" 2>/dev/null || true
            fi
            rm -f "server/.timer.pid"
        fi
        echo "服务器和监控已停止"
        ;;
    "restart")
        echo "重启服务器和监控..."
        cd "$PROJECT_DIR"
        ./persistent_server.sh restart
        ./timer_monitor.sh
        echo "服务器和监控已重启"
        ;;
    "status")
        cd "$PROJECT_DIR"
        ./persistent_server.sh status
        ;;
    "logs")
        cd "$PROJECT_DIR"
        echo "=== 服务器日志 ==="
        tail -50 server/persistent.log 2>/dev/null || echo "未找到日志文件"
        echo
        echo "=== 自动重启日志 ==="
        tail -20 server/auto_restart.log 2>/dev/null || echo "未找到自动重启日志"
        ;;
    "monitor")
        cd "$PROJECT_DIR"
        ./timer_monitor.sh
        ;;
    "health")
        cd "$PROJECT_DIR"
        ./persistent_server.sh check
        ;;
    "help"|*)
        show_help
        ;;
esac
EOF

    chmod +x "$PROJECT_DIR/manage_server.sh"
    print_success "管理脚本创建完成: manage_server.sh"
}

# 主函数
main() {
    print_info "开始设置持久化服务..."
    
    # 确保持久化脚本可执行
    chmod +x "$PERSISTENT_SCRIPT"
    
    # 创建各种脚本
    create_startup_script
    create_stop_script
    create_status_script
    create_auto_restart_script
    create_timer_script
    create_management_script
    
    # 设置.bashrc
    setup_bashrc
    
    print_success "持久化服务设置完成！"
    echo
    print_info "可用命令："
    print_info "  ./manage_server.sh start    - 启动服务器和监控"
    print_info "  ./manage_server.sh stop     - 停止服务器和监控"
    print_info "  ./manage_server.sh status   - 检查状态"
    print_info "  ./manage_server.sh logs     - 查看日志"
    print_info "  ./manage_server.sh help     - 显示帮助"
    echo
    print_info "或者使用单独的脚本："
    print_info "  ./persistent_server.sh start"
    print_info "  ./persistent_server.sh status"
    print_info "  ./timer_monitor.sh"
    echo
    print_warning "注意：这个解决方案不依赖PM2，使用原生进程管理"
    print_warning "定时监控每5分钟检查一次服务器状态并自动重启"
}

# 执行主函数
main "$@"
