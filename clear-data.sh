#!/bin/bash

# 清除数据脚本
# 用于清除系统中的虚拟数据

# 显示帮助信息
show_help() {
  echo "清除数据脚本"
  echo "用于清除系统中的虚拟数据"
  echo ""
  echo "用法: ./clear-data.sh [选项]"
  echo ""
  echo "选项:"
  echo "  -a, --all         清除所有虚拟数据"
  echo "  -p, --projects    仅清除项目库数据"
  echo "  -i, --irradiance  仅清除光照数据库数据"
  echo "  -h, --help        显示帮助信息"
  echo ""
  echo "示例:"
  echo "  ./clear-data.sh -a  # 清除所有虚拟数据"
  echo "  ./clear-data.sh -p  # 仅清除项目库数据"
}

# 解析命令行参数
if [ $# -eq 0 ]; then
  # 如果没有参数，显示交互式菜单
  echo "请选择要清除的数据类型:"
  echo "1) 清除所有虚拟数据"
  echo "2) 仅清除项目库数据"
  echo "3) 仅清除光照数据库数据"
  echo "4) 退出"
  read -p "请输入选项 [1-4]: " choice
  
  case $choice in
    1)
      node scripts/clearData.js all
      ;;
    2)
      node scripts/clearData.js projects
      ;;
    3)
      node scripts/clearData.js irradiance
      ;;
    4)
      echo "已取消操作"
      exit 0
      ;;
    *)
      echo "无效选项"
      exit 1
      ;;
  esac
else
  # 解析命令行参数
  case "$1" in
    -a|--all)
      node scripts/clearData.js all
      ;;
    -p|--projects)
      node scripts/clearData.js projects
      ;;
    -i|--irradiance)
      node scripts/clearData.js irradiance
      ;;
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      show_help
      exit 1
      ;;
  esac
fi

echo "操作完成"
