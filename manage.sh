#!/bin/bash

# 统一管理脚本
# 光伏+储能项目经济性分析系统
# 替代PM2的完整解决方案

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "光伏+储能项目经济性分析系统 - 服务器管理"
    echo
    echo "用法: $0 <命令>"
    echo
    echo "命令:"
    echo "  start     - 启动服务器"
    echo "  stop      - 停止服务器"
    echo "  restart   - 重启服务器"
    echo "  status    - 显示详细状态"
    echo "  monitor   - 启动监控（后台运行）"
    echo "  logs      - 显示服务器日志"
    echo "  health    - 快速健康检查"
    echo "  clean     - 清理日志文件"
    echo "  help      - 显示此帮助信息"
    echo
    echo "主要特性:"
    echo "  ✓ 完全替代PM2，专为Hostinger共享主机设计"
    echo "  ✓ 自动环境检测和配置"
    echo "  ✓ 智能进程监控和自动重启"
    echo "  ✓ 详细的状态报告和日志管理"
    echo "  ✓ 稳定性和可靠性优化"
    echo
}

# 启动服务器
start_server() {
    print_info "启动服务器..."
    
    # 检查脚本是否存在
    if [ ! -f "./start_server.sh" ]; then
        print_error "启动脚本不存在"
        return 1
    fi
    
    # 执行启动脚本
    bash ./start_server.sh
    
    if [ $? -eq 0 ]; then
        print_success "服务器启动完成"
        return 0
    else
        print_error "服务器启动失败"
        return 1
    fi
}

# 停止服务器
stop_server() {
    print_info "停止服务器..."
    
    # 进入服务器目录
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi
    
    # 停止监控
    if [ -f ".monitor.pid" ]; then
        local monitor_pid=$(cat .monitor.pid)
        if ps -p "$monitor_pid" > /dev/null 2>&1; then
            print_info "停止监控进程 (PID: $monitor_pid)"
            kill "$monitor_pid" 2>/dev/null || true
        fi
        rm -f .monitor.pid
    fi
    
    # 停止服务器
    if [ -f ".server.pid" ]; then
        local server_pid=$(cat .server.pid)
        if ps -p "$server_pid" > /dev/null 2>&1; then
            print_info "停止服务器进程 (PID: $server_pid)"
            kill "$server_pid" 2>/dev/null || true
            sleep 2
            if ps -p "$server_pid" > /dev/null 2>&1; then
                kill -9 "$server_pid" 2>/dev/null || true
            fi
        fi
        rm -f .server.pid
    fi
    
    # 清理端口占用
    pkill -f "node.*src/index.js" 2>/dev/null || true
    
    # 停止PM2（如果存在）
    if command -v pm2 >/dev/null 2>&1; then
        pm2 delete pv-server 2>/dev/null || true
        pm2 kill 2>/dev/null || true
    fi
    
    print_success "服务器已停止"
}

# 重启服务器
restart_server() {
    print_info "重启服务器..."
    stop_server
    sleep 2
    start_server
}

# 启动监控
start_monitor() {
    print_info "启动监控..."
    
    # 检查监控脚本是否存在
    if [ ! -f "./monitor_server.sh" ]; then
        print_error "监控脚本不存在"
        return 1
    fi
    
    # 启动监控（后台运行）
    nohup bash ./monitor_server.sh monitor > /dev/null 2>&1 &
    
    print_success "监控已启动"
}

# 显示日志
show_logs() {
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi
    
    if [ -f "server.log" ]; then
        print_info "服务器日志 (最近50行):"
        echo "----------------------------------------"
        tail -50 server.log
        echo "----------------------------------------"
    else
        print_warning "服务器日志文件不存在"
    fi
    
    if [ -f "monitor.log" ]; then
        print_info "监控日志 (最近20行):"
        echo "----------------------------------------"
        tail -20 monitor.log
        echo "----------------------------------------"
    else
        print_warning "监控日志文件不存在"
    fi
}

# 快速健康检查
health_check() {
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi
    
    print_info "快速健康检查..."
    
    # 检查进程
    if [ -f ".server.pid" ]; then
        local pid=$(cat .server.pid)
        if ps -p "$pid" > /dev/null 2>&1; then
            print_success "服务器进程运行中"
        else
            print_error "服务器进程不存在"
            return 1
        fi
    else
        print_error "PID文件不存在"
        return 1
    fi
    
    # 检查API
    if curl -s --max-time 5 http://localhost:3001/api/health > /dev/null 2>&1; then
        print_success "API健康检查通过"
        return 0
    else
        print_error "API健康检查失败"
        return 1
    fi
}

# 清理日志
clean_logs() {
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi
    
    print_info "清理日志文件..."
    
    # 备份并清理服务器日志
    if [ -f "server.log" ]; then
        local log_size=$(du -h server.log | cut -f1)
        print_info "备份服务器日志 (大小: $log_size)"
        cp server.log "server.log.$(date +%Y%m%d_%H%M%S)"
        > server.log
    fi
    
    # 备份并清理监控日志
    if [ -f "monitor.log" ]; then
        local monitor_log_size=$(du -h monitor.log | cut -f1)
        print_info "备份监控日志 (大小: $monitor_log_size)"
        cp monitor.log "monitor.log.$(date +%Y%m%d_%H%M%S)"
        > monitor.log
    fi
    
    # 清理旧备份（保留最近5个）
    ls -t server.log.* 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
    ls -t monitor.log.* 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
    
    print_success "日志清理完成"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            start_server
            ;;
        "stop")
            stop_server
            ;;
        "restart")
            restart_server
            ;;
        "status")
            if [ -f "./status.sh" ]; then
                bash ./status.sh
            else
                print_error "状态检查脚本不存在"
            fi
            ;;
        "monitor")
            start_monitor
            ;;
        "logs")
            show_logs
            ;;
        "health")
            health_check
            ;;
        "clean")
            clean_logs
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
