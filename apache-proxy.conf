# Apache配置文件，用于代理API请求到Node.js服务器
# 将此文件放在 ~/domains/pv-analysis.top/conf/ 目录下

<IfModule mod_proxy.c>
    # 启用必要的代理模块
    <IfModule !mod_proxy_http.c>
        LoadModule proxy_http_module modules/mod_proxy_http.so
    </IfModule>
    
    # 代理API请求到Node.js服务器
    ProxyRequests Off
    ProxyPreserveHost On
    
    # 代理所有/api请求到Node.js服务器
    ProxyPass /api http://localhost:3001/api
    ProxyPassReverse /api http://localhost:3001/api
    
    # 设置超时时间
    ProxyTimeout 300
    
    # 设置代理头信息
    <Proxy *>
        Order deny,allow
        Allow from all
        
        # 添加必要的头信息
        RequestHeader set X-Forwarded-Proto "https"
        RequestHeader set X-Forwarded-Port "443"
    </Proxy>
</IfModule>
