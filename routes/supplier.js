/**
 * 供应商路由
 */
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据存储路径
const dataDir = path.join(__dirname, '../../data/suppliers');

// 确保数据目录存在
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 获取所有供应商列表
router.get('/', (req, res) => {
  try {
    if (!fs.existsSync(dataDir)) {
      return res.json({ success: true, data: [] });
    }

    const files = fs.readdirSync(dataDir);
    const dataList = [];

    files.forEach(file => {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(dataDir, file);
          const fileData = fs.readFileSync(filePath, 'utf8');
          const data = JSON.parse(fileData);
          dataList.push(data);
        } catch (err) {
          console.error(`读取文件 ${file} 时出错:`, err);
        }
      }
    });

    // 按更新时间排序，最新的在前面
    dataList.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));

    res.json({ success: true, data: dataList });
  } catch (err) {
    console.error('获取供应商列表时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取单个供应商
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的供应商' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(fileData);

    res.json({ success: true, data });
  } catch (err) {
    console.error('获取供应商时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 创建新的供应商
router.post('/', (req, res) => {
  try {
    const { name, region, address, contact, description } = req.body;

    if (!name) {
      return res.status(400).json({ success: false, message: '缺少必要的数据字段' });
    }

    const id = uuidv4();
    const now = new Date().toISOString();

    const supplierData = {
      id,
      name,
      region: region || '',
      address: address || '',
      contact: contact || '',
      description: description || '',
      createdAt: now,
      updatedAt: now
    };

    const filePath = path.join(dataDir, `${id}.json`);
    fs.writeFileSync(filePath, JSON.stringify(supplierData, null, 2));

    res.status(201).json({ success: true, data: supplierData });
  } catch (err) {
    console.error('创建供应商时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 更新供应商
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { name, region, address, contact, description } = req.body;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的供应商' });
    }

    const fileData = fs.readFileSync(filePath, 'utf8');
    const existingData = JSON.parse(fileData);

    const updatedData = {
      ...existingData,
      name: name || existingData.name,
      region: region !== undefined ? region : existingData.region,
      address: address !== undefined ? address : existingData.address,
      contact: contact !== undefined ? contact : existingData.contact,
      description: description !== undefined ? description : existingData.description,
      updatedAt: new Date().toISOString()
    };

    fs.writeFileSync(filePath, JSON.stringify(updatedData, null, 2));

    res.json({ success: true, data: updatedData });
  } catch (err) {
    console.error('更新供应商时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 删除供应商
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(dataDir, `${id}.json`);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, message: '找不到指定的供应商' });
    }

    fs.unlinkSync(filePath);
    res.json({ success: true, message: '供应商已成功删除' });
  } catch (err) {
    console.error('删除供应商时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;
