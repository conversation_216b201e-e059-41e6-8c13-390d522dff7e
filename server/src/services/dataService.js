/**
 * 数据存储服务
 * 用于处理数据的读写操作
 */
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据目录
const DATA_DIR = path.join(__dirname, '../../data');

/**
 * 确保数据目录存在
 * @param {string} type 数据类型
 */
const ensureDataDir = async (type) => {
  const dir = path.join(DATA_DIR, type);
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (error) {
    console.error(`创建数据目录失败: ${dir}`, error);
    throw new Error('创建数据目录失败');
  }
};

/**
 * 获取数据列表
 * @param {string} type 数据类型
 * @returns {Promise<Array>} 数据列表
 */
const getList = async (type) => {
  try {
    console.log(`开始获取${type}列表`, new Date().toISOString());
    await ensureDataDir(type);

    // 读取索引文件
    const indexPath = path.join(DATA_DIR, type, 'index.json');
    console.log(`索引文件路径: ${indexPath}`);
    let index = [];

    try {
      console.log(`尝试读取索引文件: ${indexPath}`);
      const data = await fs.readFile(indexPath, 'utf8');
      console.log(`索引文件读取成功, 大小: ${data.length} 字节`);

      if (!data || data.trim() === '') {
        console.warn(`索引文件为空, 创建空数组`);
        index = [];
      } else {
        try {
          console.log(`尝试解析索引文件JSON`);
          index = JSON.parse(data);
          console.log(`索引文件解析成功, 数据类型: ${typeof index}, 是否数组: ${Array.isArray(index)}`);

          if (Array.isArray(index)) {
            console.log(`索引包含 ${index.length} 条数据`);
          } else {
            console.warn(`索引不是数组, 创建空数组`);
            index = [];
          }
        } catch (parseError) {
          console.error(`解析索引文件JSON失败: ${parseError.message}`);
          console.error(`索引文件内容前100个字符: ${data.substring(0, 100)}`);
          // 如果解析失败，使用空数组
          index = [];
          // 备份损坏的索引文件
          const backupPath = `${indexPath}.backup.${Date.now()}`;
          await fs.writeFile(backupPath, data, 'utf8');
          console.log(`已备份损坏的索引文件到: ${backupPath}`);
        }
      }
    } catch (error) {
      // 如果索引文件不存在或无法解析，返回空数组
      if (error.code === 'ENOENT') {
        console.log(`索引文件不存在, 将创建新文件`);
      } else {
        console.error(`读取索引文件失败: ${indexPath}`, error);
        console.error(`错误详情:`, JSON.stringify(error, Object.getOwnPropertyNames(error)));
      }
      // 创建空索引文件
      await fs.writeFile(indexPath, JSON.stringify([], null, 2), 'utf8');
      console.log(`已创建空索引文件`);
    }

    console.log(`返回${type}列表, 包含 ${index.length} 条数据`);
    return index;
  } catch (error) {
    console.error(`获取${type}列表失败`, error);
    console.error(`错误详情:`, JSON.stringify(error, Object.getOwnPropertyNames(error)));
    throw new Error(`获取${type}列表失败: ${error.message}`);
  }
};

/**
 * 获取数据详情
 * @param {string} type 数据类型
 * @param {string} id 数据ID
 * @returns {Promise<Object>} 数据详情
 */
const getDetail = async (type, id) => {
  try {
    console.log(`开始获取${type}数据详情, ID: ${id}`);

    // 构建文件路径
    const filePath = path.join(DATA_DIR, type, `${id}.json`);
    console.log(`文件路径: ${filePath}`);

    // 检查文件是否存在
    try {
      await fs.access(filePath);
      console.log(`文件存在: ${filePath}`);
    } catch (accessError) {
      console.error(`文件不存在: ${filePath}`, accessError);
      throw new Error(`${type}数据不存在: ${id}`);
    }

    // 读取文件
    const data = await fs.readFile(filePath, 'utf8');
    console.log(`文件读取成功, 大小: ${data.length} 字节`);

    // 解析JSON
    try {
      const parsedData = JSON.parse(data);

      // 根据数据类型进行不同的验证
      if (type === 'irradiance' || type === 'electricity-prices') {
        // 对于光照数据和电价政策，检查data字段
        console.log(`JSON解析成功, 数据条数: ${parsedData.data?.length || 0}`);

        // 验证数据完整性
        if (!parsedData.data || parsedData.data.length === 0) {
          console.warn(`数据不完整或为空, ID: ${id}`);
        }
      } else if (type === 'projects') {
        // 对于项目数据，检查基本字段
        console.log(`JSON解析成功, 项目名称: ${parsedData.name || '未命名'}`);

        // 验证项目数据完整性
        if (!parsedData.id || !parsedData.name) {
          console.warn(`项目数据不完整, ID: ${id}`);
        }

        // 检查小时数据
        if (parsedData.analysisResults && parsedData.analysisResults.hourlyData) {
          const hourlyDataLength = parsedData.analysisResults.hourlyData.length;
          console.log(`项目小时数据点数: ${hourlyDataLength}`);

          // 如果没有小时数据，记录警告
          if (hourlyDataLength === 0) {
            console.warn(`项目没有小时数据, ID: ${id}`);
          }
        } else {
          console.warn(`项目没有分析结果或小时数据, ID: ${id}`);
        }
      } else {
        // 其他类型数据
        console.log(`JSON解析成功, ID: ${parsedData.id}`);
      }

      return parsedData;
    } catch (parseError) {
      console.error(`JSON解析失败: ${parseError.message}`);
      throw new Error(`${type}数据格式错误: ${id}`);
    }
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`${type}数据不存在: ${id}`);
    }
    console.error(`获取${type}详情失败: ${id}`, error);
    throw new Error(`获取${type}详情失败: ${error.message}`);
  }
};

/**
 * 创建数据
 * @param {string} type 数据类型
 * @param {Object} data 数据对象
 * @returns {Promise<Object>} 创建的数据
 */
const create = async (type, data) => {
  try {
    await ensureDataDir(type);

    // 生成ID和时间戳
    const id = data.id || uuidv4();
    const now = new Date().toISOString();

    const newData = {
      ...data,
      id,
      createdAt: now,
      updatedAt: now
    };

    // 保存数据文件
    const filePath = path.join(DATA_DIR, type, `${id}.json`);
    await fs.writeFile(filePath, JSON.stringify(newData, null, 2), 'utf8');

    // 更新索引
    const indexPath = path.join(DATA_DIR, type, 'index.json');
    let index = [];

    try {
      const indexData = await fs.readFile(indexPath, 'utf8');
      index = JSON.parse(indexData);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error(`读取索引文件失败: ${indexPath}`, error);
      }
    }

    // 创建元数据（不包含详细数据）
    const metaData = { ...newData };
    if (type === 'irradiance') {
      // 对于光照数据，不在索引中包含详细数据
      delete metaData.data;
    } else if (type === 'projects') {
      // 对于项目数据，保留基本信息但不包含详细的小时数据
      if (metaData.analysisResults && metaData.analysisResults.hourlyData) {
        // 在索引中不包含小时数据，但保留其他分析结果
        const hourlyDataLength = metaData.analysisResults.hourlyData.length;
        console.log(`项目索引: 移除小时数据 (${hourlyDataLength} 条)`);
        metaData.analysisResults = {
          ...metaData.analysisResults,
          hourlyData: [] // 在索引中不保存小时数据
        };
      }
    }

    // 添加到索引
    index.push(metaData);

    // 保存索引
    await fs.writeFile(indexPath, JSON.stringify(index, null, 2), 'utf8');

    return newData;
  } catch (error) {
    console.error(`创建${type}数据失败`, error);
    throw new Error(`创建${type}数据失败`);
  }
};

/**
 * 更新数据
 * @param {string} type 数据类型
 * @param {string} id 数据ID
 * @param {Object} data 数据对象
 * @returns {Promise<Object>} 更新后的数据
 */
const update = async (type, id, data) => {
  try {
    // 获取现有数据
    const existingData = await getDetail(type, id);

    // 更新数据
    const updatedData = {
      ...existingData,
      ...data,
      id, // 确保ID不变
      updatedAt: new Date().toISOString()
    };

    // 保存数据文件
    const filePath = path.join(DATA_DIR, type, `${id}.json`);

    // 对于项目数据，限制小时数据的精度
    if (type === 'projects' && updatedData.analysisResults && updatedData.analysisResults.hourlyData) {
      // 创建一个深拷贝，避免修改原始对象
      const processedData = JSON.parse(JSON.stringify(updatedData));

      // 处理小时数据
      processedData.analysisResults.hourlyData = processedData.analysisResults.hourlyData.map(hour => {
        // 处理pvGeneration字段
        if (typeof hour.pvGeneration === 'object') {
          const processedPvGeneration = {};
          for (const moduleId in hour.pvGeneration) {
            const value = hour.pvGeneration[moduleId];
            processedPvGeneration[moduleId] = typeof value === 'number' ? parseFloat(value.toFixed(3)) : 0;
          }
          hour.pvGeneration = processedPvGeneration;
        }

        // 处理其他数值字段
        if (typeof hour.electricityConsumption === 'number') {
          hour.electricityConsumption = parseFloat(hour.electricityConsumption.toFixed(3));
        }
        if (typeof hour.storageCharge === 'number') {
          hour.storageCharge = parseFloat(hour.storageCharge.toFixed(3));
        }
        if (typeof hour.storageCapacity === 'number') {
          hour.storageCapacity = parseFloat(hour.storageCapacity.toFixed(3));
        }
        if (typeof hour.gridExport === 'number') {
          hour.gridExport = parseFloat(hour.gridExport.toFixed(3));
        }
        if (typeof hour.gridImport === 'number') {
          hour.gridImport = parseFloat(hour.gridImport.toFixed(3));
        }

        return hour;
      });

      // 保存处理后的数据
      await fs.writeFile(filePath, JSON.stringify(processedData, null, 2), 'utf8');
    } else {
      // 保存原始数据
      await fs.writeFile(filePath, JSON.stringify(updatedData, null, 2), 'utf8');
    }

    // 更新索引
    const indexPath = path.join(DATA_DIR, type, 'index.json');
    let index = [];

    try {
      const indexData = await fs.readFile(indexPath, 'utf8');
      index = JSON.parse(indexData);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error(`读取索引文件失败: ${indexPath}`, error);
      }
    }

    // 创建元数据（不包含详细数据）
    const metaData = { ...updatedData };
    if (type === 'irradiance') {
      // 对于光照数据，不在索引中包含详细数据
      delete metaData.data;
    } else if (type === 'projects') {
      // 对于项目数据，保留基本信息但不包含详细的小时数据
      if (metaData.analysisResults && metaData.analysisResults.hourlyData) {
        // 在索引中不包含小时数据，但保留其他分析结果
        const hourlyDataLength = metaData.analysisResults.hourlyData.length;
        console.log(`项目索引: 移除小时数据 (${hourlyDataLength} 条)`);
        metaData.analysisResults = {
          ...metaData.analysisResults,
          hourlyData: [] // 在索引中不保存小时数据
        };
      }
    }

    // 更新索引
    const indexIndex = index.findIndex(item => item.id === id);
    if (indexIndex !== -1) {
      index[indexIndex] = metaData;
    } else {
      index.push(metaData);
    }

    // 保存索引
    await fs.writeFile(indexPath, JSON.stringify(index, null, 2), 'utf8');

    return updatedData;
  } catch (error) {
    console.error(`更新${type}数据失败: ${id}`, error);
    throw new Error(`更新${type}数据失败`);
  }
};

/**
 * 删除数据
 * @param {string} type 数据类型
 * @param {string} id 数据ID
 * @returns {Promise<boolean>} 是否成功
 */
const remove = async (type, id) => {
  try {
    // 删除数据文件
    const filePath = path.join(DATA_DIR, type, `${id}.json`);
    await fs.unlink(filePath);

    // 更新索引
    const indexPath = path.join(DATA_DIR, type, 'index.json');
    let index = [];

    try {
      const indexData = await fs.readFile(indexPath, 'utf8');
      index = JSON.parse(indexData);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error(`读取索引文件失败: ${indexPath}`, error);
      }
    }

    // 从索引中移除
    const newIndex = index.filter(item => item.id !== id);

    // 保存索引
    await fs.writeFile(indexPath, JSON.stringify(newIndex, null, 2), 'utf8');

    return true;
  } catch (error) {
    if (error.code === 'ENOENT') {
      // 文件不存在，视为删除成功
      return true;
    }
    console.error(`删除${type}数据失败: ${id}`, error);
    throw new Error(`删除${type}数据失败`);
  }
};

module.exports = {
  getList,
  getDetail,
  create,
  update,
  remove
};
