/**
 * 服务器入口文件
 * 光伏+储能项目经济性分析系统 - 服务器端数据存储服务
 */
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');

// 导入路由
const irradianceRoutes = require('./routes/irradiance');
const electricityPriceRoutes = require('./routes/electricityPrice');
const supplierRoutes = require('./routes/supplier');
const equipmentRoutes = require('./routes/equipment');
const attachmentRoutes = require('./routes/attachment');
const projectRoutes = require('./routes/project');
const settingsRoutes = require('./routes/settings');
const versionRoutes = require('./routes/version');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3001;

// 确保数据目录存在
const dataDir = path.join(__dirname, '../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 确保上传目录存在
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 中间件
app.use(cors()); // 允许跨域请求
app.use(morgan('dev')); // 日志记录
app.use(express.json({ limit: '50mb' })); // 解析JSON请求体，增加限制以处理大型数据
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 解析URL编码的请求体

// 路由
app.use('/api/irradiance', irradianceRoutes);
app.use('/api/electricity-prices', electricityPriceRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/equipment', equipmentRoutes);
app.use('/api/attachments', attachmentRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/version', versionRoutes);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '光伏+储能项目经济性分析系统 - 服务器端数据存储服务',
    status: 'running',
    endpoints: [
      '/api/irradiance',
      '/api/electricity-prices',
      '/api/suppliers',
      '/api/equipment',
      '/api/attachments',
      '/api/projects',
      '/api/settings',
      '/api/version',
      '/api/health'
    ]
  });
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    message: '服务器正常运行'
  });
});

// 服务器重启端点
app.post('/api/system/restart', (req, res) => {
  console.log('收到重启请求...');

  // 立即返回响应
  res.json({
    success: true,
    message: '正在重启服务器...',
    timestamp: new Date().toISOString()
  });

  // 延迟执行重启命令，确保响应已发送
  setTimeout(() => {
    console.log('执行服务器重启...');

    // 执行重启脚本
    const { spawn } = require('child_process');
    const scriptPath = path.join(__dirname, '../../start_server.sh');

    // 检查脚本是否存在
    if (fs.existsSync(scriptPath)) {
      console.log('执行重启脚本:', scriptPath);

      // 使用spawn执行重启脚本
      const restart = spawn('bash', [scriptPath], {
        detached: true,
        stdio: 'ignore',
        cwd: path.join(__dirname, '../../')
      });

      restart.unref();

      // 等待一秒后退出当前进程
      setTimeout(() => {
        console.log('退出当前服务器进程...');
        process.exit(0);
      }, 1000);
    } else {
      console.error('重启脚本不存在:', scriptPath);
    }
  }, 500);
});

// 系统状态端点
app.get('/api/system/status', (req, res) => {
  const { exec } = require('child_process');

  // 检查监控进程状态
  exec('ps aux | grep -E "(hostinger_monitor|monitor_server)" | grep -v grep', (error, stdout, stderr) => {
    const monitorRunning = stdout.trim().length > 0;

    res.json({
      success: true,
      data: {
        server: {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          pid: process.pid
        },
        monitor: {
          status: monitorRunning ? 'running' : 'stopped',
          processes: stdout.trim().split('\n').filter(line => line.trim().length > 0)
        },
        timestamp: new Date().toISOString()
      }
    });
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: err.message || '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器已启动，监听端口: ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});
