/**
 * 项目路由
 * 处理项目相关的API请求
 */
const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');

// 数据类型
const DATA_TYPE = 'projects';

/**
 * 获取项目列表
 * GET /api/projects
 */
router.get('/', async (req, res) => {
  try {
    console.log('收到获取项目列表请求', new Date().toISOString());
    const { page = 1, pageSize = 10 } = req.query;
    console.log('请求参数:', { page, pageSize });

    // 获取数据列表
    console.log('调用dataService.getList...');
    const data = await dataService.getList(DATA_TYPE, {
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

    console.log('获取项目列表成功, 项目数量:', Array.isArray(data) ? data.length : '未知');

    // 检查返回的数据结构
    if (Array.isArray(data)) {
      console.log('项目列表是数组, 包含', data.length, '个项目');
      // 检查第一个项目的结构
      if (data.length > 0) {
        console.log('第一个项目ID:', data[0].id);
        console.log('第一个项目名称:', data[0].name);
      }
    } else {
      console.warn('项目列表不是数组:', typeof data);
    }

    // 构造响应
    const items = Array.isArray(data) ? data : [];

    // 检查每个项目的数据结构
    const processedItems = items.map(item => {
      // 确保基本字段存在
      const processedItem = {
        ...item,
        id: item.id || '',
        name: item.name || '',
        location: item.location || '',
        capacity: item.capacity || 0,
        status: item.status || 'draft',
        createdAt: item.createdAt || new Date().toISOString(),
        updatedAt: item.updatedAt || new Date().toISOString(),
        syncStatus: item.syncStatus || 'synced'
      };

      // 确保分析结果字段存在
      if (!processedItem.analysisResults) {
        processedItem.analysisResults = {
          hourlyData: [],
          analysisCompleted: false,
          analysisDate: ''
        };
      }

      return processedItem;
    });

    const response = {
      success: true,
      data: {
        items: processedItems,
        total: processedItems.length
      }
    };

    console.log('发送响应, 数据条数:', response.data.items.length);
    res.json(response);
  } catch (error) {
    console.error('获取项目列表失败:', error);
    console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    res.status(500).json({
      success: false,
      message: error.message || '获取项目列表失败'
    });
  }
});

/**
 * 获取项目详情
 * GET /api/projects/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`获取项目详情: ${id}`, new Date().toISOString());

    // 获取数据详情
    const data = await dataService.getDetail(DATA_TYPE, id);
    console.log(`获取到项目详情, ID: ${id}, 名称: ${data.name || '未命名'}`);

    // 检查项目数据结构
    if (!data.analysisResults) {
      console.log(`项目 ${id} 没有分析结果，创建空的分析结果`);
      data.analysisResults = {
        hourlyData: [],
        analysisCompleted: false,
        analysisDate: ''
      };
    } else {
      // 检查小时数据
      if (data.analysisResults.hourlyData) {
        console.log(`项目 ${id} 有小时数据，数量: ${data.analysisResults.hourlyData.length}`);

        // 检查第一个小时数据点
        if (data.analysisResults.hourlyData.length > 0) {
          const firstHourData = data.analysisResults.hourlyData[0];
          console.log(`第一个小时数据点: ${JSON.stringify(firstHourData)}`);

          // 检查electricityConsumption字段
          if (firstHourData.electricityConsumption !== undefined) {
            console.log(`electricityConsumption类型: ${typeof firstHourData.electricityConsumption}`);
            if (typeof firstHourData.electricityConsumption === 'object') {
              console.log(`electricityConsumption对象: ${JSON.stringify(firstHourData.electricityConsumption)}`);
            }
          } else {
            console.log(`electricityConsumption字段不存在`);
          }

          // 检查pvGeneration字段
          if (firstHourData.pvGeneration) {
            console.log(`pvGeneration类型: ${typeof firstHourData.pvGeneration}`);
            if (typeof firstHourData.pvGeneration === 'object') {
              console.log(`pvGeneration是对象，包含 ${Object.keys(firstHourData.pvGeneration).length} 个组件`);
            }
          } else {
            console.log(`pvGeneration字段不存在或为空`);
          }
        }
      } else {
        console.log(`项目 ${id} 没有小时数据`);
      }
    }

    // 确保返回的数据结构正确
    const processedData = {
      ...data,
      // 确保基本字段存在
      id: data.id || '',
      name: data.name || '未命名项目',
      location: data.location || '',
      capacity: typeof data.capacity === 'number' ? data.capacity : 0,
      status: data.status || 'draft',
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: data.updatedAt || new Date().toISOString(),
      syncStatus: data.syncStatus || 'synced'
    };

    // 确保分析结果字段存在
    if (!processedData.analysisResults) {
      processedData.analysisResults = {
        hourlyData: [],
        analysisCompleted: false,
        analysisDate: ''
      };
    }

    console.log('发送处理后的项目详情数据');
    res.json({
      success: true,
      data: processedData
    });
  } catch (error) {
    console.error(`获取项目详情失败: ${req.params.id}`, error);
    console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    res.status(404).json({
      success: false,
      message: error.message || '获取项目详情失败'
    });
  }
});

/**
 * 创建项目
 * POST /api/projects
 */
router.post('/', async (req, res) => {
  try {
    const data = req.body;

    // 创建数据
    const createdData = await dataService.create(DATA_TYPE, data);

    res.status(201).json({
      success: true,
      data: createdData
    });
  } catch (error) {
    console.error('创建项目失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '创建项目失败'
    });
  }
});

/**
 * 更新项目
 * PUT /api/projects/:id
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const data = req.body;

    // 更新数据
    const updatedData = await dataService.update(DATA_TYPE, id, data);

    res.json({
      success: true,
      data: updatedData
    });
  } catch (error) {
    console.error(`更新项目失败: ${req.params.id}`, error);
    res.status(500).json({
      success: false,
      message: error.message || '更新项目失败'
    });
  }
});

/**
 * 删除项目
 * DELETE /api/projects/:id
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 删除数据
    await dataService.remove(DATA_TYPE, id);

    res.json({
      success: true,
      message: '项目删除成功'
    });
  } catch (error) {
    console.error(`删除项目失败: ${req.params.id}`, error);
    res.status(500).json({
      success: false,
      message: error.message || '删除项目失败'
    });
  }
});

/**
 * 开始项目分析
 * POST /api/projects/:id/analyze
 */
router.post('/:id/analyze', async (req, res) => {
  try {
    const { id } = req.params;

    // 获取项目数据
    const projectData = await dataService.getDetail(DATA_TYPE, id);

    // 更新项目状态为分析中
    projectData.status = 'analyzing';
    projectData.updatedAt = new Date().toISOString();

    // 保存更新后的项目数据
    await dataService.update(DATA_TYPE, id, projectData);

    // 这里可以添加实际的分析逻辑，或者启动一个异步任务

    res.json({
      success: true,
      message: '项目分析已开始'
    });
  } catch (error) {
    console.error(`开始项目分析失败: ${req.params.id}`, error);
    res.status(500).json({
      success: false,
      message: error.message || '开始项目分析失败'
    });
  }
});

/**
 * 获取项目分析结果
 * GET /api/projects/:id/results
 */
router.get('/:id/results', async (req, res) => {
  try {
    const { id } = req.params;

    // 获取项目数据
    const projectData = await dataService.getDetail(DATA_TYPE, id);

    // 检查是否有分析结果
    if (!projectData.analysisResults) {
      return res.status(404).json({
        success: false,
        message: '项目分析结果不存在'
      });
    }

    res.json({
      success: true,
      data: projectData.analysisResults
    });
  } catch (error) {
    console.error(`获取项目分析结果失败: ${req.params.id}`, error);
    res.status(500).json({
      success: false,
      message: error.message || '获取项目分析结果失败'
    });
  }
});

/**
 * 获取项目小时数据
 * GET /api/projects/:id/hourly-data
 */
router.get('/:id/hourly-data', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`获取项目小时数据: ${id}`, new Date().toISOString());

    // 获取项目数据
    const projectData = await dataService.getDetail(DATA_TYPE, id);

    // 检查是否有分析结果和小时数据
    if (!projectData.analysisResults || !projectData.analysisResults.hourlyData) {
      console.log(`项目 ${id} 没有分析结果或小时数据`);
      return res.status(404).json({
        success: false,
        message: '项目小时数据不存在'
      });
    }

    const hourlyData = projectData.analysisResults.hourlyData;
    console.log(`获取到项目 ${id} 的小时数据，数量: ${hourlyData.length}`);

    // 检查第一个小时数据点
    if (hourlyData.length > 0) {
      const firstHourData = hourlyData[0];
      console.log(`第一个小时数据点: ${JSON.stringify(firstHourData)}`);
    }

    res.json({
      success: true,
      data: hourlyData
    });
  } catch (error) {
    console.error(`获取项目小时数据失败: ${req.params.id}`, error);
    res.status(500).json({
      success: false,
      message: error.message || '获取项目小时数据失败'
    });
  }
});

module.exports = router;
