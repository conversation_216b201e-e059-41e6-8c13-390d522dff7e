const express = require('express');
const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const router = express.Router();

// 项目根目录
const PROJECT_ROOT = path.resolve(__dirname, '../../../');
const PACKAGE_JSON_PATH = path.join(PROJECT_ROOT, 'package.json');
const UPDATE_STATUS_FILE = path.join(PROJECT_ROOT, 'server/data/update_status.json');

// 更新状态存储
let updateStatus = {
  status: 'idle', // idle, checking, downloading, installing, success, error
  progress: 0,
  message: '就绪',
  error: null
};

// 加载更新状态
async function loadUpdateStatus() {
  try {
    const data = await fs.readFile(UPDATE_STATUS_FILE, 'utf8');
    updateStatus = JSON.parse(data);
  } catch (error) {
    // 文件不存在或读取失败，使用默认状态
    updateStatus = {
      status: 'idle',
      progress: 0,
      message: '就绪',
      error: null
    };
  }
}

// 保存更新状态
async function saveUpdateStatus() {
  try {
    // 确保目录存在
    const dir = path.dirname(UPDATE_STATUS_FILE);
    await fs.mkdir(dir, { recursive: true });

    await fs.writeFile(UPDATE_STATUS_FILE, JSON.stringify(updateStatus, null, 2));
  } catch (error) {
    console.error('保存更新状态失败:', error);
  }
}

// 初始化时加载状态
loadUpdateStatus();

/**
 * 获取当前版本信息
 */
router.get('/info', async (req, res) => {
  try {
    // 读取package.json获取当前版本
    const packageData = await fs.readFile(PACKAGE_JSON_PATH, 'utf8');
    const packageJson = JSON.parse(packageData);

    const versionInfo = {
      current: packageJson.version || '1.0.0',
      latest: packageJson.version || '1.0.0', // 初始时设为相同
      hasUpdate: false,
      releaseDate: '2023-12-01',
      changelog: [
        '修复了已知问题',
        '优化了用户界面',
        '提升了系统性能'
      ]
    };

    res.json({
      success: true,
      data: versionInfo
    });
  } catch (error) {
    console.error('获取版本信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取版本信息失败',
      error: error.message
    });
  }
});

/**
 * 检查更新
 */
router.get('/check', async (req, res) => {
  try {
    updateStatus = {
      status: 'checking',
      progress: 10,
      message: '正在检查更新...',
      error: null
    };

    // 获取当前版本
    const currentVersion = await getCurrentVersion();
    console.log('当前版本:', currentVersion);

    // 尝试获取远程最新版本
    let latestVersion;
    let githubConnected = true;
    let errorMessage = null;

    try {
      latestVersion = await getLatestVersion();
      console.log('GitHub最新版本:', latestVersion);
    } catch (error) {
      console.error('无法连接到GitHub:', error.message);
      githubConnected = false;
      latestVersion = currentVersion;
      errorMessage = '无法连接到项目代码仓库，请检查网络连接';
    }

    const hasUpdate = githubConnected && compareVersions(latestVersion, currentVersion) > 0;

    const versionInfo = {
      current: currentVersion,
      latest: latestVersion,
      hasUpdate,
      releaseDate: new Date().toISOString().split('T')[0],
      changelog: hasUpdate ? [
        '更新关于信息和修复logo显示问题',
        '更新技术栈信息',
        '修复版本控制功能',
        '优化部署流程'
      ] : []
    };

    let statusMessage;
    if (!githubConnected) {
      statusMessage = errorMessage;
    } else if (hasUpdate) {
      statusMessage = '发现新版本';
    } else {
      statusMessage = '已是最新版本';
    }

    updateStatus = {
      status: githubConnected ? 'idle' : 'error',
      progress: 0,
      message: statusMessage,
      error: githubConnected ? null : errorMessage
    };

    res.json({
      success: true,
      data: versionInfo
    });
  } catch (error) {
    console.error('检查更新失败:', error);
    updateStatus = {
      status: 'error',
      progress: 0,
      message: '检查更新失败',
      error: error.message
    };

    res.status(500).json({
      success: false,
      message: '检查更新失败',
      error: error.message
    });
  }
});

/**
 * 执行更新
 */
router.post('/update', async (req, res) => {
  try {
    updateStatus = {
      status: 'downloading',
      progress: 0,
      message: '开始更新...',
      error: null
    };

    res.json({
      success: true,
      data: updateStatus
    });

    // 异步执行更新
    performUpdate();
  } catch (error) {
    console.error('启动更新失败:', error);
    updateStatus = {
      status: 'error',
      progress: 0,
      message: '启动更新失败',
      error: error.message
    };

    res.status(500).json({
      success: false,
      message: '启动更新失败',
      error: error.message
    });
  }
});

/**
 * 获取更新状态
 */
router.get('/status', (req, res) => {
  res.json({
    success: true,
    data: updateStatus
  });
});

/**
 * 重启应用
 */
router.post('/restart', (req, res) => {
  res.json({
    success: true,
    message: '应用正在重启...'
  });

  // 延迟重启，给客户端时间接收响应
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

/**
 * 获取更新日志
 */
router.get('/changelog/:version', async (req, res) => {
  try {
    const { version } = req.params;

    // 这里可以从GitHub API或其他源获取更新日志
    const changelog = [
      `版本 ${version} 更新内容:`,
      '• 新增功能A',
      '• 修复问题B',
      '• 优化性能C'
    ];

    res.json({
      success: true,
      data: changelog
    });
  } catch (error) {
    console.error('获取更新日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取更新日志失败',
      error: error.message
    });
  }
});

/**
 * 获取当前版本
 */
async function getCurrentVersion() {
  try {
    // 检查是否在Hostinger环境
    const isHostinger = process.env.NODE_ENV === 'production' ||
                       process.cwd().includes('/domains/pv-analysis.top/');

    let packagePath;
    if (isHostinger) {
      // Hostinger环境
      packagePath = '/home/<USER>/domains/pv-analysis.top/public_html/package.json';
    } else {
      // 本地开发环境
      packagePath = PACKAGE_JSON_PATH;
    }

    console.log('尝试读取package.json:', packagePath);
    const packageData = await fs.readFile(packagePath, 'utf8');
    const packageJson = JSON.parse(packageData);
    const version = packageJson.version || '1.0.0';
    console.log('当前版本:', version);
    return version;
  } catch (error) {
    console.error('读取package.json失败:', error.message);
    return '1.0.0';
  }
}

/**
 * 获取最新版本（从GitHub）
 */
async function getLatestVersion() {
  return new Promise((resolve, reject) => {
    // 首先检查是否在Hostinger环境
    const isHostinger = process.env.NODE_ENV === 'production' ||
                       process.cwd().includes('/domains/pv-analysis.top/');

    let gitCommand;
    if (isHostinger) {
      // Hostinger环境
      gitCommand = 'cd /home/<USER>/domains/pv-analysis.top/public_html && git ls-remote --tags origin 2>/dev/null | grep "refs/tags/v" | tail -1 | cut -d/ -f3';
    } else {
      // 本地开发环境
      gitCommand = `cd ${PROJECT_ROOT} && git ls-remote --tags origin 2>/dev/null | grep "refs/tags/v" | tail -1 | cut -d/ -f3`;
    }

    exec(gitCommand, { timeout: 10000 }, (error, stdout, stderr) => {
      if (error) {
        console.error('无法连接到GitHub仓库:', error.message);
        // 如果无法连接GitHub，抛出错误
        reject(new Error('无法连接到项目代码仓库'));
        return;
      }

      const latestTag = stdout.trim();
      if (latestTag && latestTag.startsWith('v')) {
        // 移除v前缀
        const version = latestTag.replace(/^v/, '');
        console.log('从GitHub获取到最新版本:', version);
        resolve(version);
      } else {
        console.log('未找到有效的版本标签');
        reject(new Error('未找到有效的版本标签'));
      }
    });
  });
}

/**
 * 比较版本号
 */
function compareVersions(version1, version2) {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
    const v1part = v1parts[i] || 0;
    const v2part = v2parts[i] || 0;

    if (v1part > v2part) return 1;
    if (v1part < v2part) return -1;
  }

  return 0;
}

/**
 * 执行更新
 */
async function performUpdate() {
  try {
    updateStatus = {
      status: 'downloading',
      progress: 20,
      message: '正在下载最新代码...',
      error: null
    };
    await saveUpdateStatus();

    console.log('开始执行更新脚本...');

    // 执行更新脚本
    const isHostinger = process.env.NODE_ENV === 'production' ||
                       process.cwd().includes('/domains/pv-analysis.top/');

    const updateScript = isHostinger
      ? '/home/<USER>/domains/pv-analysis.top/public_html/update_hostinger.sh'
      : '/home/<USER>/server_update.sh';

    exec(`bash ${updateScript}`, { timeout: 300000 }, async (error, stdout, stderr) => {
      console.log('更新脚本执行完成');
      console.log('stdout:', stdout);
      console.log('stderr:', stderr);

      if (error) {
        console.error('更新失败:', error);
        updateStatus = {
          status: 'error',
          progress: 0,
          message: '更新失败',
          error: error.message
        };
        await saveUpdateStatus();
        return;
      }

      console.log('更新成功完成');
      updateStatus = {
        status: 'success',
        progress: 100,
        message: '更新完成',
        error: null
      };
      await saveUpdateStatus();
    });

    // 模拟进度更新
    const progressInterval = setInterval(async () => {
      if (updateStatus.status === 'downloading' && updateStatus.progress < 80) {
        updateStatus.progress += 10;
        updateStatus.message = `正在更新... ${updateStatus.progress}%`;
        console.log(`更新进度: ${updateStatus.progress}%`);
        await saveUpdateStatus();
      } else if (updateStatus.status === 'success' || updateStatus.status === 'error') {
        console.log('清除进度更新定时器');
        clearInterval(progressInterval);
      }
    }, 2000);

  } catch (error) {
    console.error('执行更新失败:', error);
    updateStatus = {
      status: 'error',
      progress: 0,
      message: '执行更新失败',
      error: error.message
    };
    await saveUpdateStatus();
  }
}

module.exports = router;
