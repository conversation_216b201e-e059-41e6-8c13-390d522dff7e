/**
 * 系统设置路由
 * 用于处理系统设置的保存和获取
 */
const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const dataService = require('../services/dataService');

// 数据类型
const DATA_TYPE = 'settings';

// 确保上传目录存在
const UPLOAD_DIR = path.join(__dirname, '../../uploads/settings');
fs.mkdir(UPLOAD_DIR, { recursive: true }).catch(err => {
  console.error('创建设置上传目录失败:', err);
});

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_DIR);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名，保留原始扩展名
    const originalExt = path.extname(file.originalname);
    const uniqueFilename = `logo-${Date.now()}-${uuidv4()}${originalExt}`;
    cb(null, uniqueFilename);
  }
});

// 自定义文件过滤器，只允许图片文件
const fileFilter = (req, file, cb) => {
  // 只允许图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 } // 限制文件大小为5MB
});

/**
 * 获取系统设置
 * GET /api/settings
 */
router.get('/', async (req, res, next) => {
  try {
    // 尝试获取设置
    let settings;
    try {
      settings = await dataService.getDetail(DATA_TYPE, 'global');
    } catch (error) {
      // 如果设置不存在，创建默认设置
      if (error.message.includes('不存在')) {
        const defaultSettings = {
          id: 'global',
          language: 'zh',
          currency: 'JPY',
          timezone: 'Asia/Tokyo',
          theme: 'light',
          siteName: '光储投资分析',
          siteLogo: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        settings = await dataService.create(DATA_TYPE, defaultSettings);
      } else {
        throw error;
      }
    }

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新系统设置
 * PUT /api/settings
 */
router.put('/', async (req, res, next) => {
  try {
    const { language, currency, timezone, theme, siteName } = req.body;
    
    // 更新设置
    const updatedSettings = await dataService.update(DATA_TYPE, 'global', {
      language,
      currency,
      timezone,
      theme,
      siteName,
      updatedAt: new Date().toISOString()
    });

    res.json({
      success: true,
      data: updatedSettings
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 上传网站Logo
 * POST /api/settings/upload-logo
 */
router.post('/upload-logo', upload.single('logo'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未上传文件'
      });
    }

    // 构建文件URL (相对路径)
    const fileUrl = `/uploads/settings/${req.file.filename}`;

    // 更新设置中的logo URL
    let settings;
    try {
      settings = await dataService.getDetail(DATA_TYPE, 'global');
    } catch (error) {
      // 如果设置不存在，创建默认设置
      if (error.message.includes('不存在')) {
        const defaultSettings = {
          id: 'global',
          language: 'zh',
          currency: 'JPY',
          timezone: 'Asia/Tokyo',
          theme: 'light',
          siteName: '光储投资分析',
          siteLogo: fileUrl,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        settings = await dataService.create(DATA_TYPE, defaultSettings);
      } else {
        throw error;
      }
    }

    // 如果设置已存在，更新logo URL
    if (settings) {
      // 如果有旧的logo，尝试删除
      if (settings.siteLogo && settings.siteLogo.startsWith('/uploads/settings/')) {
        try {
          const oldLogoPath = path.join(__dirname, '../..', settings.siteLogo);
          await fs.unlink(oldLogoPath);
        } catch (error) {
          console.error('删除旧logo失败:', error);
          // 继续执行，不影响更新
        }
      }

      // 更新设置
      settings = await dataService.update(DATA_TYPE, 'global', {
        siteLogo: fileUrl,
        updatedAt: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: {
        siteLogo: fileUrl
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
