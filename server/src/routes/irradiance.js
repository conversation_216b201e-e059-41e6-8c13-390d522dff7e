/**
 * 光照数据路由
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const dataService = require('../services/dataService');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../uploads'));
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 限制文件大小为50MB
});

// 数据类型
const DATA_TYPE = 'irradiance';

/**
 * 获取光照数据列表
 * GET /api/irradiance
 */
router.get('/', async (req, res, next) => {
  try {
    const data = await dataService.getList(DATA_TYPE);

    // 分页处理
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = page * pageSize;

    const paginatedData = data.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        items: paginatedData,
        total: data.length
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取光照数据详情
 * GET /api/irradiance/:id
 */
router.get('/:id', async (req, res, next) => {
  try {
    const data = await dataService.getDetail(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 创建光照数据
 * POST /api/irradiance
 */
router.post('/', async (req, res, next) => {
  try {
    const data = await dataService.create(DATA_TYPE, req.body);
    res.status(201).json({
      success: true,
      data
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新光照数据
 * PUT /api/irradiance/:id
 */
router.put('/:id', async (req, res, next) => {
  try {
    const data = await dataService.update(DATA_TYPE, req.params.id, req.body);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 验证光照数据有效性
 * GET /api/irradiance/:id/validate
 */
router.get('/:id/validate', async (req, res, next) => {
  try {
    const data = await dataService.getDetail(DATA_TYPE, req.params.id);

    // 验证数据有效性
    const isValid = data && data.data && Array.isArray(data.data) && data.data.length > 0;

    if (isValid) {
      res.json({
        success: true,
        data,
        message: '数据有效'
      });
    } else {
      res.json({
        success: false,
        data: null,
        message: '数据无效或不完整'
      });
    }
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 删除光照数据
 * DELETE /api/irradiance/:id
 */
router.delete('/:id', async (req, res, next) => {
  try {
    await dataService.remove(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      message: '光照数据删除成功'
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      // 如果数据不存在，也返回成功
      return res.json({
        success: true,
        message: '光照数据不存在或已被删除'
      });
    }
    next(error);
  }
});

/**
 * 上传光照数据CSV文件
 * POST /api/irradiance/upload
 */
router.post('/upload', upload.single('file'), (req, res) => {
  // 这里只处理文件上传，实际的CSV解析和数据创建由前端处理
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '未上传文件'
    });
  }

  res.json({
    success: true,
    data: {
      filename: req.file.filename,
      path: req.file.path,
      size: req.file.size
    }
  });
});

module.exports = router;
