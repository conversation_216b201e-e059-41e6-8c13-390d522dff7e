/**
 * 附件路由
 * 用于处理设备附件的上传、下载和删除
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const dataService = require('../services/dataService');

// 数据类型
const DATA_TYPE = 'attachment';

// 确保上传目录存在
const UPLOAD_DIR = path.join(__dirname, '../../uploads/attachments');
fs.mkdir(UPLOAD_DIR, { recursive: true }).catch(err => {
  console.error('创建上传目录失败:', err);
});

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, UPLOAD_DIR);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名，保留原始扩展名
    const originalExt = path.extname(file.originalname);
    const uniqueFilename = `${Date.now()}-${uuidv4()}${originalExt}`;
    cb(null, uniqueFilename);
  }
});

// 自定义文件过滤器，处理文件名编码
const fileFilter = (req, file, cb) => {
  try {
    // 解决文件名编码问题
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    file.originalname = originalName;
    cb(null, true);
  } catch (error) {
    console.error('文件名编码处理失败:', error);
    cb(null, true); // 即使处理失败也允许上传
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 50 * 1024 * 1024 } // 限制文件大小为50MB
});

/**
 * 获取附件列表
 * GET /api/attachments?equipmentId=xxx
 */
router.get('/', async (req, res, next) => {
  try {
    const { equipmentId } = req.query;

    if (!equipmentId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID参数'
      });
    }

    // 获取设备详情
    try {
      const equipment = await dataService.getDetail('equipment', equipmentId);
      const attachments = equipment.attachments || [];

      res.json({
        success: true,
        data: attachments
      });
    } catch (error) {
      // 如果设备不存在，返回空数组
      res.json({
        success: true,
        data: []
      });
    }
  } catch (error) {
    next(error);
  }
});

/**
 * 获取附件详情
 * GET /api/attachments/:id
 */
router.get('/:id', async (req, res, next) => {
  try {
    const data = await dataService.getDetail(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 上传附件
 * POST /api/attachments/upload
 */
router.post('/upload', upload.single('file'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未上传文件'
      });
    }

    const { equipmentId, type = 'other' } = req.body;

    if (!equipmentId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID参数'
      });
    }

    // 创建附件记录
    const id = uuidv4();
    const now = new Date().toISOString();

    // 构建文件URL (相对路径)
    const fileUrl = `/uploads/attachments/${req.file.filename}`;

    // 创建附件对象
    const attachment = {
      id,
      name: req.file.originalname,
      type,
      url: fileUrl,
      fileSize: req.file.size,
      fileType: req.file.mimetype,
      uploadedAt: now,
      equipmentId
    };

    // 保存附件记录
    await dataService.create(DATA_TYPE, attachment);

    // 如果设备ID不是临时ID，则关联到设备
    if (!equipmentId.startsWith('temp_')) {
      try {
        // 获取设备详情
        const equipment = await dataService.getDetail('equipment', equipmentId);

        // 添加附件到设备
        const attachments = equipment.attachments || [];
        attachments.push(attachment);

        // 更新设备
        await dataService.update('equipment', equipmentId, {
          attachments,
          updatedAt: now
        });
      } catch (error) {
        console.error(`关联附件到设备失败: ${error.message}`);
        // 即使关联失败，也返回成功，因为附件已经上传成功
      }
    }

    res.json({
      success: true,
      data: attachment
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 删除附件
 * DELETE /api/attachments/:id
 */
router.delete('/:id', async (req, res, next) => {
  try {
    // 获取附件详情
    const attachment = await dataService.getDetail(DATA_TYPE, req.params.id);

    // 删除文件
    const filePath = path.join(__dirname, '../..', attachment.url);
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.error(`删除文件失败: ${filePath}`, error);
      // 即使文件删除失败，也继续删除记录
    }

    // 删除附件记录
    await dataService.remove(DATA_TYPE, req.params.id);

    // 如果有设备ID，则从设备中移除附件
    if (attachment.equipmentId) {
      try {
        // 获取设备详情
        const equipment = await dataService.getDetail('equipment', attachment.equipmentId);

        // 移除附件
        const attachments = (equipment.attachments || []).filter(a => a.id !== req.params.id);

        // 更新设备
        await dataService.update('equipment', attachment.equipmentId, {
          attachments,
          updatedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error(`从设备中移除附件失败: ${error.message}`);
        // 即使关联失败，也返回成功，因为附件已经删除成功
      }
    }

    res.json({
      success: true,
      message: '附件删除成功'
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      // 如果附件不存在，也返回成功
      return res.json({
        success: true,
        message: '附件不存在或已被删除'
      });
    }
    next(error);
  }
});

module.exports = router;
