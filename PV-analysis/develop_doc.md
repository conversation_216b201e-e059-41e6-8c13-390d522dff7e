# 光伏+储能项目经济性分析应用开发文档

## 项目概述

本文档详细描述了光伏+储能项目经济性分析应用的开发计划、技术架构、组件设计和开发进度管理。该应用是一个基于React的跨平台网页应用，用于分析日本光伏+储能项目的经济可行性。

## 技术栈选择

### 前端框架与库

- **React**: 用于构建用户界面的JavaScript库
- **Vite**: 现代前端构建工具，提供更快的开发体验
- **React Router**: 用于应用路由管理
- **Redux Toolkit**: 状态管理库，简化Redux的使用
- **Ant Design**: UI组件库，提供丰富的预设计组件
- **Tailwind CSS**: 实用优先的CSS框架，用于快速构建自定义设计
- **Echarts/Recharts**: 数据可视化图表库
- **Axios**: 用于HTTP请求
- **i18next**: 国际化支持
- **dayjs**: 轻量级日期处理库
- **xlsx**: Excel文件处理库，用于导入/导出数据

### 开发工具

- **TypeScript**: 静态类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Jest**: 单元测试框架
- **React Testing Library**: React组件测试
- **Husky**: Git钩子工具，用于提交前代码检查

## 项目结构

```
src/
├── assets/            # 静态资源（图片、字体等）
├── components/        # 共享组件
│   ├── common/        # 通用组件（按钮、输入框等）
│   ├── layout/        # 布局组件
│   ├── charts/        # 图表组件
│   └── forms/         # 表单组件
├── config/            # 配置文件
├── hooks/             # 自定义钩子
├── locales/           # 国际化文件
├── pages/             # 页面组件
│   ├── auth/          # 认证相关页面
│   ├── projects/      # 项目库相关页面
│   ├── databases/     # 基础数据库相关页面
│   └── settings/      # 系统设置相关页面
├── services/          # API服务
├── store/             # Redux状态管理
│   ├── slices/        # Redux切片
│   └── index.js       # Store配置
├── styles/            # 全局样式
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
├── App.tsx            # 应用入口组件
├── main.tsx           # 应用入口文件
└── vite-env.d.ts      # Vite环境类型定义
```

## 组件设计规范

### 通用组件设计

1. **按钮组件 (Button)**
   - 支持多种类型：主要、次要、危险、链接等
   - 支持不同尺寸：大、中、小
   - 支持图标和文字组合
   - 支持加载状态

2. **表单组件 (Form)**
   - 支持多种输入类型：文本、数字、日期、选择器等
   - 支持表单验证
   - 支持动态表单项
   - 支持表单布局配置

3. **表格组件 (Table)**
   - 支持分页、排序、筛选
   - 支持自定义列渲染
   - 支持行选择和批量操作
   - 支持数据导出

4. **图表组件 (Chart)**
   - 支持多种图表类型：折线图、柱状图、饼图等
   - 支持交互式图表
   - 支持自定义主题和样式
   - 支持数据导出

5. **步骤组件 (Steps)**
   - 支持垂直和水平布局
   - 支持自定义图标和内容
   - 支持步骤状态：等待、进行中、完成、错误

### 页面组件设计

1. **布局组件 (Layout)**
   - 顶部导航栏
   - 侧边菜单
   - 内容区域
   - 页脚

2. **认证页面 (Auth)**
   - 登录页面
   - 注册页面
   - 忘记密码页面

3. **项目库页面 (Projects)**
   - 项目列表页面
   - 项目详情页面
   - 新建项目向导页面
   - 项目分析结果页面

4. **基础数据库页面 (Databases)**
   - 光照数据库页面
   - 电价政策数据库页面
   - 供应商数据库页面
   - 设备数据库页面

5. **系统设置页面 (Settings)**
   - 常规设置页面
   - 账户管理页面
   - 通知设置页面
   - 备份设置页面
   - 关于页面

## 数据模型设计

### 项目数据模型

```typescript
interface Project {
  id: string;
  name: string;
  location: string;
  capacity: number;
  status: 'draft' | 'analyzing' | 'completed';
  createdAt: string;
  updatedAt: string;
  basicInfo: {
    // 项目基础信息
  };
  irradianceData: {
    // 光照数据
  };
  electricityPrice: {
    // 电价政策
  };
  electricityUsage: {
    // 用电数据
  };
  pvModules: {
    // 光伏设备
  };
  energyStorage: {
    // 储能设备
  };
  inverters: {
    // 逆变器设备
  };
  otherInvestments: {
    // 其他投资项
  };
  analysisResults?: {
    // 分析结果
  };
}
```

### 数据库模型

```typescript
interface IrradianceData {
  id: string;
  name: string;
  location: string;
  data: Array<{ hour: number; day: number; month: number; value: number }>;
}

interface ElectricityPrice {
  id: string;
  name: string;
  region: string;
  rules: Array<{
    startTime: string;
    endTime: string;
    price: number;
    type: 'peak' | 'normal' | 'valley';
  }>;
}

interface Supplier {
  id: string;
  name: string;
  contact: string;
  address: string;
  products: string[];
}

interface Equipment {
  id: string;
  name: string;
  type: 'pv' | 'storage' | 'inverter' | 'other';
  manufacturer: string;
  model: string;
  specs: Record<string, any>;
  price: number;
}
```

## 开发进度管理

### 阶段一：项目初始化与基础架构（2周）

- [x] 项目初始化与配置
- [ ] 基础组件库开发
- [ ] 路由系统搭建
- [ ] 状态管理系统搭建
- [ ] 国际化支持实现
- [ ] 主题与样式系统搭建

### 阶段二：核心功能开发（4周）

- [ ] 认证系统实现
- [ ] 项目库模块开发
  - [ ] 项目列表页面
  - [ ] 项目详情页面
  - [ ] 新建项目向导（步骤1-4）
  - [ ] 新建项目向导（步骤5-8）
- [ ] 基础数据库模块开发
  - [ ] 光照数据库页面
  - [ ] 电价政策数据库页面
  - [ ] 供应商数据库页面
  - [ ] 设备数据库页面

### 阶段三：高级功能与分析模块（3周）

- [ ] 项目分析算法实现
- [ ] 分析结果可视化
- [ ] 数据导入/导出功能
- [ ] 系统设置模块开发
  - [ ] 常规设置页面
  - [ ] 账户管理页面
  - [ ] 通知设置页面
  - [ ] 备份设置页面
  - [ ] 关于页面

### 阶段四：测试与优化（2周）

- [ ] 单元测试编写
- [ ] 集成测试编写
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 浏览器兼容性测试

### 阶段五：部署与文档（1周）

- [ ] 应用打包与部署
- [ ] 用户文档编写
- [ ] 开发文档完善
- [ ] 发布准备

## API设计

### 项目相关API

- `GET /api/projects`: 获取项目列表
- `GET /api/projects/:id`: 获取项目详情
- `POST /api/projects`: 创建新项目
- `PUT /api/projects/:id`: 更新项目信息
- `DELETE /api/projects/:id`: 删除项目
- `POST /api/projects/:id/analyze`: 开始项目分析
- `GET /api/projects/:id/results`: 获取项目分析结果

### 数据库相关API

- `GET /api/irradiance`: 获取光照数据列表
- `POST /api/irradiance`: 创建光照数据
- `GET /api/electricity-prices`: 获取电价政策列表
- `POST /api/electricity-prices`: 创建电价政策
- `GET /api/suppliers`: 获取供应商列表
- `POST /api/suppliers`: 创建供应商
- `GET /api/equipment`: 获取设备列表
- `POST /api/equipment`: 创建设备

## 代码规范

- 使用TypeScript进行静态类型检查
- 遵循ESLint和Prettier配置的代码风格
- 组件使用函数式组件和React Hooks
- 文件命名采用kebab-case（如`project-list.tsx`）
- 组件命名采用PascalCase（如`ProjectList`）
- 函数和变量命名采用camelCase（如`getProjectList`）
- 常量命名采用UPPER_SNAKE_CASE（如`MAX_PROJECT_COUNT`）
- 每个文件不超过1000行，超过则拆分为多个小文件

## 测试策略

- 使用Jest和React Testing Library进行单元测试
- 关键组件和功能需要编写测试用例
- 测试覆盖率目标：80%以上
- 使用Cypress进行端到端测试

## 部署策略

- 使用Docker容器化应用
- 支持多环境部署：开发、测试、生产
- 使用CI/CD自动化部署流程
- 支持版本回滚机制

## 结语

本文档将随着项目开发的进展不断更新和完善。开发团队应定期参考本文档，确保开发工作按照计划和规范进行。
