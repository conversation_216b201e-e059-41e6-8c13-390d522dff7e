# 光伏+储能项目经济性分析系统 - Hostinger部署指南

## 概述

本项目已成功部署在Hostinger服务器上，可通过以下地址访问：

- **网站地址**: https://pv-analysis.top
- **API地址**: https://pv-analysis.top/api

## 部署架构

- **前端**: React应用，构建后部署为静态文件
- **后端**: Node.js Express API服务器，使用PM2进程管理
- **代理**: Apache .htaccess配置，将API请求代理到Node.js服务器
- **域名**: pv-analysis.top (支持HTTPS)

## 部署脚本

### 1. 完整部署脚本 (`deploy_hostinger.sh`)

用于首次部署或完全重新部署：

```bash
./deploy_hostinger.sh
```

**功能**：
- 检查SSH连接
- 清理Git状态
- 拉取最新代码
- 安装依赖
- 设置环境变量
- 构建前端应用
- 启动/重启API服务器
- 配置.htaccess代理
- 部署前端文件

### 2. 更新部署脚本 (`update_hostinger.sh`)

用于日常代码更新：

```bash
./update_hostinger.sh
```

**功能**：
- 拉取最新代码
- 重新构建前端
- 重启API服务器
- 更新前端文件

## 服务器配置

### SSH连接信息
- **主机**: *************
- **端口**: 65002
- **用户**: u387728176
- **项目路径**: ~/domains/pv-analysis.top/public_html

### 服务器环境
- **Node.js**: v18.20.8 (通过NVM管理)
- **npm**: 10.8.2
- **PM2**: 进程管理器
- **Apache**: Web服务器 + 代理

### API服务器
- **端口**: 3001 (内部)
- **进程名**: pv-server
- **管理命令**: `pm2 list`, `pm2 restart pv-server`, `pm2 logs pv-server`

## 环境变量

### 生产环境 (.env.production)
```
VITE_API_BASE_URL=/api
```

### 开发环境 (.env.local)
```
VITE_API_BASE_URL=http://localhost:3001/api
```

## .htaccess配置

```apache
RewriteEngine On
RewriteBase /

# API代理到后端服务器（必须在前端路由之前）
RewriteCond %{REQUEST_URI} ^/api/(.*)$
RewriteRule ^api/(.*)$ http://localhost:3001/api/$1 [P,L]

# 处理前端路由（React Router）
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# 设置CORS头
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>
```

## 故障排除

### 1. API无法访问
```bash
# 检查PM2进程状态
ssh -p 65002 u387728176@************* "pm2 list"

# 查看API服务器日志
ssh -p 65002 u387728176@************* "pm2 logs pv-server"

# 重启API服务器
ssh -p 65002 u387728176@************* "pm2 restart pv-server"
```

### 2. 前端无法加载
```bash
# 检查.htaccess文件
ssh -p 65002 u387728176@************* "cat ~/domains/pv-analysis.top/public_html/.htaccess"

# 重新部署前端
./update_hostinger.sh
```

### 3. 构建失败
- 确保Node.js版本正确
- 检查依赖是否完整安装
- 查看构建日志中的错误信息

## 维护命令

### 查看服务器状态
```bash
ssh -p 65002 u387728176@************* "pm2 monit"
```

### 查看磁盘使用情况
```bash
ssh -p 65002 u387728176@************* "df -h"
```

### 清理日志
```bash
ssh -p 65002 u387728176@************* "pm2 flush"
```

## 注意事项

1. **HTTPS强制**: Hostinger强制使用HTTPS，所有HTTP请求会被重定向
2. **Node.js版本**: 服务器使用Node.js 18.20.8，某些包可能需要Node.js 20+
3. **TypeScript检查**: 部署时跳过TypeScript检查以避免构建失败
4. **缓存**: 浏览器可能缓存旧版本，部署后可能需要强制刷新

## 安全考虑

1. SSH密钥已配置用于GitHub访问
2. API服务器仅在内部端口3001运行
3. 通过Apache代理访问API，增加安全层
4. CORS头已正确配置

## 联系信息

如有问题，请联系系统管理员或查看项目文档。
