#!/bin/bash

# 服务器端更新脚本
# 在Hostinger服务器上执行的更新脚本

set -e  # 遇到错误立即退出

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 项目目录
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"

# 主函数
main() {
    print_info "开始执行服务器更新..."
    
    # 加载NVM
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    nvm use node
    
    # 进入项目目录
    cd "$PROJECT_DIR"
    
    print_info "当前目录: $(pwd)"
    
    # 停止现有进程
    print_info "停止现有进程..."
    pkill -f "node.*server" || true
    pkill -f "npm.*dev" || true
    
    # 清理Git状态
    print_info "清理Git状态..."
    git reset --hard HEAD
    git clean -fd
    
    # 拉取最新代码
    print_info "拉取最新代码..."
    git pull origin main
    
    # 设置环境变量
    print_info "设置环境变量..."
    echo "VITE_API_BASE_URL=/api" > .env.production
    
    # 安装前端依赖
    print_info "安装前端依赖..."
    npm install
    
    # 构建前端应用
    print_info "构建前端应用..."
    # 备份原始package.json
    cp package.json package.json.bak
    # 修改构建命令
    sed -i 's/"build": "tsc -b && vite build"/"build": "vite build"/' package.json
    # 构建应用
    npm run build
    # 恢复原始package.json
    mv package.json.bak package.json
    
    # 安装服务器依赖
    print_info "安装服务器依赖..."
    cd server
    npm install
    
    # 安装PM2（如果未安装）
    if ! command -v pm2 &> /dev/null; then
        print_info "安装PM2..."
        npm install -g pm2
    fi
    
    # 启动服务器
    print_info "启动API服务器..."
    pm2 delete pv-server 2>/dev/null || true
    pm2 start src/index.js --name pv-server
    
    # 返回项目根目录
    cd ..
    
    # 创建.htaccess文件用于前端路由和API代理
    print_info "配置前端路由和API代理..."
    cat > .htaccess << 'EOF'
RewriteEngine On
RewriteBase /

# API代理到后端服务器（必须在前端路由之前）
RewriteCond %{REQUEST_URI} ^/api/(.*)$
RewriteRule ^api/(.*)$ http://localhost:3001/api/$1 [P,L]

# 处理前端路由（React Router）
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# 设置CORS头
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>
EOF
    
    # 复制构建文件到根目录
    print_info "部署前端文件..."
    cp -r dist/* .
    
    print_success "更新完成！"
    print_info "前端地址: https://pv-analysis.top"
    print_info "API地址: https://pv-analysis.top/api"
    
    # 显示PM2状态
    pm2 list
}

# 执行主函数
main "$@"
