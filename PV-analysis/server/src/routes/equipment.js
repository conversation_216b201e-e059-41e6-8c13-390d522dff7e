/**
 * 设备路由
 */
const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');

// 数据类型
const DATA_TYPE = 'equipment';

/**
 * 获取设备列表
 * GET /api/equipment
 */
router.get('/', async (req, res, next) => {
  try {
    const data = await dataService.getList(DATA_TYPE);
    
    // 分页处理
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = page * pageSize;
    
    const paginatedData = data.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        items: paginatedData,
        total: data.length
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取设备详情
 * GET /api/equipment/:id
 */
router.get('/:id', async (req, res, next) => {
  try {
    const data = await dataService.getDetail(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 创建设备
 * POST /api/equipment
 */
router.post('/', async (req, res, next) => {
  try {
    const data = await dataService.create(DATA_TYPE, req.body);
    res.status(201).json({
      success: true,
      data
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新设备
 * PUT /api/equipment/:id
 */
router.put('/:id', async (req, res, next) => {
  try {
    const data = await dataService.update(DATA_TYPE, req.params.id, req.body);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 删除设备
 * DELETE /api/equipment/:id
 */
router.delete('/:id', async (req, res, next) => {
  try {
    await dataService.remove(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      message: '设备删除成功'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
