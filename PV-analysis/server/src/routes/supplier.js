/**
 * 供应商路由
 */
const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');

// 数据类型
const DATA_TYPE = 'suppliers';

/**
 * 获取供应商列表
 * GET /api/suppliers
 */
router.get('/', async (req, res, next) => {
  try {
    const data = await dataService.getList(DATA_TYPE);
    
    // 分页处理
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = page * pageSize;
    
    const paginatedData = data.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        items: paginatedData,
        total: data.length
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 获取供应商详情
 * GET /api/suppliers/:id
 */
router.get('/:id', async (req, res, next) => {
  try {
    const data = await dataService.getDetail(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 创建供应商
 * POST /api/suppliers
 */
router.post('/', async (req, res, next) => {
  try {
    const data = await dataService.create(DATA_TYPE, req.body);
    res.status(201).json({
      success: true,
      data
    });
  } catch (error) {
    next(error);
  }
});

/**
 * 更新供应商
 * PUT /api/suppliers/:id
 */
router.put('/:id', async (req, res, next) => {
  try {
    const data = await dataService.update(DATA_TYPE, req.params.id, req.body);
    res.json({
      success: true,
      data
    });
  } catch (error) {
    if (error.message.includes('不存在')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    next(error);
  }
});

/**
 * 删除供应商
 * DELETE /api/suppliers/:id
 */
router.delete('/:id', async (req, res, next) => {
  try {
    await dataService.remove(DATA_TYPE, req.params.id);
    res.json({
      success: true,
      message: '供应商删除成功'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
