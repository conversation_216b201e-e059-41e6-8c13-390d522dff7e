#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "光伏+储能项目经济性分析系统 - 服务器端数据存储服务启动脚本"
    echo ""
    echo "用法: ./start-server.sh [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                显示帮助信息"
    echo "  --port PORT               设置服务器端口 (默认: 3000)"
    echo ""
    echo "示例:"
    echo "  ./start-server.sh                使用默认设置启动服务器"
    echo "  ./start-server.sh --port 8080    使用自定义端口启动服务器"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --port)
                SERVER_PORT="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 设置默认值（如果未指定）
    SERVER_PORT=${SERVER_PORT:-3000}
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "未找到Node.js，请先安装Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm >/dev/null 2>&1; then
        print_error "未找到npm，请先安装npm"
        exit 1
    fi
    
    # 检查Node.js版本
    NODE_VERSION=$(node -v | cut -d 'v' -f 2)
    NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d '.' -f 1)
    
    if [ $NODE_MAJOR_VERSION -lt 14 ]; then
        print_warning "Node.js版本过低，建议使用v14.0.0或更高版本"
    else
        print_success "Node.js版本: $NODE_VERSION"
    fi
    
    print_success "依赖检查通过"
}

# 安装依赖
install_dependencies() {
    print_info "安装依赖..."
    
    # 检查node_modules目录是否存在
    if [ ! -d "node_modules" ]; then
        print_info "正在安装依赖，请稍候..."
        npm install
        
        if [ $? -ne 0 ]; then
            print_error "依赖安装失败，请检查错误信息"
            exit 1
        fi
        
        print_success "依赖安装完成"
    else
        print_info "依赖已安装，跳过安装步骤"
    fi
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    
    # 创建数据目录
    mkdir -p data/irradiance
    mkdir -p data/electricity-prices
    mkdir -p data/suppliers
    mkdir -p data/equipment
    
    # 创建上传目录
    mkdir -p uploads
    
    print_success "目录创建完成"
}

# 启动服务器
start_server() {
    print_info "正在启动服务器，端口: $SERVER_PORT..."
    
    # 检查是否有正在运行的实例
    if command -v lsof >/dev/null 2>&1; then
        if lsof -i :$SERVER_PORT > /dev/null 2>&1; then
            print_warning "端口 $SERVER_PORT 已被占用，尝试停止现有实例..."
            kill $(lsof -t -i:$SERVER_PORT) 2>/dev/null || true
            sleep 2
        fi
    else
        print_warning "未找到lsof命令，无法检查端口占用情况"
    fi
    
    # 设置环境变量
    export PORT=$SERVER_PORT
    
    # 启动服务器
    npm run dev &
    SERVER_PID=$!
    
    # 保存进程ID
    echo $SERVER_PID > .server.pid
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否成功启动
    if ps -p $SERVER_PID > /dev/null; then
        print_success "服务器已成功启动！"
        print_info "访问地址: http://localhost:$SERVER_PORT"
    else
        print_error "服务器启动失败，请检查日志"
        exit 1
    fi
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 显示欢迎信息
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统 - 服务器端数据存储服务"
    echo "=================================================="
    echo ""
    
    # 检查依赖
    check_dependencies
    
    # 安装依赖
    install_dependencies
    
    # 创建必要的目录
    create_directories
    
    # 启动服务器
    start_server
    
    echo ""
    echo "=================================================="
    echo "  服务器已成功启动"
    echo "  按 Ctrl+C 停止服务器"
    echo "=================================================="
    echo "  服务器端口: $SERVER_PORT"
    echo "  API基础URL: http://localhost:$SERVER_PORT/api"
    echo "=================================================="
    
    # 等待用户按Ctrl+C
    wait $SERVER_PID
}

# 执行主函数
main "$@"
