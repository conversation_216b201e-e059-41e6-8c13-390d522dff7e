# 光伏+储能项目经济性分析系统 - 服务器端数据存储服务

这是光伏+储能项目经济性分析系统的服务器端数据存储服务，用于存储和管理系统中的各种数据，包括光照数据、电价政策、供应商和设备信息等。

## 功能特点

- 提供RESTful API接口，支持数据的增删改查
- 数据持久化存储，确保数据安全
- 支持文件上传功能，用于处理CSV等数据文件
- 支持跨域请求，方便前端调用
- 提供详细的日志记录，便于调试和问题排查

## 技术栈

- Node.js
- Express
- Multer (文件上传)
- CORS (跨域支持)
- Morgan (日志记录)

## 目录结构

```
server/
├── data/                # 数据存储目录
│   ├── irradiance/      # 光照数据
│   ├── electricity-prices/ # 电价政策
│   ├── suppliers/       # 供应商
│   └── equipment/       # 设备
├── src/                 # 源代码
│   ├── index.js         # 入口文件
│   ├── routes/          # 路由
│   │   ├── irradiance.js    # 光照数据路由
│   │   ├── electricityPrice.js # 电价政策路由
│   │   ├── supplier.js      # 供应商路由
│   │   └── equipment.js     # 设备路由
│   └── services/        # 服务
│       └── dataService.js   # 数据服务
├── uploads/             # 上传文件临时存储目录
├── package.json         # 项目配置
└── README.md            # 说明文档
```

## 安装与启动

### 安装依赖

```bash
cd server
npm install
```

### 启动服务器

```bash
# 开发模式启动
npm run dev

# 生产模式启动
npm start
```

或者使用提供的启动脚本：

```bash
./start-server.sh
```

## API接口

### 光照数据

- `GET /api/irradiance` - 获取光照数据列表
- `GET /api/irradiance/:id` - 获取光照数据详情
- `POST /api/irradiance` - 创建光照数据
- `PUT /api/irradiance/:id` - 更新光照数据
- `DELETE /api/irradiance/:id` - 删除光照数据
- `POST /api/irradiance/upload` - 上传光照数据CSV文件

### 电价政策

- `GET /api/electricity-prices` - 获取电价政策列表
- `GET /api/electricity-prices/:id` - 获取电价政策详情
- `POST /api/electricity-prices` - 创建电价政策
- `PUT /api/electricity-prices/:id` - 更新电价政策
- `DELETE /api/electricity-prices/:id` - 删除电价政策

### 供应商

- `GET /api/suppliers` - 获取供应商列表
- `GET /api/suppliers/:id` - 获取供应商详情
- `POST /api/suppliers` - 创建供应商
- `PUT /api/suppliers/:id` - 更新供应商
- `DELETE /api/suppliers/:id` - 删除供应商

### 设备

- `GET /api/equipment` - 获取设备列表
- `GET /api/equipment/:id` - 获取设备详情
- `POST /api/equipment` - 创建设备
- `PUT /api/equipment/:id` - 更新设备
- `DELETE /api/equipment/:id` - 删除设备

## 数据存储

服务器使用文件系统进行数据存储，所有数据都保存在`data`目录下的对应子目录中。每个数据项都以JSON文件的形式存储，文件名为数据项的ID。

每种数据类型都有一个`index.json`文件，用于存储该类型的所有数据项的元数据，便于快速获取列表数据。

## 文件上传

服务器支持文件上传功能，上传的文件会临时存储在`uploads`目录中。上传成功后，服务器会返回文件的相关信息，前端可以使用这些信息进行后续处理。

## 注意事项

- 服务器默认监听3000端口，可以通过环境变量`PORT`或启动参数修改
- 上传文件大小限制为50MB
- 服务器会自动创建必要的目录，无需手动创建
- 数据文件使用UTF-8编码，确保正确处理中文等字符
