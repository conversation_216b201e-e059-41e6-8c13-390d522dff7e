#!/bin/bash

# Hostinger服务器更新脚本
# 光伏+储能项目经济性分析系统

set -e  # 遇到错误立即退出

# 配置变量
SERVER_HOST="*************"
SERVER_PORT="65002"
SERVER_USER="u387728176"

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 更新服务器代码
update_server() {
    print_info "开始更新Hostinger服务器代码..."

    ssh -i ~/.ssh/hostinger_key -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << 'ENDSSH'
        set -e

        # 设置颜色输出函数
        print_info() {
            echo -e "\033[34m[INFO]\033[0m $1"
        }

        print_success() {
            echo -e "\033[32m[SUCCESS]\033[0m $1"
        }

        # 加载NVM
        export NVM_DIR="$HOME/.nvm"
        [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
        nvm use node

        # 进入项目目录
        cd ~/domains/pv-analysis.top/public_html

        # 拉取最新代码
        print_info "拉取最新代码..."
        git pull origin main

        # 设置环境变量
        print_info "设置环境变量..."
        echo "VITE_API_BASE_URL=/api" > .env.production

        # 构建前端应用
        print_info "构建前端应用..."
        # 备份原始package.json
        cp package.json package.json.bak
        # 修改构建命令
        sed -i 's/"build": "tsc -b && vite build"/"build": "vite build"/' package.json
        # 构建应用
        npm run build
        # 恢复原始package.json
        mv package.json.bak package.json

        # 重启API服务器
        print_info "重启API服务器..."
        cd server
        pm2 restart pv-server
        cd ..

        # 复制构建文件到根目录
        print_info "更新前端文件..."
        cp -r dist/* .

        print_success "更新完成！"

        # 显示PM2状态
        pm2 list

ENDSSH
}

# 主函数
main() {
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统 - Hostinger更新脚本"
    echo "=================================================="
    echo ""

    update_server

    echo ""
    echo "=================================================="
    echo "  更新完成！"
    echo "=================================================="
    echo "  网站地址: https://pv-analysis.top"
    echo "  API地址: https://pv-analysis.top/api"
    echo "=================================================="
}

# 执行主函数
main "$@"
