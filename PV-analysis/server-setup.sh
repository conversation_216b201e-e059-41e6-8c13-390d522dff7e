#!/bin/bash

# 光伏+储能项目经济性分析系统 - 服务器端设置脚本

echo "开始设置服务器端..."

# 创建必要的目录结构
mkdir -p ~/domains/pv-analysis.top/public_html/server
mkdir -p ~/domains/pv-analysis.top/public_html/server/routes
mkdir -p ~/domains/pv-analysis.top/public_html/server/data
mkdir -p ~/domains/pv-analysis.top/public_html/server/uploads

# 切换到服务器目录
cd ~/domains/pv-analysis.top/public_html/server

# 创建package.json
cat > package.json << 'EOF'
{
  "name": "pv-analysis-server",
  "version": "1.0.0",
  "description": "光伏+储能项目经济性分析系统 - 服务器端数据存储服务",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "dependencies": {
    "cors": "^2.8.5",
    "express": "^4.18.2",
    "morgan": "^1.10.0",
    "multer": "^1.4.5-lts.1",
    "uuid": "^9.0.0"
  },
  "devDependencies": {
    "nodemon": "^2.0.22"
  },
  "engines": {
    "node": ">=14.0.0"
  },
  "author": "",
  "license": "ISC"
}
EOF

# 创建主入口文件
cat > index.js << 'EOF'
/**
 * 服务器入口文件
 * 光伏+储能项目经济性分析系统 - 服务器端数据存储服务
 */
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');

// 导入路由
const irradianceRoutes = require('./routes/irradiance');
const electricityPriceRoutes = require('./routes/electricityPrice');
const supplierRoutes = require('./routes/supplier');
const equipmentRoutes = require('./routes/equipment');
const attachmentRoutes = require('./routes/attachment');
const projectRoutes = require('./routes/project');
const settingsRoutes = require('./routes/settings');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3001;

// 确保数据目录存在
const dataDir = path.join(__dirname, './data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 确保上传目录存在
const uploadsDir = path.join(__dirname, './uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 中间件
app.use(cors()); // 允许跨域请求
app.use(morgan('dev')); // 日志记录
app.use(express.json({ limit: '50mb' })); // 解析JSON请求体，增加限制以处理大型数据
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 解析URL编码的请求体

// 路由
app.use('/api/irradiance', irradianceRoutes);
app.use('/api/electricity-prices', electricityPriceRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/equipment', equipmentRoutes);
app.use('/api/attachments', attachmentRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/settings', settingsRoutes);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, './uploads')));

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '光伏+储能项目经济性分析系统 - 服务器端数据存储服务',
    status: 'running',
    endpoints: [
      '/api/irradiance',
      '/api/electricity-prices',
      '/api/suppliers',
      '/api/equipment',
      '/api/attachments',
      '/api/projects',
      '/api/settings',
      '/api/health'
    ]
  });
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    message: '服务器正常运行'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: err.message || '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器已启动，监听端口: ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});
EOF

# 创建路由目录
mkdir -p routes

# 安装依赖
echo "安装依赖..."
npm install

# 使用PM2启动服务
echo "使用PM2启动服务..."
pm2 start index.js --name pv-server

echo "服务器设置完成！"
echo "API服务器已在端口3001上启动"
