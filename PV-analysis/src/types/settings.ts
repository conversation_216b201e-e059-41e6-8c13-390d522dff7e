// 主题类型
export type ThemeType = 'light' | 'dark' | 'system';

// 语言类型
export type LanguageType = 'zh' | 'en' | 'ja';

// 货币类型
export type CurrencyType = 'JPY' | 'CNY' | 'USD';

// 时区类型
export type TimezoneType = 'Asia/Tokyo' | 'Asia/Shanghai' | 'America/New_York' | 'Europe/London';

// 设置类型
export interface Settings {
  language: LanguageType;
  currency: CurrencyType;
  timezone: TimezoneType;
  theme: ThemeType;
  siteName: string;
  siteLogo: string;
}

// 通知设置类型
export interface NotificationSettings {
  email: boolean;
  system: boolean;
  projectComplete: boolean;
  updates: boolean;
}

// 备份设置类型
export interface BackupSettings {
  autoBackup: boolean;
  backupInterval: 'daily' | 'weekly' | 'monthly';
  backupTime: string;
  maxBackups: number;
}
