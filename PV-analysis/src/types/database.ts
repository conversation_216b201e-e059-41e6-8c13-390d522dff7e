// 光照数据类型
export interface IrradianceData {
  id: string;
  name: string;
  location: string;
  latitude: number;
  longitude: number;
  year: number;
  dataSize: number; // 数据条数
  syncStatus: 'synced' | 'local-only' | 'server-only' | 'invalid'; // 同步状态
  data: Array<IrradianceHourlyData>;
  createdAt: string;
  updatedAt: string;
}

// 光照小时数据类型
export interface IrradianceHourlyData {
  year: number; // 年
  month: number; // 月
  day: number; // 日
  hour: number; // 小时
  temperature: number; // 温度 Ta，单位：℃
  sunHeight: number; // 太阳高度 hs
  sunAngle: number; // 太阳角 Az
  globalHorizontalIrradiance: number; // 水平辐射强度 G_Gh，单位：W/m²
  diffuseHorizontalIrradiance: number; // 水平漫射强度 G_Dh，单位：W/m²
  directNormalIrradiance: number; // 直射强度 G_Bn，单位：W/m²
  directTime: number; // 直射时间 Sd，单位：分钟
}

// 光照数据可视化选项
export interface IrradianceVisualizationOptions {
  metric: 'G_Gh' | 'Ta' | 'Sd'; // 数据指标：水平辐射强度、温度、直射时间
  viewMode: 'hourly' | 'daily' | 'monthly'; // 查看方式：按小时、按日、按月
  month?: number; // 选择的月份，1-12，或者0表示全年
  day?: number; // 选择的日期，1-31
}

// CSV解析结果类型
export interface CSVParseResult {
  cityName: string;
  latitude: number;
  longitude: number;
  data: Array<IrradianceHourlyData>;
}

// 电价规则类型
export interface PriceRule {
  startTime: string; // 格式：HH:MM
  endTime: string; // 格式：HH:MM
  price: number; // 用电价格，单位：JPY/kWh
  gridFeedInPrice?: number; // 光伏上网电价，单位：JPY/kWh
  type: 'peak' | 'normal' | 'valley' | 'super-peak'; // 峰时、平时、谷时、尖峰
}

// 季节性电价政策类型
export interface SeasonalPricePolicy {
  id: string; // 季节政策ID
  name: string; // 季节名称，如"夏季"、"冬季"等
  months: number[]; // 适用月份，如[6, 7, 8]表示6-8月
  rules: PriceRule[]; // 该季节的电价规则
}

// 电价政策类型
export interface ElectricityPrice {
  id: string;
  name: string;
  region: string;
  policyType: 'fixed' | 'seasonal'; // 固定电价或季节性电价
  // 固定电价使用rules，季节性电价使用seasonalPolicies
  rules: PriceRule[]; // 固定电价规则
  seasonalPolicies: SeasonalPricePolicy[]; // 季节性电价政策
  createdAt: string;
  updatedAt: string;
  syncStatus: 'synced' | 'local-only' | 'server-only'; // 同步状态
}

// 供应商类型
export interface Supplier {
  id: string;
  name: string;
  contact: string;
  phone: string;
  email: string;
  address: string;
  products: string[];
  supplierType?: string;
  region?: string;
  cooperationStatus?: string;
  rating?: string;
  createdAt: string;
  updatedAt: string;
  syncStatus?: 'synced' | 'local-only' | 'server-only';
}

// 设备附件类型
export interface EquipmentAttachment {
  id: string;
  name: string;
  type: 'manual' | 'image' | 'datasheet' | 'other'; // 附件类型：技术手册、图片、数据表、其他
  url: string; // 文件URL
  fileSize: number; // 文件大小，单位：字节
  fileType: string; // 文件MIME类型
  uploadedAt: string; // 上传时间
}

// 设备类型
export interface Equipment {
  id: string;
  name: string;
  type: 'pv' | 'storage' | 'inverter' | 'other';
  manufacturer: string;
  model: string;
  specs: Record<string, any>;
  price: number;
  supplierId?: string;
  attachments?: EquipmentAttachment[]; // 设备附件
  createdAt: string;
  updatedAt: string;
  syncStatus?: 'synced' | 'local-only' | 'server-only';
}

// 光伏设备规格类型
export interface PVSpecs {
  power: number; // 单位：W
  efficiency: number; // 效率，百分比
  area: number; // 单位：m²
  warranty: number; // 质保期，单位：年
  degradation: number; // 年衰减率，百分比
  length?: number; // 长度，单位：mm
  width?: number; // 宽度，单位：mm
  height?: number; // 高度，单位：mm
}

// 储能设备规格类型
export interface StorageSpecs {
  capacity: number; // 单位：kWh
  power: number; // 单位：kW
  efficiency: number; // 效率，百分比
  cycles: number; // 循环次数
  warranty: number; // 质保期，单位：年
  depthOfDischarge: number; // 放电深度，百分比
  length?: number; // 长度，单位：mm
  width?: number; // 宽度，单位：mm
  height?: number; // 高度，单位：mm
}

// 逆变器规格类型
export interface InverterSpecs {
  power: number; // 单位：kW
  efficiency: number; // 效率，百分比
  mpptRange: {
    min: number; // 单位：V
    max: number; // 单位：V
  };
  warranty: number; // 质保期，单位：年
  length?: number; // 长度，单位：mm
  width?: number; // 宽度，单位：mm
  height?: number; // 高度，单位：mm
}
