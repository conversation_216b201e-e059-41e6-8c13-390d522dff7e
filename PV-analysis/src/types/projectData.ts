/**
 * 项目数据结构定义
 * 包含项目基本信息、输入数据和运算结果
 */

import { ProjectBasicInfo, PVModule, EnergyStorage, Inverter, OtherInvestment, ElectricityUsage, ProjectStatus, SyncStatus } from './project';

/**
 * 项目小时数据点类型
 * 记录每小时的发电、用电、储能等数据
 */
export interface ProjectHourlyData {
  // 时间标识
  hour: number;       // 小时 (0-23)
  day: number;        // 日 (1-31)
  month: number;      // 月 (1-12)

  // 光伏发电数据 - 每个组件的发电量
  pvGeneration: {
    [moduleId: string]: number;  // 每个光伏组件的发电量 (kWh)
  };

  // 储能数据
  storageCharge: number;     // 本小时储能充电量 (kWh，正值表示充电，负值表示放电)
  storageCapacity: number;   // 本小时结束时储能的存储电量 (kWh)

  // 用电数据
  electricityConsumption: number;  // 本小时用户用电量 (kWh)

  // 电网交互数据
  gridExport: number;  // 本小时上网电量 (kWh)，只能为正值或0
  gridImport: number;  // 本小时从电网购电量 (kWh)，只能为正值或0

  // 电价数据
  electricityPrice: number;  // 本小时购电电价 (JPY/kWh)
  gridFeedInPrice: number;   // 本小时上网电价 (JPY/kWh)
}

/**
 * 项目日数据类型
 * 汇总每日的发电、用电、储能等数据
 */
export interface ProjectDailyData {
  day: number;        // 日 (1-31)
  month: number;      //月 (1-12)

  // 光伏发电数据
  pvGeneration: number;  // 当日光伏发电量 (kWh)

  // 储能数据
  storageCharge: number;     // 当日储能充电量 (kWh)
  storageDischarge: number;  // 当日储能放电量 (kWh)

  // 用电数据
  electricityConsumption: number;  // 当日用户用电量 (kWh)

  // 电网交互数据
  gridExport: number;  // 当日上网电量 (kWh)
  gridImport: number;  // 当日从电网购电量 (kWh)

  // 经济效益数据
  gridExportIncome: number;  // 当日上网售电收入 (JPY)
  pvBenefit: number;         // 当日光伏收益 (JPY)
  storageBenefit: number;    // 当日储能收益 (JPY)

  // 总收益
  totalBenefit: number;      // 当日总收益 (JPY)
}

/**
 * 项目月数据类型
 * 汇总每月的发电、用电、储能等数据
 */
export interface ProjectMonthlyData {
  month: number;      // 月 (1-12)

  // 光伏发电数据
  pvGeneration: number;  // 当月光伏发电量 (kWh)

  // 储能数据
  storageCharge: number;     // 当月储能充电量 (kWh)
  storageDischarge: number;  // 当月储能放电量 (kWh)

  // 用电数据
  electricityConsumption: number;  // 当月用户用电量 (kWh)

  // 电网交互数据
  gridExport: number;  // 当月上网电量 (kWh)
  gridImport: number;  // 当月从电网购电量 (kWh)

  // 经济效益数据
  gridExportIncome: number;  // 当月上网售电收入 (JPY)
  pvBenefit: number;         // 当月光伏收益 (JPY)
  storageBenefit: number;    // 当月储能收益 (JPY)

  // 总收益
  totalBenefit: number;      // 当月总收益 (JPY)
}

/**
 * 项目年数据类型
 * 汇总全年的发电、用电、储能等数据
 */
export interface ProjectYearlyData {
  // 光伏发电数据
  pvGeneration: number;  // 全年光伏发电量 (kWh)

  // 储能数据
  storageCharge: number;     // 全年储能充电量 (kWh)
  storageDischarge: number;  // 全年储能放电量 (kWh)

  // 用电数据
  electricityConsumption: number;  // 全年用户用电量 (kWh)

  // 电网交互数据
  gridExport: number;  // 全年上网电量 (kWh)
  gridImport: number;  // 全年从电网购电量 (kWh)

  // 经济效益数据
  gridExportIncome: number;  // 全年上网售电收入 (JPY)
  pvBenefit: number;         // 全年光伏收益 (JPY)
  storageBenefit: number;    // 全年储能收益 (JPY)

  // 总收益
  totalBenefit: number;      // 全年总收益 (JPY)

  // 投资回报分析
  roi: number;               // 投资回报率 (%)
  paybackPeriod: number;     // 回收期 (年)
}

/**
 * 项目分析结果类型
 */
export interface ProjectAnalysisResults {
  // 详细数据
  hourlyData: ProjectHourlyData[];   // 8760个小时数据点
  dailyData?: ProjectDailyData[];    // 日数据
  monthlyData?: ProjectMonthlyData[]; // 月数据
  yearlyData?: ProjectYearlyData;    // 年数据

  // 分析状态
  analysisCompleted: boolean;        // 分析是否完成
  analysisDate: string;              // 分析日期
}

/**
 * 完整项目数据结构
 */
export interface ProjectData {
  // 项目基本信息
  id: string;                      // 项目ID
  name: string;                    // 项目名称
  location: string;                // 项目位置
  capacity: number;                // 项目容量 (kW)
  status: ProjectStatus;           // 项目状态
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
  syncStatus?: SyncStatus; // 同步状态

  // 项目详细信息
  basicInfo: ProjectBasicInfo;     // 基本信息

  // 项目输入数据
  irradianceDataId: string;        // 光照数据ID
  electricityPriceId: string;      // 电价政策ID
  electricityUsage: ElectricityUsage; // 用电数据
  pvModules: PVModule[];           // 光伏组件
  energyStorage: EnergyStorage[];  // 储能设备
  inverters: Inverter[];           // 逆变器
  otherInvestments: OtherInvestment[]; // 其他投资

  // 项目分析结果
  analysisResults?: ProjectAnalysisResults; // 分析结果
}

/**
 * 创建空的项目小时数据点
 */
export const createEmptyHourlyData = (hour: number, day: number, month: number): ProjectHourlyData => {
  return {
    hour,
    day,
    month,
    pvGeneration: {},
    storageCharge: 0,
    storageCapacity: 0,
    electricityConsumption: 0,
    gridExport: 0,
    gridImport: 0,
    electricityPrice: 0,
    gridFeedInPrice: 0
  };
};

/**
 * 创建8760个小时数据点的数组
 */
export const createEmptyHourlyDataArray = (): ProjectHourlyData[] => {
  const hourlyData: ProjectHourlyData[] = [];

  // 每月的天数
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

  // 生成8760个小时数据点
  let hourIndex = 0;
  for (let month = 1; month <= 12; month++) {
    for (let day = 1; day <= daysInMonth[month - 1]; day++) {
      for (let hour = 0; hour < 24; hour++) {
        hourlyData.push(createEmptyHourlyData(hour, day, month));
        hourIndex++;
      }
    }
  }

  return hourlyData;
};
