import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IrradianceData, IrradianceVisualizationOptions, ElectricityPrice as DBElectricityPrice, Supplier as DBSupplier, Equipment as DBEquipment } from '../../types/database';

// 使用数据库中的类型定义
export type ElectricityPrice = DBElectricityPrice;
export type Supplier = DBSupplier;
export type Equipment = DBEquipment;

// 定义数据库状态类型
export interface DatabasesState {
  irradianceData: IrradianceData[];
  currentIrradianceData: IrradianceData | null;
  irradianceVisualizationOptions: IrradianceVisualizationOptions;
  electricityPrices: ElectricityPrice[];
  suppliers: Supplier[];
  equipment: Equipment[];
  isLoading: boolean;
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
}

// 初始状态
const initialState: DatabasesState = {
  irradianceData: [],
  currentIrradianceData: null,
  irradianceVisualizationOptions: {
    metric: 'G_Gh',
    viewMode: 'monthly',
  },
  electricityPrices: [],
  suppliers: [],
  equipment: [],
  isLoading: false,
  isUploading: false,
  uploadProgress: 0,
  error: null,
};

// 创建数据库切片
export const databasesSlice = createSlice({
  name: 'databases',
  initialState,
  reducers: {
    // 获取数据开始
    fetchDataStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    // 获取光照数据成功
    fetchIrradianceDataSuccess: (state, action: PayloadAction<IrradianceData[]>) => {
      state.isLoading = false;
      state.irradianceData = action.payload;
      state.error = null;
    },
    // 获取电价政策成功
    fetchElectricityPricesSuccess: (state, action: PayloadAction<ElectricityPrice[]>) => {
      state.isLoading = false;
      state.electricityPrices = action.payload;
      state.error = null;
    },
    // 获取供应商成功
    fetchSuppliersSuccess: (state, action: PayloadAction<Supplier[]>) => {
      state.isLoading = false;
      state.suppliers = action.payload;
      state.error = null;
    },
    // 获取设备成功
    fetchEquipmentSuccess: (state, action: PayloadAction<Equipment[]>) => {
      state.isLoading = false;
      state.equipment = action.payload;
      state.error = null;
    },
    // 获取数据失败
    fetchDataFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    // 添加光照数据
    addIrradianceData: (state, action: PayloadAction<IrradianceData>) => {
      state.irradianceData.push(action.payload);
    },
    // 更新光照数据
    updateIrradianceData: (state, action: PayloadAction<IrradianceData>) => {
      const index = state.irradianceData.findIndex(data => data.id === action.payload.id);
      if (index !== -1) {
        state.irradianceData[index] = action.payload;
      }
    },
    // 删除光照数据
    deleteIrradianceData: (state, action: PayloadAction<string>) => {
      console.log('Redux开始删除数据，ID:', action.payload);
      console.log('删除前数据列表长度:', state.irradianceData.length);
      console.log('当前选中的数据:', state.currentIrradianceData?.id);

      // 记录要删除的数据
      const dataToDelete = state.irradianceData.find(data => data.id === action.payload);
      console.log('要删除的数据:', dataToDelete ? {
        id: dataToDelete.id,
        name: dataToDelete.name,
        location: dataToDelete.location
      } : '未找到');

      // 从数组中过滤掉要删除的数据
      const originalLength = state.irradianceData.length;
      state.irradianceData = state.irradianceData.filter(data => {
        const keep = data.id !== action.payload;
        if (!keep) {
          console.log('从Redux状态中删除数据:', data.id, data.name);
        }
        return keep;
      });

      console.log('过滤后数据列表长度:', state.irradianceData.length);
      console.log('是否成功删除:', originalLength !== state.irradianceData.length ? '是' : '否');

      // 如果当前选中的数据被删除，清空当前数据
      if (state.currentIrradianceData && state.currentIrradianceData.id === action.payload) {
        console.log('正在清空当前选中的数据');
        state.currentIrradianceData = null;
      }

      console.log('Redux删除数据完成');
    },
    // 添加电价政策
    addElectricityPrice: (state, action: PayloadAction<ElectricityPrice>) => {
      state.electricityPrices.push(action.payload);
    },
    // 更新电价政策
    updateElectricityPrice: (state, action: PayloadAction<ElectricityPrice>) => {
      const index = state.electricityPrices.findIndex(price => price.id === action.payload.id);
      if (index !== -1) {
        state.electricityPrices[index] = action.payload;
      }
    },
    // 删除电价政策
    deleteElectricityPrice: (state, action: PayloadAction<string>) => {
      state.electricityPrices = state.electricityPrices.filter(price => price.id !== action.payload);
    },
    // 添加供应商
    addSupplier: (state, action: PayloadAction<Supplier>) => {
      state.suppliers.push(action.payload);
    },
    // 更新供应商
    updateSupplier: (state, action: PayloadAction<Supplier>) => {
      const index = state.suppliers.findIndex(supplier => supplier.id === action.payload.id);
      if (index !== -1) {
        state.suppliers[index] = action.payload;
      }
    },
    // 删除供应商
    deleteSupplier: (state, action: PayloadAction<string>) => {
      state.suppliers = state.suppliers.filter(supplier => supplier.id !== action.payload);
    },
    // 添加设备
    addEquipment: (state, action: PayloadAction<Equipment>) => {
      state.equipment.push(action.payload);
    },
    // 更新设备
    updateEquipment: (state, action: PayloadAction<Equipment>) => {
      const index = state.equipment.findIndex(equipment => equipment.id === action.payload.id);
      if (index !== -1) {
        state.equipment[index] = action.payload;
      }
    },
    // 删除设备
    deleteEquipment: (state, action: PayloadAction<string>) => {
      state.equipment = state.equipment.filter(equipment => equipment.id !== action.payload);
    },
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    // 设置当前光照数据
    setCurrentIrradianceData: (state, action: PayloadAction<IrradianceData | null>) => {
      state.currentIrradianceData = action.payload;
      state.isLoading = false; // 重置加载状态
    },
    // 设置光照数据可视化选项
    setIrradianceVisualizationOptions: (state, action: PayloadAction<Partial<IrradianceVisualizationOptions>>) => {
      state.irradianceVisualizationOptions = {
        ...state.irradianceVisualizationOptions,
        ...action.payload,
      };
    },
    // 上传开始
    uploadStart: (state) => {
      state.isUploading = true;
      state.uploadProgress = 0;
      state.error = null;
    },
    // 上传进度
    uploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    // 上传成功
    uploadSuccess: (state) => {
      state.isUploading = false;
      state.uploadProgress = 100;
    },
    // 上传失败
    uploadFailure: (state, action: PayloadAction<string>) => {
      state.isUploading = false;
      state.error = action.payload;
    },
    // 更新光照数据同步状态
    updateIrradianceDataSyncStatus: (state, action: PayloadAction<{ id: string; status: 'synced' | 'local-only' | 'server-only' }>) => {
      const { id, status } = action.payload;
      const index = state.irradianceData.findIndex(data => data.id === id);
      if (index !== -1) {
        state.irradianceData[index].syncStatus = status;
      }
      if (state.currentIrradianceData && state.currentIrradianceData.id === id) {
        state.currentIrradianceData.syncStatus = status;
      }
    },
  },
});

// 导出actions
export const {
  fetchDataStart,
  fetchIrradianceDataSuccess,
  fetchElectricityPricesSuccess,
  fetchSuppliersSuccess,
  fetchEquipmentSuccess,
  fetchDataFailure,
  addIrradianceData,
  updateIrradianceData,
  deleteIrradianceData,
  addElectricityPrice,
  updateElectricityPrice,
  deleteElectricityPrice,
  addSupplier,
  updateSupplier,
  deleteSupplier,
  addEquipment,
  updateEquipment,
  deleteEquipment,
  clearError,
  setCurrentIrradianceData,
  setIrradianceVisualizationOptions,
  uploadStart,
  uploadProgress,
  uploadSuccess,
  uploadFailure,
  updateIrradianceDataSyncStatus,
} = databasesSlice.actions;

// 导出reducer
export default databasesSlice.reducer;
