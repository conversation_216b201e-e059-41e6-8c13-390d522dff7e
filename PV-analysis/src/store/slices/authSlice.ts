import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 定义用户类型
export interface User {
  id: string;
  username: string;
  email: string;
}

// 定义认证状态类型
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// 创建认证切片
export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 登录请求开始
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    // 登录成功
    loginSuccess: (state, action: PayloadAction<{ user: User; token: string }>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.error = null;
    },
    // 登录失败
    loginFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.error = action.payload;
    },
    // 注销
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.error = null;
    },
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
});

// 导出actions
export const { loginStart, loginSuccess, loginFailure, logout, clearError } = authSlice.actions;

// 导出reducer
export default authSlice.reducer;
