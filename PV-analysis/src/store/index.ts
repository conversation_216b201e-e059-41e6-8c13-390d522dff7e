import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // 使用localStorage作为存储引擎

// 导入各个切片的reducer
import settingsReducer from './slices/settingsSlice';
import projectsReducer from './slices/projectsSlice';
import databasesReducer from './slices/databasesSlice';
import authReducer from './slices/authSlice';

// 配置项目数据持久化
const projectsPersistConfig = {
  key: 'projects',
  storage,
  //debug: true, // 启用调试模式
};

// 配置根持久化
const rootPersistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'settings'], // 持久化auth和settings状态
  //debug: true, // 启用调试模式
};

// 创建持久化的项目reducer
const persistedProjectsReducer = persistReducer(projectsPersistConfig, projectsReducer);

// 合并所有reducer
const rootReducer = combineReducers({
  settings: settingsReducer,
  projects: persistedProjectsReducer, // 使用持久化的项目reducer
  databases: databasesReducer,
  auth: authReducer,
});

// 创建持久化reducer
const persistedReducer = persistReducer(rootPersistConfig, rootReducer);

// 配置Redux store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // 优化性能，禁用不必要的中间件
      immutableCheck: process.env.NODE_ENV === 'production' ? false : {
        warnAfter: 128 // 提高警告阈值，减少开发环境中的警告
      },
      serializableCheck: {
        // 忽略redux-persist的action类型
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE', 'persist/REGISTER'],
        // 忽略siteLogo字段的序列化检查，因为它可能包含很长的Base64字符串
        ignoredPaths: ['settings.siteLogo'],
        warnAfter: 128 // 提高警告阈值，减少开发环境中的警告
      },
    }),
});

// 创建persistor
export const persistor = persistStore(store);

// 定义RootState和AppDispatch类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 定义自定义hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
