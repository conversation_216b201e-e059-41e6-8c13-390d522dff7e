import { useState, useEffect } from 'react';

/**
 * 使用媒体查询的自定义钩子
 * @param query 媒体查询字符串
 * @returns 是否匹配
 */
export function useMediaQuery(query: string): boolean {
  // 创建媒体查询列表
  const getMatches = (query: string): boolean => {
    // 检查是否在浏览器环境
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  };

  // 状态和setter
  const [matches, setMatches] = useState<boolean>(getMatches(query));

  // 处理媒体查询变化
  function handleChange() {
    setMatches(getMatches(query));
  }

  // 监听媒体查询变化
  useEffect(() => {
    const matchMedia = window.matchMedia(query);

    // 初始化
    handleChange();

    // 监听变化
    if (matchMedia.addListener) {
      // 旧API
      matchMedia.addListener(handleChange);
    } else {
      // 新API
      matchMedia.addEventListener('change', handleChange);
    }

    // 清理函数
    return () => {
      if (matchMedia.removeListener) {
        // 旧API
        matchMedia.removeListener(handleChange);
      } else {
        // 新API
        matchMedia.removeEventListener('change', handleChange);
      }
    };
  }, [query]);

  return matches;
}
