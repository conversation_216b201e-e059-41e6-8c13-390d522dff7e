import { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '../store';
import { setTheme } from '../store/slices/settingsSlice';
import { useMediaQuery } from './useMediaQuery';
import { ThemeType } from '../types/settings';

/**
 * 使用主题的自定义钩子
 * @returns [当前主题, 设置主题的函数]
 */
export function useTheme(): [ThemeType, (theme: ThemeType) => void] {
  const dispatch = useAppDispatch();
  const theme = useAppSelector((state) => state.settings.theme);
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
  const [forceUpdate, setForceUpdate] = useState(0);

  // 根据系统主题和用户设置确定实际主题
  const actualTheme = theme === 'system' ? (prefersDarkMode ? 'dark' : 'light') : theme;

  // 更新主题
  const updateTheme = (newTheme: ThemeType) => {
    dispatch(setTheme(newTheme));
    // 强制组件重新渲染
    setForceUpdate(prev => prev + 1);
  };

  // 应用主题到文档
  useEffect(() => {
    const root = window.document.documentElement;

    if (actualTheme === 'dark') {
      root.classList.add('dark');
      root.style.colorScheme = 'dark';
      // 设置深色模式下的背景色
      document.body.style.backgroundColor = 'var(--background-color)';
      document.body.style.color = 'var(--text-color)';

      // 更新菜单主题
      const menuElements = document.querySelectorAll('.ant-menu');
      menuElements.forEach(menu => {
        menu.classList.remove('ant-menu-light');
        menu.classList.add('ant-menu-dark');
      });

      // 更新表格主题
      const tableElements = document.querySelectorAll('.ant-table');
      tableElements.forEach(table => {
        table.classList.add('dark-table');
      });
    } else {
      root.classList.remove('dark');
      root.style.colorScheme = 'light';
      // 设置浅色模式下的背景色
      document.body.style.backgroundColor = 'var(--background-color)';
      document.body.style.color = 'var(--text-color)';

      // 更新菜单主题
      const menuElements = document.querySelectorAll('.ant-menu');
      menuElements.forEach(menu => {
        menu.classList.remove('ant-menu-dark');
        menu.classList.add('ant-menu-light');
      });

      // 更新表格主题
      const tableElements = document.querySelectorAll('.ant-table');
      tableElements.forEach(table => {
        table.classList.remove('dark-table');
      });
    }

    // 触发一个自定义事件，通知其他组件主题已更改
    const event = new CustomEvent('themechange', { detail: { theme: actualTheme } });
    window.dispatchEvent(event);

    // 强制重新渲染整个应用
    setTimeout(() => {
      const refreshEvent = new CustomEvent('theme-refresh');
      window.dispatchEvent(refreshEvent);

      // 额外处理：确保菜单主题正确应用
      const menuTheme = actualTheme === 'dark' ? 'dark' : 'light';
      document.querySelectorAll('.main-menu').forEach(menu => {
        if (menuTheme === 'dark') {
          menu.classList.remove('ant-menu-light');
          menu.classList.add('ant-menu-dark');
        } else {
          menu.classList.remove('ant-menu-dark');
          menu.classList.add('ant-menu-light');
        }
      });

      // 额外处理：确保表格主题正确应用
      document.querySelectorAll('.ant-table').forEach(table => {
        if (actualTheme === 'dark') {
          table.classList.add('dark-table');
        } else {
          table.classList.remove('dark-table');
        }
      });

      // 额外处理：强制更新表格行样式
      document.querySelectorAll('.ant-table-row').forEach(row => {
        row.style.transition = 'background-color 0.3s';
        if (actualTheme === 'dark') {
          row.classList.add('dark-row');
        } else {
          row.classList.remove('dark-row');
        }
      });
    }, 0);
  }, [actualTheme, forceUpdate]);

  return [theme, updateTheme];
}
