import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme as antdTheme, App as AntdApp } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import enUS from 'antd/lib/locale/en_US';
import jaJP from 'antd/lib/locale/ja_JP';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from './store';
import { useTheme, useDocumentTitle } from './hooks';

// 布局组件
import { MainLayout, AuthLayout } from './components/layout';

// 认证页面
import Login from './pages/auth/Login';

// 项目页面
import ProjectList from './pages/projects/ProjectList';
import NewProject from './pages/projects/NewProject';
import EditProject from './pages/projects/EditProject';
import ProjectAnalysis from './pages/projects/ProjectAnalysis';

// 数据库页面
import IrradianceDatabase from './pages/databases/IrradianceDatabase';
import ElectricityPriceDatabase from './pages/databases/ElectricityPriceDatabase';
import SupplierDatabase from './pages/databases/SupplierDatabase';
import EquipmentDatabase from './pages/databases/EquipmentDatabase';

// 设置页面
import Settings from './pages/settings/Settings';

// 国际化配置
import './locales/i18n';

// 获取Ant Design的语言包
const getAntdLocale = (language: string) => {
  switch (language) {
    case 'zh':
      return zhCN;
    case 'en':
      return enUS;
    case 'ja':
      return jaJP;
    default:
      return zhCN;
  }
};

// 认证路由守卫
const ProtectedRoute = ({ children }: { children: React.ReactElement }) => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

function App() {
  const { i18n } = useTranslation();
  const { language } = useAppSelector((state) => state.settings);
  const [theme, _] = useTheme();
  // 使用自定义钩子更新文档标题
  useDocumentTitle();
  const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

  // 在应用启动时加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // 从服务器获取设置
        const settings = await import('./services/settingsService').then(module => module.getSettings());
        console.log('从服务器加载设置成功:', settings);

        // 导入并使用 Redux actions 更新设置
        const { setLanguage, setCurrency, setTimezone, setTheme, setSiteName, setSiteLogo } =
          await import('./store/slices/settingsSlice');
        const dispatch = await import('./store').then(module => module.store.dispatch);

        // 更新 Redux store 中的设置
        if (settings.language) dispatch(setLanguage(settings.language));
        if (settings.currency) dispatch(setCurrency(settings.currency));
        if (settings.timezone) dispatch(setTimezone(settings.timezone));
        if (settings.theme) dispatch(setTheme(settings.theme));
        if (settings.siteName) dispatch(setSiteName(settings.siteName));
        if (settings.siteLogo) dispatch(setSiteLogo(settings.siteLogo));
      } catch (error) {
        console.error('从服务器加载设置失败:', error);
      }
    };

    loadSettings();
  }, []);

  // 根据系统主题和用户设置确定实际主题
  const actualTheme = theme === 'system' ? (prefersDarkMode ? 'dark' : 'light') : theme;

  // 同步语言设置
  useEffect(() => {
    i18n.changeLanguage(language);
  }, [language, i18n]);

  // 配置Ant Design主题
  const themeConfig = {
    algorithm: actualTheme === 'dark' ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
    token: {
      colorPrimary: '#1677ff',
    },
    components: {
      Layout: {
        bodyBg: 'var(--background-color)',
        headerBg: 'var(--background-color)',
        siderBg: 'var(--background-color)',
        footerBg: 'var(--background-color)',
      },
      Menu: {
        darkItemBg: '#141414',
        darkItemColor: 'rgba(255, 255, 255, 0.85)',
        darkItemHoverBg: '#1f1f1f',
        darkItemSelectedBg: '#1668dc',
      },
      Card: {
        colorBgContainer: 'var(--background-color)',
        colorBorderSecondary: 'var(--border-color)',
      },
      Table: {
        colorBgContainer: 'var(--background-color)',
        colorBorderSecondary: 'var(--border-color)',
      },
      Select: {
        colorBgContainer: 'var(--background-color)',
        colorBorder: 'var(--border-color)',
      },
      Input: {
        colorBgContainer: 'var(--background-color)',
        colorBorder: 'var(--border-color)',
      },
      Button: {
        colorBgContainer: 'var(--background-color)',
        colorBorder: 'var(--border-color)',
      },
    },
  };

  return (
    <ConfigProvider
      locale={getAntdLocale(language)}
      theme={themeConfig}
    >
      {/* 使用App组件包装，解决message组件的静态方法无法使用动态主题的问题 */}
      <AntdApp>
        <Router>
          <Routes>
            {/* 认证路由 */}
            <Route path="/" element={<AuthLayout />}>
              <Route path="login" element={<Login />} />
              <Route index element={<Navigate to="/login" replace />} />
            </Route>

            {/* 主应用路由 */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              {/* 项目路由 */}
              <Route path="projects" element={<ProjectList />} />
              <Route path="projects/new" element={<NewProject />} />
              <Route path="projects/edit/:id" element={<EditProject />} />
              <Route path="projects/analysis/:id" element={<ProjectAnalysis />} />

              {/* 数据库路由 */}
              <Route path="databases">
                <Route path="irradiance" element={<IrradianceDatabase />} />
                <Route path="electricity-price" element={<ElectricityPriceDatabase />} />
                <Route path="suppliers" element={<SupplierDatabase />} />
                <Route path="equipment" element={<EquipmentDatabase />} />
                <Route index element={<Navigate to="/databases/irradiance" replace />} />
              </Route>

              {/* 设置路由 */}
              <Route path="settings" element={<Settings />} />

              {/* 默认重定向 */}
              <Route path="*" element={<Navigate to="/projects" replace />} />
            </Route>
          </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
