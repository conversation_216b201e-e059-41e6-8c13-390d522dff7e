/**
 * 验证工具函数
 */

/**
 * 验证电子邮箱格式
 * @param email 电子邮箱
 * @returns 是否有效
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度评级（0-4）
 */
export const getPasswordStrength = (password: string): number => {
  let strength = 0;
  
  // 长度检查
  if (password.length >= 8) strength += 1;
  
  // 包含小写字母
  if (/[a-z]/.test(password)) strength += 1;
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) strength += 1;
  
  // 包含数字
  if (/[0-9]/.test(password)) strength += 1;
  
  // 包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
  
  return strength;
};

/**
 * 验证手机号格式
 * @param phone 手机号
 * @param country 国家代码
 * @returns 是否有效
 */
export const isValidPhone = (phone: string, country: 'JP' | 'CN' | 'US' = 'JP'): boolean => {
  let phoneRegex: RegExp;
  
  switch (country) {
    case 'JP':
      // 日本手机号格式
      phoneRegex = /^(070|080|090)\d{8}$/;
      break;
    case 'CN':
      // 中国手机号格式
      phoneRegex = /^1[3-9]\d{9}$/;
      break;
    case 'US':
      // 美国手机号格式
      phoneRegex = /^\d{10}$/;
      break;
    default:
      phoneRegex = /^\d{10,15}$/;
  }
  
  return phoneRegex.test(phone.replace(/\D/g, ''));
};

/**
 * 验证URL格式
 * @param url URL
 * @returns 是否有效
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * 验证数字范围
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @returns 是否在范围内
 */
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

/**
 * 验证字符串长度
 * @param str 字符串
 * @param minLength 最小长度
 * @param maxLength 最大长度
 * @returns 是否在长度范围内
 */
export const isValidLength = (str: string, minLength: number, maxLength: number): boolean => {
  return str.length >= minLength && str.length <= maxLength;
};

/**
 * 验证是否为空
 * @param value 值
 * @returns 是否为空
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};
