/**
 * 本地存储工具函数
 */

// 存储前缀，避免命名冲突
const STORAGE_PREFIX = 'pv_storage_';

/**
 * 存储数据到localStorage
 * @param key 键名
 * @param value 值
 */
export const setLocalStorage = <T>(key: string, value: T): void => {
  try {
    const prefixedKey = STORAGE_PREFIX + key;
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(prefixedKey, serializedValue);
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

/**
 * 从localStorage获取数据
 * @param key 键名
 * @param defaultValue 默认值
 * @returns 存储的值或默认值
 */
export const getLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const prefixedKey = STORAGE_PREFIX + key;
    const serializedValue = localStorage.getItem(prefixedKey);
    if (serializedValue === null) {
      return defaultValue;
    }
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return defaultValue;
  }
};

/**
 * 从localStorage删除数据
 * @param key 键名
 */
export const removeLocalStorage = (key: string): void => {
  try {
    const prefixedKey = STORAGE_PREFIX + key;
    localStorage.removeItem(prefixedKey);
  } catch (error) {
    console.error('Error removing from localStorage:', error);
  }
};

/**
 * 清除所有localStorage数据
 */
export const clearLocalStorage = (): void => {
  try {
    // 只清除带有前缀的项
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(STORAGE_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }
};

/**
 * 存储数据到sessionStorage
 * @param key 键名
 * @param value 值
 */
export const setSessionStorage = <T>(key: string, value: T): void => {
  try {
    const prefixedKey = STORAGE_PREFIX + key;
    const serializedValue = JSON.stringify(value);
    sessionStorage.setItem(prefixedKey, serializedValue);
  } catch (error) {
    console.error('Error saving to sessionStorage:', error);
  }
};

/**
 * 从sessionStorage获取数据
 * @param key 键名
 * @param defaultValue 默认值
 * @returns 存储的值或默认值
 */
export const getSessionStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const prefixedKey = STORAGE_PREFIX + key;
    const serializedValue = sessionStorage.getItem(prefixedKey);
    if (serializedValue === null) {
      return defaultValue;
    }
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error('Error reading from sessionStorage:', error);
    return defaultValue;
  }
};

/**
 * 从sessionStorage删除数据
 * @param key 键名
 */
export const removeSessionStorage = (key: string): void => {
  try {
    const prefixedKey = STORAGE_PREFIX + key;
    sessionStorage.removeItem(prefixedKey);
  } catch (error) {
    console.error('Error removing from sessionStorage:', error);
  }
};

/**
 * 清除所有sessionStorage数据
 */
export const clearSessionStorage = (): void => {
  try {
    // 只清除带有前缀的项
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith(STORAGE_PREFIX)) {
        sessionStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
  }
};
