/**
 * 清除数据工具
 * 用于清除系统中的虚拟数据
 */
import { store } from '../store';
import { fetchProjectsSuccess } from '../store/slices/projectsSlice';
import { 
  fetchIrradianceDataSuccess, 
  fetchElectricityPricesSuccess, 
  fetchSuppliersSuccess, 
  fetchEquipmentSuccess,
  setCurrentIrradianceData
} from '../store/slices/databasesSlice';

// 清除localStorage中的项目数据
const clearProjectsLocalStorage = (): void => {
  try {
    // 清除所有以pv_storage_开头的项目数据
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_storage_')) {
        localStorage.removeItem(key);
      }
    });
    console.log('项目数据已从localStorage中清除');
  } catch (error) {
    console.error('清除项目数据失败:', error);
  }
};

// 清除localStorage中的光照数据
const clearIrradianceLocalStorage = (): void => {
  try {
    // 清除所有以pv_irradiance_开头的光照数据
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_irradiance_')) {
        localStorage.removeItem(key);
      }
    });
    console.log('光照数据已从localStorage中清除');
  } catch (error) {
    console.error('清除光照数据失败:', error);
  }
};

// 清除Redux store中的项目数据
const clearProjectsReduxStore = (): void => {
  try {
    // 将项目列表设置为空数组
    store.dispatch(fetchProjectsSuccess([]));
    console.log('项目数据已从Redux store中清除');
  } catch (error) {
    console.error('清除Redux store中的项目数据失败:', error);
  }
};

// 清除Redux store中的数据库数据
const clearDatabasesReduxStore = (): void => {
  try {
    // 将各种数据库数据设置为空数组
    store.dispatch(fetchIrradianceDataSuccess([]));
    store.dispatch(fetchElectricityPricesSuccess([]));
    store.dispatch(fetchSuppliersSuccess([]));
    store.dispatch(fetchEquipmentSuccess([]));
    store.dispatch(setCurrentIrradianceData(null));
    console.log('数据库数据已从Redux store中清除');
  } catch (error) {
    console.error('清除Redux store中的数据库数据失败:', error);
  }
};

/**
 * 清除所有虚拟数据
 * 包括项目库和光照数据库的数据
 */
export const clearAllData = (): void => {
  try {
    // 清除localStorage中的数据
    clearProjectsLocalStorage();
    clearIrradianceLocalStorage();
    
    // 清除Redux store中的数据
    clearProjectsReduxStore();
    clearDatabasesReduxStore();
    
    console.log('所有虚拟数据已清除');
  } catch (error) {
    console.error('清除虚拟数据失败:', error);
    throw new Error('清除虚拟数据失败');
  }
};

/**
 * 清除项目库数据
 */
export const clearProjectsData = (): void => {
  clearProjectsLocalStorage();
  clearProjectsReduxStore();
};

/**
 * 清除光照数据库数据
 */
export const clearIrradianceData = (): void => {
  clearIrradianceLocalStorage();
  // 只清除光照数据部分
  try {
    store.dispatch(fetchIrradianceDataSuccess([]));
    store.dispatch(setCurrentIrradianceData(null));
  } catch (error) {
    console.error('清除Redux store中的光照数据失败:', error);
  }
};
