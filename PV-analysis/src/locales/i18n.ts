import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 导入语言文件
import enTranslation from './en.json';
import jaTranslation from './ja.json';
import zhTranslation from './zh.json';

// 配置i18next
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslation
      },
      ja: {
        translation: jaTranslation
      },
      zh: {
        translation: zhTranslation
      }
    },
    lng: 'zh', // 默认语言
    fallbackLng: 'en', // 回退语言
    interpolation: {
      escapeValue: false // 不转义HTML
    }
  });

export default i18n;
