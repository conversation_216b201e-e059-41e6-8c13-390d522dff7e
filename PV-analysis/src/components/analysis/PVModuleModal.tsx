import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, Button, Row, Col, Tooltip, message } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { PVModule } from '../../types/project';
import { useAppSelector, useAppDispatch } from '../../store';
import { fetchDataStart, fetchEquipmentSuccess } from '../../store/slices/databasesSlice';
import { getEquipmentList } from '../../services/equipmentService';

const { Option } = Select;

interface PVModuleModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (module: PVModule) => void;
  editingModule?: PVModule | null;
}

/**
 * 光伏组件添加/编辑模态框
 */
const PVModuleModal: React.FC<PVModuleModalProps> = ({
  visible,
  onCancel,
  onSave,
  editingModule
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const { equipment } = useAppSelector((state) => state.databases);
  const [selectedEquipment, setSelectedEquipment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 过滤设备列表，只显示光伏设备
  const pvEquipment = equipment
    .filter(item => item.type === 'pv')
    .map(item => ({
      ...item,
      specs: item.specs || {}
    }));

  // 获取设备列表
  const fetchEquipmentData = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getEquipmentList();
      dispatch(fetchEquipmentSuccess(response.items));
      console.log('PVModuleModal: 成功获取设备列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('PVModuleModal: 获取设备列表失败:', error);
      message.error(t('common.fetchDataFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // 当模态框可见时，获取设备数据
  useEffect(() => {
    if (visible) {
      // 如果Redux store中没有数据，则从服务器获取
      if (equipment.length === 0) {
        console.log('PVModuleModal: Redux store中没有设备数据，从服务器获取');
        fetchEquipmentData();
      } else {
        console.log('PVModuleModal: Redux store中已有设备数据，数据条数:', equipment.length);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 当编辑模式或可见性变化时，重置表单
  useEffect(() => {
    if (visible) {
      if (editingModule) {
        // 编辑模式，填充表单
        form.setFieldsValue({
          name: editingModule.name,
          manufacturer: editingModule.manufacturer,
          model: editingModule.model,
          power: editingModule.power,
          efficiency: editingModule.efficiency,
          area: editingModule.area,
          price: editingModule.price,
          quantity: editingModule.quantity,
          angle: editingModule.angle,
          orientation: editingModule.orientation,
          orientationAngle: editingModule.orientationAngle || 0,
        });
      } else {
        // 新增模式，重置表单
        form.resetFields();
        form.setFieldsValue({
          angle: 30,
          orientation: 'south',
          orientationAngle: 0,
          quantity: 1
        });
      }
    }
  }, [visible, editingModule, form]);

  // 处理设备选择
  const handleEquipmentSelect = (equipmentId: string) => {
    const selected = equipment.find(item => item.id === equipmentId);
    if (selected) {
      // 确保specs字段存在
      const specs = selected.specs || {};
      setSelectedEquipment(selected);

      // 填充表单
      form.setFieldsValue({
        name: selected.name || '',
        manufacturer: selected.manufacturer || '',
        model: selected.model || '',
        power: specs.power || 0,
        efficiency: specs.efficiency || 0,
        area: specs.area || 0,
        price: selected.price || 0,
        quantity: 1,
        angle: 30,
        orientation: 'south',
        orientationAngle: 0,
      });
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 准备保存的模块数据
      const moduleData: PVModule = {
        id: editingModule?.id || Date.now().toString(),
        ...values
      };

      onSave(moduleData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={editingModule ? t('pvModules.editModule') : t('pvModules.addModule')}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('common.cancel')}
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {t('common.confirm')}
        </Button>
      ]}
      width={800}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <div style={{ marginBottom: 24 }}>
          <Form.Item
            name="equipmentId"
            label={t('pvModules.selectEquipment')}
          >
            <Select
              placeholder={t('pvModules.selectEquipmentPlaceholder')}
              onChange={handleEquipmentSelect}
              allowClear
              showSearch
              loading={isLoading}
              filterOption={(input, option) =>
                (option?.children as unknown as string).toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {pvEquipment.length > 0 ? (
                pvEquipment.map(item => (
                  <Option key={item.id} value={item.id}>{item.name} - {item.manufacturer}</Option>
                ))
              ) : (
                <Option value="" disabled>{t('common.noData')}</Option>
              )}
            </Select>
          </Form.Item>
        </div>

        <h3>{t('pvModules.basicInfo')}</h3>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={t('pvModules.name')}
              rules={[{ required: true, message: t('pvModules.nameRequired') }]}
            >
              <Input placeholder={t('pvModules.name')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="manufacturer"
              label={t('pvModules.manufacturer')}
              rules={[{ required: true, message: t('pvModules.manufacturerRequired') }]}
            >
              <Input placeholder={t('pvModules.manufacturer')} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="model"
              label={t('pvModules.model')}
              rules={[{ required: true, message: t('pvModules.modelRequired') }]}
            >
              <Input placeholder={t('pvModules.model')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="quantity"
              label={t('pvModules.quantity')}
              rules={[{ required: true, message: t('pvModules.quantityRequired') }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <h3>{t('pvModules.specifications')}</h3>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="power"
              label={t('pvModules.power') + ' (W)'}
              rules={[{ required: true, message: t('pvModules.powerRequired') }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="efficiency"
              label={t('pvModules.efficiency') + ' (%)'}
              rules={[{ required: true, message: t('pvModules.efficiencyRequired') }]}
            >
              <InputNumber min={0} max={100} step={0.1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="area"
              label={t('pvModules.area') + ' (m²)'}
              rules={[{ required: true, message: t('pvModules.areaRequired') }]}
            >
              <InputNumber min={0} step={0.01} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="price"
              label={t('pvModules.price') + ' (JPY)'}
              rules={[{ required: true, message: t('pvModules.priceRequired') }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="angle"
              label={
                <span>
                  {t('pvModules.angle')} (°)
                  <Tooltip title={t('pvModules.angleTooltip')}>
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
              rules={[{ required: true, message: t('pvModules.angleRequired') }]}
            >
              <InputNumber min={0} max={90} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="orientation"
              label={t('pvModules.orientation')}
              rules={[{ required: true, message: t('pvModules.orientationRequired') }]}
            >
              <Select placeholder={t('pvModules.selectOrientation')}>
                <Option value="south">{t('pvModules.south')}</Option>
                <Option value="north">{t('pvModules.north')}</Option>
                <Option value="east">{t('pvModules.east')}</Option>
                <Option value="west">{t('pvModules.west')}</Option>
                <Option value="southeast">{t('pvModules.southeast')}</Option>
                <Option value="southwest">{t('pvModules.southwest')}</Option>
                <Option value="northeast">{t('pvModules.northeast')}</Option>
                <Option value="northwest">{t('pvModules.northwest')}</Option>
                <Option value="custom">{t('pvModules.custom')}</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="orientationAngle"
              label={
                <span>
                  {t('pvModules.orientationAngle')} (°)
                  <Tooltip title={t('pvModules.orientationAngleHelp')}>
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              }
            >
              <InputNumber min={-180} max={180} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default PVModuleModal;
