import React, { useState, useEffect } from 'react';
import { Card, Table, Empty, Row, Col, Statistic, Checkbox, Space, Select, Divider, Button, Modal, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { ProjectData, ProjectDailyData, ProjectMonthlyData } from '../../types/projectData';
import { formatCurrency, formatNumber } from '../../utils';
import StorageAnalysisChart, { StorageMetric } from './StorageAnalysisChart';
import { ElectricityPrice, PriceRule, SeasonalPricePolicy } from '../../types/database';
import { getElectricityPriceDetail } from '../../services/electricityPriceService';
import { EnergyStorage } from '../../types/project';
import StorageDeviceModal from './StorageDeviceModal';
import StatisticWithTooltip from '../../components/common/StatisticWithTooltip';

interface StorageAnalysisTabProps {
  project: ProjectData;
  onAddDevice?: (device: EnergyStorage) => void;
  onEditDevice?: (device: EnergyStorage) => void;
  onDeleteDevice?: (deviceId: string) => void;
}

const { Option } = Select;

/**
 * 储能分析标签页组件
 */
const StorageAnalysisTab: React.FC<StorageAnalysisTabProps> = ({
  project,
  onAddDevice,
  onEditDevice,
  onDeleteDevice
}) => {
  const { t } = useTranslation();
  const [electricityPrice, setElectricityPrice] = useState<ElectricityPrice | null>(null);
  const [visualizationOptions, setVisualizationOptions] = useState({
    viewMode: 'hourly' as 'hourly' | 'daily' | 'monthly',
    month: new Date().getMonth() + 1, // 默认当前月
    day: new Date().getDate(), // 默认当前日
    metrics: ['storageCharge', 'storageDischarge'] as StorageMetric[] // 默认选中的指标
  });
  const [dailyData, setDailyData] = useState<ProjectDailyData[]>([]);
  const [monthlyData, setMonthlyData] = useState<ProjectMonthlyData[]>([]);

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDevice, setEditingDevice] = useState<EnergyStorage | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [deviceToDelete, setDeviceToDelete] = useState<string | null>(null);

  console.log('StorageAnalysisTab - 开始渲染，项目ID:', project.id);
  console.log('StorageAnalysisTab - 储能设备数量:', project.energyStorage.length);

  // 检查hourlyData的结构
  const hourlyData = project.analysisResults?.hourlyData || [];
  console.log('StorageAnalysisTab - 小时数据点数量:', hourlyData.length);

  // 获取电价政策
  useEffect(() => {
    const fetchElectricityPrice = async () => {
      try {
        if (project.electricityPriceId) {
          const priceData = await getElectricityPriceDetail(project.electricityPriceId);
          setElectricityPrice(priceData);
          console.log('StorageAnalysisTab - 获取电价政策成功:', priceData.name);
        }
      } catch (error) {
        console.error('StorageAnalysisTab - 获取电价政策失败:', error);
      }
    };

    fetchElectricityPrice();
  }, [project.electricityPriceId]);

  // 计算日数据和月数据
  useEffect(() => {
    if (hourlyData.length > 0) {
      // 计算日数据
      const dailyMap = new Map<string, ProjectDailyData>();

      hourlyData.forEach(hour => {
        const key = `${hour.month}-${hour.day}`;
        const pvTotal = Object.values(hour.pvGeneration).reduce((sum, val) => sum + (val || 0), 0);

        if (!dailyMap.has(key)) {
          dailyMap.set(key, {
            day: hour.day,
            month: hour.month,
            pvGeneration: 0,
            storageCharge: 0,
            storageDischarge: 0,
            electricityConsumption: 0,
            gridExport: 0,
            gridImport: 0,
            gridExportIncome: 0,
            pvBenefit: 0,
            storageBenefit: 0,
            totalBenefit: 0
          });
        }

        const dailyData = dailyMap.get(key)!;

        // 累加数据
        dailyData.pvGeneration += pvTotal;
        dailyData.electricityConsumption += hour.electricityConsumption;
        dailyData.gridExport += hour.gridExport;
        dailyData.gridImport += hour.gridImport;

        // 处理充放电
        if (hour.storageCharge > 0) {
          dailyData.storageCharge += hour.storageCharge;
        } else if (hour.storageCharge < 0) {
          dailyData.storageDischarge += Math.abs(hour.storageCharge);
        }
      });

      setDailyData(Array.from(dailyMap.values()));

      // 计算月数据
      const monthlyMap = new Map<number, ProjectMonthlyData>();

      dailyMap.forEach((daily) => {
        if (!monthlyMap.has(daily.month)) {
          monthlyMap.set(daily.month, {
            month: daily.month,
            pvGeneration: 0,
            storageCharge: 0,
            storageDischarge: 0,
            electricityConsumption: 0,
            gridExport: 0,
            gridImport: 0,
            gridExportIncome: 0,
            pvBenefit: 0,
            storageBenefit: 0,
            totalBenefit: 0
          });
        }

        const monthlyData = monthlyMap.get(daily.month)!;

        // 累加数据
        monthlyData.pvGeneration += daily.pvGeneration;
        monthlyData.storageCharge += daily.storageCharge;
        monthlyData.storageDischarge += daily.storageDischarge;
        monthlyData.electricityConsumption += daily.electricityConsumption;
        monthlyData.gridExport += daily.gridExport;
        monthlyData.gridImport += daily.gridImport;
      });

      setMonthlyData(Array.from(monthlyMap.values()));
    }
  }, [hourlyData]);

  // 处理指标选择变化
  const handleMetricsChange = (metrics: StorageMetric[]) => {
    setVisualizationOptions(prev => ({
      ...prev,
      metrics
    }));
  };

  // 处理视图模式变化
  const handleViewModeChange = (viewMode: 'hourly' | 'daily' | 'monthly') => {
    setVisualizationOptions(prev => ({
      ...prev,
      viewMode
    }));
  };

  // 处理月份选择变化
  const handleMonthChange = (month: number) => {
    setVisualizationOptions(prev => ({
      ...prev,
      month
    }));
  };

  // 处理日期选择变化
  const handleDayChange = (day: number) => {
    setVisualizationOptions(prev => ({
      ...prev,
      day
    }));
  };

  // 处理添加设备
  const handleAddDevice = () => {
    setEditingDevice(null);
    setModalVisible(true);
  };

  // 处理编辑设备
  const handleEditDevice = (device: EnergyStorage) => {
    setEditingDevice(device);
    setModalVisible(true);
  };

  // 处理删除设备
  const handleDeleteDevice = (deviceId: string) => {
    setDeviceToDelete(deviceId);
    setDeleteConfirmVisible(true);
  };

  // 确认删除设备
  const confirmDeleteDevice = () => {
    if (deviceToDelete && onDeleteDevice) {
      onDeleteDevice(deviceToDelete);
      message.success(t('energyStorage.deleteSuccess'));
    }
    setDeleteConfirmVisible(false);
    setDeviceToDelete(null);
  };

  // 保存设备
  const handleSaveDevice = (device: EnergyStorage) => {
    if (editingDevice) {
      // 编辑现有设备
      if (onEditDevice) {
        console.log('StorageAnalysisTab - 编辑设备:', device);
        onEditDevice(device);
        message.success(t('energyStorage.editSuccess'));
      }
    } else {
      // 添加新设备
      if (onAddDevice) {
        console.log('StorageAnalysisTab - 添加新设备:', device);
        onAddDevice(device);
        message.success(t('energyStorage.addSuccess'));
      }
    }
    setModalVisible(false);
    setEditingDevice(null);
  };

  // 渲染可视化选项
  const renderVisualizationOptions = () => {
    // 获取可用的月份
    const availableMonths = Array.from(new Set(hourlyData.map(h => h.month))).sort((a, b) => a - b);

    // 获取选定月份的可用日期
    const availableDays = Array.from(
      new Set(
        hourlyData
          .filter(h => h.month === visualizationOptions.month)
          .map(h => h.day)
      )
    ).sort((a, b) => a - b);

    return (
      <Card title={t('chart.visualizationOptions')} style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Space>
              <span>{t('chart.viewMode')}:</span>
              <Select
                value={visualizationOptions.viewMode}
                onChange={handleViewModeChange}
                style={{ width: 120 }}
              >
                <Option value="hourly">{t('chart.hourly')}</Option>
                <Option value="daily">{t('chart.daily')}</Option>
                <Option value="monthly">{t('chart.monthly')}</Option>
              </Select>

              {visualizationOptions.viewMode !== 'monthly' && (
                <>
                  <span>{t('chart.month')}:</span>
                  <Select
                    value={visualizationOptions.month}
                    onChange={handleMonthChange}
                    style={{ width: 100 }}
                  >
                    {availableMonths.map(month => (
                      <Option key={month} value={month}>{month}{t('chart.monthUnit')}</Option>
                    ))}
                  </Select>
                </>
              )}

              {visualizationOptions.viewMode === 'hourly' && (
                <>
                  <span>{t('chart.day')}:</span>
                  <Select
                    value={visualizationOptions.day}
                    onChange={handleDayChange}
                    style={{ width: 100 }}
                  >
                    {availableDays.map(day => (
                      <Option key={day} value={day}>{day}{t('chart.dayUnit')}</Option>
                    ))}
                  </Select>
                </>
              )}
            </Space>
          </Col>

          <Col span={24}>
            <div>{t('chart.metrics')}:</div>
            <Checkbox.Group
              value={visualizationOptions.metrics}
              onChange={handleMetricsChange as any}
              style={{ display: 'flex', flexWrap: 'wrap' }}
            >
              <Row gutter={[16, 8]}>
                <Col span={8}>
                  <Checkbox value="storageCharge">{t('chart.storageCharge')}</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="storageDischarge">{t('chart.storageDischarge')}</Checkbox>
                </Col>

                <Col span={8}>
                  <Checkbox value="pvGeneration">{t('chart.pvGeneration')}</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="electricityConsumption">{t('chart.electricityConsumption')}</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="gridExport">{t('chart.gridExport')}</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="gridImport">{t('chart.gridImport')}</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="storageBenefit">{t('chart.storageBenefit')}</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="cumulativeStorageBenefit">{t('chart.cumulativeStorageBenefit')}</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染储能设备列表
  const renderStorageList = () => {
    const columns = [
      {
        title: t('energyStorage.name'),
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: t('energyStorage.manufacturer'),
        dataIndex: 'manufacturer',
        key: 'manufacturer',
      },
      {
        title: t('energyStorage.model'),
        dataIndex: 'model',
        key: 'model',
      },
      {
        title: t('energyStorage.capacity') + ' (kWh)',
        dataIndex: 'capacity',
        key: 'capacity',
      },
      {
        title: t('energyStorage.power') + ' (kW)',
        dataIndex: 'power',
        key: 'power',
      },
      {
        title: t('energyStorage.efficiency') + ' (%)',
        dataIndex: 'efficiency',
        key: 'efficiency',
      },
      {
        title: t('energyStorage.quantity'),
        dataIndex: 'quantity',
        key: 'quantity',
      },
      {
        title: t('energyStorage.totalCapacity') + ' (kWh)',
        key: 'totalCapacity',
        render: (_: any, record: any) => formatNumber(record.capacity * record.quantity, 1),
      },
      {
        title: t('energyStorage.totalPower') + ' (kW)',
        key: 'totalPower',
        render: (_: any, record: any) => formatNumber(record.power * record.quantity, 1),
      },
      {
        title: t('common.operation'),
        key: 'operation',
        width: 120,
        render: (_: any, record: EnergyStorage) => (
          <Space size="small">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditDevice(record)}
              title={t('common.edit')}
            />
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteDevice(record.id)}
              title={t('common.delete')}
            />
          </Space>
        ),
      },
    ];

    return (
      <Card
        title={t('energyStorage.storageList')}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddDevice}
          >
            {t('energyStorage.addDevice')}
          </Button>
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          rowKey="id"
          columns={columns}
          dataSource={project.energyStorage}
          pagination={false}
          size="small"
        />
      </Card>
    );
  };

  // 获取特定时间点的电价
  const getHourlyPrice = (hour: number, day: number, month: number, electricityPrice: ElectricityPrice): { price: number, gridFeedInPrice: number } => {
    try {
      // 默认价格
      let price = 0;
      let gridFeedInPrice = 0;

      // 时间格式化
      const timeStr = `${hour.toString().padStart(2, '0')}:00`;

      // 根据电价政策类型获取价格
      if (electricityPrice.policyType === 'fixed') {
        // 固定电价
        const rule = findMatchingRule(timeStr, electricityPrice.rules);
        if (rule) {
          price = rule.price;
          gridFeedInPrice = rule.gridFeedInPrice || 0;
        }
      } else {
        // 季节性电价
        const seasonPolicy = findMatchingSeason(month, electricityPrice.seasonalPolicies);
        if (seasonPolicy) {
          const rule = findMatchingRule(timeStr, seasonPolicy.rules);
          if (rule) {
            price = rule.price;
            gridFeedInPrice = rule.gridFeedInPrice || 0;
          }
        }
      }

      return { price, gridFeedInPrice };
    } catch (error) {
      console.error('获取电价时出错:', error);
      return { price: 0, gridFeedInPrice: 0 };
    }
  };

  // 查找匹配的时间规则
  const findMatchingRule = (timeStr: string, rules: PriceRule[]): PriceRule | undefined => {
    return rules.find(rule => {
      const start = rule.startTime;
      const end = rule.endTime;

      // 处理跨天的情况
      if (start < end) {
        return timeStr >= start && timeStr < end;
      } else {
        return timeStr >= start || timeStr < end;
      }
    });
  };

  // 查找匹配的季节
  const findMatchingSeason = (month: number, seasons: SeasonalPricePolicy[]): SeasonalPricePolicy | undefined => {
    return seasons.find(season => season.months.includes(month));
  };

  // 渲染储能统计数据
  const renderStorageStatistics = () => {
    console.log('StorageAnalysisTab - 开始计算储能统计数据');
    const hourlyData = project.analysisResults?.hourlyData || [];
    console.log('StorageAnalysisTab - 用于计算的小时数据点数量:', hourlyData.length);

    // 计算总充电量（正值）和总放电量（负值）
    let totalChargeAmount = 0;
    let totalDischargeAmount = 0;
    let processedHours = 0;
    let chargeHours = 0;
    let dischargeHours = 0;

    for (const hour of hourlyData) {
      if (hour.storageCharge > 0) {
        totalChargeAmount += hour.storageCharge;
        chargeHours++;

        // 只记录前几个充电小时的详细日志
        if (chargeHours <= 3) {
          console.log(`StorageAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，充电量:`, hour.storageCharge, 'kWh');
        }
      } else if (hour.storageCharge < 0) {
        const dischargeAmount = Math.abs(hour.storageCharge);
        totalDischargeAmount += dischargeAmount;
        dischargeHours++;

        // 只记录前几个放电小时的详细日志
        if (dischargeHours <= 3) {
          console.log(`StorageAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，放电量:`, dischargeAmount, 'kWh');
        }
      }

      // 记录储能容量
      if (processedHours < 3) {
        console.log(`StorageAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，储能容量:`, hour.storageCapacity, 'kWh');
      }

      processedHours++;
    }

    console.log('StorageAnalysisTab - 处理完成的小时数:', processedHours);
    console.log('StorageAnalysisTab - 充电小时数:', chargeHours);
    console.log('StorageAnalysisTab - 放电小时数:', dischargeHours);
    console.log('StorageAnalysisTab - 总充电量:', totalChargeAmount, 'kWh');
    console.log('StorageAnalysisTab - 总放电量:', totalDischargeAmount, 'kWh');

    // 计算储能效率
    const storageEfficiency = totalChargeAmount > 0 ?
      (totalDischargeAmount / totalChargeAmount) * 100 : 0;
    console.log('StorageAnalysisTab - 储能效率计算:', totalDischargeAmount, '/', totalChargeAmount, '*100 =', storageEfficiency, '%');

    // 计算容量利用率 = 年充电量/储能容量/365天
    // 计算总储能容量
    const totalStorageCapacity = project.energyStorage.reduce((sum, storage) =>
      sum + storage.capacity * storage.quantity, 0);
    const capacityUtilizationRate = totalStorageCapacity > 0 ?
      (totalChargeAmount / totalStorageCapacity / 365) * 100 : 0;
    console.log('StorageAnalysisTab - 容量利用率计算:', totalChargeAmount, '/', totalStorageCapacity, '/365*100 =', capacityUtilizationRate, '%');

    // 计算储能收益（使用小时级别的实际收益累加）
    // 从小时数据中累加实际的储能收益
    let storageBenefit = 0;

    // 使用与图表相同的计算方法计算储能收益
    // 从月视图图表中获取12月的累计收益值
    // 这是最准确的方法，因为图表已经正确计算了累计收益

    // 计算每小时的储能收益并累加
    let cumulativeBenefit = 0;

    if (electricityPrice) {
      for (const hour of hourlyData) {
        // 计算每小时的储能收益
        let hourlyBenefit = 0;
        if (hour.storageCharge > 0) {
          // 充电时，收益为负（使用上网电价）
          const gridFeedInPrice = getHourlyPrice(hour.hour, hour.day, hour.month, electricityPrice).gridFeedInPrice;
          hourlyBenefit = -gridFeedInPrice * hour.storageCharge;
        } else if (hour.storageCharge < 0) {
          // 放电时，收益为正（使用用电价格）
          const price = getHourlyPrice(hour.hour, hour.day, hour.month, electricityPrice).price;
          hourlyBenefit = price * Math.abs(hour.storageCharge);
        }
        cumulativeBenefit += hourlyBenefit;
      }

      // 使用累计收益作为总收益
      storageBenefit = cumulativeBenefit;
      console.log('StorageAnalysisTab - 储能收益计算（使用实际电价累计）:', storageBenefit, '日元');
    } else {
      // 如果没有电价数据，使用固定的峰谷电价差计算
      // 这是一个备选方案，通常不会执行到这里
      const peakValleyPriceDiff = 20; // 日元/kWh，使用更合理的峰谷电价差
      storageBenefit = totalDischargeAmount * peakValleyPriceDiff;
      console.log('StorageAnalysisTab - 储能收益计算（使用固定峰谷差）:', storageBenefit, '日元');
    }

    // 计算储能投资成本
    const energyStorageCost = project.energyStorage.reduce((sum, storage) =>
      sum + storage.price * storage.quantity, 0);

    // 计算储能年收益率 = 储能年收益/储能设备投资
    const storageReturnRate = energyStorageCost > 0 ? (storageBenefit / energyStorageCost) * 100 : 0;

    return (
      <Card title={t('analysis.storageStatistics')}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.annualStorageCharge')}
              tooltip={t('analysis.tooltips.annualStorageCharge')}
              value={totalChargeAmount}
              precision={1}
              suffix="kWh"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.annualStorageDischarge')}
              tooltip={t('analysis.tooltips.annualStorageDischarge')}
              value={totalDischargeAmount}
              precision={1}
              suffix="kWh"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.storageEfficiency')}
              tooltip={t('analysis.tooltips.storageEfficiency')}
              value={storageEfficiency}
              precision={1}
              suffix="%"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.storageBenefit')}
              tooltip={t('analysis.tooltips.storageBenefit')}
              value={storageBenefit}
              precision={1}
              prefix="JPY "
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.storageReturnRate')}
              tooltip={t('analysis.tooltips.storageReturnRate')}
              value={storageReturnRate}
              precision={1}
              suffix="%"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.capacityUtilizationRate')}
              tooltip={t('analysis.tooltips.capacityUtilizationRate')}
              value={capacityUtilizationRate}
              precision={1}
              suffix="%"
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染图表
  const renderChart = () => {
    return (
      <StorageAnalysisChart
        data={{
          hourlyData,
          dailyData,
          monthlyData,
          electricityPrice
        }}
        options={visualizationOptions}
        onOptionsChange={(newOptions) => setVisualizationOptions(prev => ({ ...prev, ...newOptions }))}
      />
    );
  };

  return (
    <div>
      {renderStorageList()}

      {project.energyStorage.length > 0 ? (
        <>
          {renderStorageStatistics()}
          <Divider />
          {renderVisualizationOptions()}
          {renderChart()}
        </>
      ) : (
        <Empty
          description={t('energyStorage.noStorage')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}

      {/* 添加/编辑设备模态框 */}
      <StorageDeviceModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSave={handleSaveDevice}
        editingDevice={editingDevice}
      />

      {/* 删除确认对话框 */}
      <Modal
        title={t('energyStorage.confirmDelete')}
        open={deleteConfirmVisible}
        onOk={confirmDeleteDevice}
        onCancel={() => setDeleteConfirmVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <p>{t('energyStorage.deleteConfirmMessage')}</p>
      </Modal>
    </div>
  );
};

export default StorageAnalysisTab;
