import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, InputNumber, Select, Button, Row, Col, Tooltip, message } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { EnergyStorage } from '../../types/project';
import { useAppSelector, useAppDispatch } from '../../store';
import { fetchDataStart, fetchEquipmentSuccess } from '../../store/slices/databasesSlice';
import { getEquipmentList } from '../../services/equipmentService';

const { Option } = Select;

interface StorageDeviceModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (device: EnergyStorage) => void;
  editingDevice?: EnergyStorage | null;
}

/**
 * 储能设备添加/编辑模态框
 */
const StorageDeviceModal: React.FC<StorageDeviceModalProps> = ({
  visible,
  onCancel,
  onSave,
  editingDevice
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const { equipment } = useAppSelector((state) => state.databases);
  const [selectedEquipment, setSelectedEquipment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 过滤设备列表，只显示储能设备
  const storageEquipment = equipment
    .filter(item => item.type === 'storage')
    .map(item => ({
      ...item,
      specs: item.specs || {}
    }));

  // 获取设备列表
  const fetchEquipmentData = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getEquipmentList();
      dispatch(fetchEquipmentSuccess(response.items));
      console.log('StorageDeviceModal: 成功获取设备列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('StorageDeviceModal: 获取设备列表失败:', error);
      message.error(t('common.fetchDataFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // 当模态框可见时，获取设备数据
  useEffect(() => {
    if (visible) {
      // 如果Redux store中没有数据，则从服务器获取
      if (equipment.length === 0) {
        console.log('StorageDeviceModal: Redux store中没有设备数据，从服务器获取');
        fetchEquipmentData();
      } else {
        console.log('StorageDeviceModal: Redux store中已有设备数据，数据条数:', equipment.length);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 组件挂载或编辑设备变更时，重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();

      if (editingDevice) {
        // 如果是编辑模式，填充表单
        form.setFieldsValue({
          name: editingDevice.name,
          manufacturer: editingDevice.manufacturer,
          model: editingDevice.model,
          capacity: editingDevice.capacity,
          power: editingDevice.power,
          efficiency: editingDevice.efficiency,
          cycles: editingDevice.cycles,
          price: editingDevice.price,
          quantity: editingDevice.quantity,
        });
      } else {
        // 如果是新增模式，设置默认值
        form.setFieldsValue({
          efficiency: 93,
          quantity: 1,
        });
      }
    }
  }, [visible, editingDevice, form]);

  // 处理设备选择
  const handleEquipmentSelect = (equipmentId: string) => {
    const selected = equipment.find(item => item.id === equipmentId);
    if (selected) {
      // 确保specs字段存在
      const specs = selected.specs || {};
      setSelectedEquipment(selected);

      // 填充表单
      form.setFieldsValue({
        name: selected.name || '',
        manufacturer: selected.manufacturer || '',
        model: selected.model || '',
        capacity: specs.capacity || 0,
        power: specs.power || 0,
        efficiency: specs.efficiency || 93,
        cycles: specs.cycles || 0,
        price: selected.price || 0,
        quantity: 1,
      });
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 准备保存的设备数据
      const deviceData: EnergyStorage = {
        id: editingDevice?.id || Date.now().toString(),
        ...values
      };

      onSave(deviceData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={editingDevice ? t('energyStorage.editDevice') : t('energyStorage.addDevice')}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('common.cancel')}
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          {t('common.confirm')}
        </Button>
      ]}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="equipmentId"
              label={t('energyStorage.selectEquipment')}
            >
              <Select
                placeholder={t('energyStorage.selectEquipmentPlaceholder')}
                onChange={handleEquipmentSelect}
                allowClear
                loading={isLoading}
                style={{ width: '100%' }}
              >
                {storageEquipment.length > 0 ? (
                  storageEquipment.map(item => (
                    <Option key={item.id} value={item.id}>
                      {item.name} - {item.manufacturer} {item.model}
                    </Option>
                  ))
                ) : (
                  <Option value="" disabled>{t('common.noData')}</Option>
                )}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="name"
              label={t('energyStorage.name')}
              rules={[{ required: true, message: t('energyStorage.nameRequired') }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="manufacturer"
              label={t('energyStorage.manufacturer')}
              rules={[{ required: true, message: t('energyStorage.manufacturerRequired') }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="model"
              label={t('energyStorage.model')}
              rules={[{ required: true, message: t('energyStorage.modelRequired') }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="capacity"
              label={t('energyStorage.capacity') + ' (kWh)'}
              rules={[{ required: true, message: t('energyStorage.capacityRequired') }]}
            >
              <InputNumber min={0} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="power"
              label={t('energyStorage.power') + ' (kW)'}
              rules={[{ required: true, message: t('energyStorage.powerRequired') }]}
            >
              <InputNumber min={0} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="efficiency"
              label={t('energyStorage.efficiency') + ' (%)'}
              rules={[{ required: true, message: t('energyStorage.efficiencyRequired') }]}
            >
              <InputNumber min={0} max={100} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="cycles"
              label={t('energyStorage.cycles')}
              rules={[{ required: true, message: t('energyStorage.cyclesRequired') }]}
            >
              <InputNumber min={0} step={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="price"
              label={t('energyStorage.price') + ' (JPY)'}
              rules={[{ required: true, message: t('energyStorage.priceRequired') }]}
            >
              <InputNumber min={0} step={1000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="quantity"
              label={t('energyStorage.quantity')}
              rules={[{ required: true, message: t('energyStorage.quantityRequired') }]}
            >
              <InputNumber min={1} step={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default StorageDeviceModal;
