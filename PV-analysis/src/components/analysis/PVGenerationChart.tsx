import React, { useState, useEffect } from 'react';
import { Card, Empty, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { ProjectHourlyData, ProjectDailyData, ProjectMonthlyData } from '../../types/projectData';
import { formatNumber } from '../../utils';

interface PVGenerationChartProps {
  data: {
    pvGeneration: number;
    pvBenefit: number;
    monthlyData: ProjectMonthlyData[];
    dailyData: ProjectDailyData[];
    hourlyData: ProjectHourlyData[];
  } | null;
  options: {
    metric: 'generation' | 'income';
    viewMode: 'hourly' | 'daily' | 'monthly';
    month: number;
    day: number;
  };
}

/**
 * 光伏发电图表组件
 */
const PVGenerationChart: React.FC<PVGenerationChartProps> = ({ data, options }) => {
  const { t } = useTranslation();
  const [chartOptions, setChartOptions] = useState<EChartsOption | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [chartKey, setChartKey] = useState<number>(0);

  // 当数据或选项变化时，更新图表
  useEffect(() => {
    if (!data) {
      setChartOptions(null);
      return;
    }

    try {
      setError(null);
      const { metric, viewMode, month, day } = options;

      // 根据视图模式和指标生成不同的图表
      if (viewMode === 'monthly') {
        if (metric === 'generation') {
          generateMonthlyGenerationChart(data.monthlyData);
        } else {
          generateMonthlyIncomeChart(data.monthlyData);
        }
      } else if (viewMode === 'daily') {
        if (metric === 'generation') {
          generateDailyGenerationChart(data.dailyData, month);
        } else {
          generateDailyIncomeChart(data.dailyData, month);
        }
      } else if (viewMode === 'hourly') {
        if (metric === 'generation') {
          generateHourlyGenerationChart(data.hourlyData, month, day);
        } else {
          generateHourlyIncomeChart(data.hourlyData, month, day);
        }
      }

      // 更新图表键，强制重新渲染
      setChartKey(prev => prev + 1);
    } catch (err) {
      console.error('生成图表时出错:', err);
      setError((err as Error).message || '生成图表时出错');
    }
  }, [data, options]);

  // 生成月度发电量图表
  const generateMonthlyGenerationChart = (monthlyData: ProjectMonthlyData[]) => {
    // 准备数据
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const generationValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? monthData.pvGeneration : 0;
    });

    // 计算累计发电量
    const cumulativeValues = [];
    let cumulative = 0;
    for (const value of generationValues) {
      cumulative += value;
      cumulativeValues.push(cumulative);
    }

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: t('chart.monthlyPVGeneration'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const month = params[0].name;
          const generationValue = params[0].value;
          const cumulativeValue = params[1].value;
          return `${month}月<br/>${t('chart.generation')}: ${formatNumber(generationValue, 1)} kWh<br/>${t('chart.cumulativeGeneration')}: ${formatNumber(cumulativeValue, 1)} kWh`;
        }
      },
      legend: {
        data: [t('chart.generation'), t('chart.cumulativeGeneration')],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months.map(m => `${m}${t('chart.monthUnit')}`),
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.generation'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        },
        {
          type: 'value',
          name: t('chart.cumulativeGeneration'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        }
      ],
      series: [
        {
          name: t('chart.generation'),
          type: 'bar',
          data: generationValues,
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: t('chart.cumulativeGeneration'),
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeValues,
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#faad14'
          },
          itemStyle: {
            color: '#faad14'
          }
        }
      ]
    };

    setChartOptions(options);
  };

  // 生成月度收益图表
  const generateMonthlyIncomeChart = (monthlyData: ProjectMonthlyData[]) => {
    // 准备数据
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const incomeValues = months.map(month => {
      const monthData = monthlyData.find(m => m.month === month);
      return monthData ? monthData.pvBenefit : 0;
    });

    // 计算累计收益
    const cumulativeValues = [];
    let cumulative = 0;
    for (const value of incomeValues) {
      cumulative += value;
      cumulativeValues.push(cumulative);
    }

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: t('chart.monthlyPVIncome'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const month = params[0].name;
          const incomeValue = params[0].value;
          const cumulativeValue = params[1].value;
          return `${month}月<br/>${t('chart.income')}: ${formatNumber(incomeValue, 1)} JPY<br/>${t('chart.cumulativeIncome')}: ${formatNumber(cumulativeValue, 1)} JPY`;
        }
      },
      legend: {
        data: [t('chart.income'), t('chart.cumulativeIncome')],
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months.map(m => `${m}${t('chart.monthUnit')}`),
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.income'),
          axisLabel: {
            formatter: '{value} JPY'
          }
        },
        {
          type: 'value',
          name: t('chart.cumulativeIncome'),
          axisLabel: {
            formatter: '{value} JPY'
          }
        }
      ],
      series: [
        {
          name: t('chart.income'),
          type: 'bar',
          data: incomeValues,
          itemStyle: {
            color: '#52c41a'
          }
        },
        {
          name: t('chart.cumulativeIncome'),
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeValues,
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#faad14'
          },
          itemStyle: {
            color: '#faad14'
          }
        }
      ]
    };

    setChartOptions(options);
  };

  // 生成日发电量图表
  const generateDailyGenerationChart = (dailyData: ProjectDailyData[], selectedMonth: number) => {
    // 准备数据
    let filteredData = dailyData;
    let monthDisplay = t('chart.allYear');

    // 如果选择了特定月份，则过滤数据
    if (selectedMonth > 0) {
      filteredData = dailyData.filter(d => d.month === selectedMonth);
      monthDisplay = `${selectedMonth}${t('chart.monthUnit')}`;
    }

    // 按日期排序
    filteredData.sort((a, b) => {
      if (a.month !== b.month) return a.month - b.month;
      return a.day - b.day;
    });

    const days = filteredData.map(d => `${d.month}/${d.day}`);
    const generationValues = filteredData.map(d => d.pvGeneration);

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: `${t('chart.dailyPVGeneration')} - ${monthDisplay}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const day = params[0].name;
          const value = params[0].value;
          return `${day}<br/>${t('chart.generation')}: ${formatNumber(value, 1)} kWh`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('chart.date'),
        axisLabel: {
          interval: Math.floor(days.length / 10),
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: t('chart.generation'),
        axisLabel: {
          formatter: '{value} kWh'
        }
      },
      series: [
        {
          name: t('chart.generation'),
          type: 'line',
          data: generationValues,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    };

    setChartOptions(options);
  };

  // 生成日收益图表
  const generateDailyIncomeChart = (dailyData: ProjectDailyData[], selectedMonth: number) => {
    // 准备数据
    let filteredData = dailyData;
    let monthDisplay = t('chart.allYear');

    // 如果选择了特定月份，则过滤数据
    if (selectedMonth > 0) {
      filteredData = dailyData.filter(d => d.month === selectedMonth);
      monthDisplay = `${selectedMonth}${t('chart.monthUnit')}`;
    }

    // 按日期排序
    filteredData.sort((a, b) => {
      if (a.month !== b.month) return a.month - b.month;
      return a.day - b.day;
    });

    const days = filteredData.map(d => `${d.month}/${d.day}`);
    const incomeValues = filteredData.map(d => d.pvBenefit);

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: `${t('chart.dailyPVIncome')} - ${monthDisplay}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const day = params[0].name;
          const value = params[0].value;
          return `${day}<br/>${t('chart.income')}: ${formatNumber(value, 1)} JPY`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('chart.date'),
        axisLabel: {
          interval: Math.floor(days.length / 10),
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: t('chart.income'),
        axisLabel: {
          formatter: '{value} JPY'
        }
      },
      series: [
        {
          name: t('chart.income'),
          type: 'line',
          data: incomeValues,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    };

    setChartOptions(options);
  };

  // 生成小时发电量图表
  const generateHourlyGenerationChart = (hourlyData: ProjectHourlyData[], selectedMonth: number, selectedDay: number) => {
    // 准备数据
    let filteredData = hourlyData;

    // 如果选择了特定月份和日期，则过滤数据
    if (selectedMonth > 0) {
      filteredData = hourlyData.filter(h => h.month === selectedMonth);
      if (selectedDay > 0) {
        filteredData = filteredData.filter(h => h.day === selectedDay);
      }
    }

    // 按时间排序
    filteredData.sort((a, b) => {
      if (a.month !== b.month) return a.month - b.month;
      if (a.day !== b.day) return a.day - b.day;
      return a.hour - b.hour;
    });

    const hours = filteredData.map(h => `${h.hour}:00`);
    const generationValues = filteredData.map(h => h.pvGeneration);

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: `${t('chart.hourlyPVGeneration')} - ${selectedMonth}${t('chart.monthUnit')}${selectedDay}${t('chart.dayUnit')}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const hour = params[0].name;
          const value = params[0].value;
          return `${hour}<br/>${t('chart.generation')}: ${formatNumber(value, 3)} kWh`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('chart.hour')
      },
      yAxis: {
        type: 'value',
        name: t('chart.generation'),
        axisLabel: {
          formatter: '{value} kWh'
        }
      },
      series: [
        {
          name: t('chart.generation'),
          type: 'bar',
          data: generationValues,
          itemStyle: {
            color: '#1890ff'
          }
        }
      ]
    };

    setChartOptions(options);
  };

  // 生成小时收益图表
  const generateHourlyIncomeChart = (hourlyData: ProjectHourlyData[], selectedMonth: number, selectedDay: number) => {
    // 准备数据
    let filteredData = hourlyData;

    // 如果选择了特定月份和日期，则过滤数据
    if (selectedMonth > 0) {
      filteredData = hourlyData.filter(h => h.month === selectedMonth);
      if (selectedDay > 0) {
        filteredData = filteredData.filter(h => h.day === selectedDay);
      }
    }

    // 按时间排序
    filteredData.sort((a, b) => {
      if (a.month !== b.month) return a.month - b.month;
      if (a.day !== b.day) return a.day - b.day;
      return a.hour - b.hour;
    });

    const hours = filteredData.map(h => `${h.hour}:00`);
    const incomeValues = filteredData.map(h => h.pvBenefit);

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: `${t('chart.hourlyPVIncome')} - ${selectedMonth}${t('chart.monthUnit')}${selectedDay}${t('chart.dayUnit')}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const hour = params[0].name;
          const value = params[0].value;
          return `${hour}<br/>${t('chart.income')}: ${formatNumber(value, 3)} JPY`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('chart.hour')
      },
      yAxis: {
        type: 'value',
        name: t('chart.income'),
        axisLabel: {
          formatter: '{value} JPY'
        }
      },
      series: [
        {
          name: t('chart.income'),
          type: 'bar',
          data: incomeValues,
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    };

    setChartOptions(options);
  };

  // 显示错误信息或空数据提示
  if (!chartOptions) {
    return (
      <Card>
        {error ? (
          <Alert
            message={t('chart.error')}
            description={error}
            type="error"
            showIcon
          />
        ) : (
          <Empty description={t('chart.noData')} />
        )}
      </Card>
    );
  }

  // 显示图表
  return (
    <Card>
      <div key={chartKey} style={{ position: 'relative', width: '100%', height: '400px' }}>
        <ReactECharts
          option={chartOptions}
          style={{ height: '400px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
          notMerge={true}
          lazyUpdate={false}
          theme="light"
        />
      </div>
    </Card>
  );
};

export default PVGenerationChart;
