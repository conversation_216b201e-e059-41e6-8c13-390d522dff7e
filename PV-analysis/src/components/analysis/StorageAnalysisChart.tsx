import React, { useState, useEffect } from 'react';
import { Card, Empty, Alert, Checkbox, Space, Row, Col, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { ProjectHourlyData, ProjectDailyData, ProjectMonthlyData } from '../../types/projectData';
import { formatNumber } from '../../utils';
import { ElectricityPrice, PriceRule, SeasonalPricePolicy } from '../../types/database';

const { Option } = Select;

// 定义图表指标类型
export type StorageMetric =
  | 'storageCharge' // 充电电量（只显示正值）
  | 'storageDischarge' // 放电电量（负值转为正值显示）
  | 'pvGeneration' // 光伏发电总量
  | 'electricityConsumption' // 用户用电量
  | 'gridExport' // 上网电量
  | 'gridImport' // 购买电量
  | 'storageBenefit' // 储能收益
  | 'cumulativeStorageBenefit'; // 储能累计收益

interface StorageAnalysisChartProps {
  data: {
    hourlyData: ProjectHourlyData[];
    dailyData: ProjectDailyData[];
    monthlyData: ProjectMonthlyData[];
    electricityPrice: ElectricityPrice | null;
  };
  options: {
    viewMode: 'hourly' | 'daily' | 'monthly';
    month: number;
    day: number;
    metrics: StorageMetric[];
  };
  onOptionsChange?: (options: any) => void;
}

/**
 * 储能分析图表组件
 */
const StorageAnalysisChart: React.FC<StorageAnalysisChartProps> = ({
  data,
  options,
  onOptionsChange
}) => {
  const { t } = useTranslation();
  const [chartOptions, setChartOptions] = useState<EChartsOption | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [chartKey, setChartKey] = useState<number>(0);

  // 定义指标配置
  const metricConfigs = {
    storageCharge: {
      name: t('chart.storageCharge'),
      type: 'bar',
      yAxisIndex: 0,
      color: '#1890ff'
    },
    storageDischarge: {
      name: t('chart.storageDischarge'),
      type: 'bar',
      yAxisIndex: 0,
      color: '#f5222d'
    },
    pvGeneration: {
      name: t('chart.pvGeneration'),
      type: 'line',
      yAxisIndex: 0,
      color: '#faad14'
    },
    electricityConsumption: {
      name: t('chart.electricityConsumption'),
      type: 'line',
      yAxisIndex: 0,
      color: '#722ed1'
    },
    gridExport: {
      name: t('chart.gridExport'),
      type: 'line',
      yAxisIndex: 0,
      color: '#eb2f96'
    },
    gridImport: {
      name: t('chart.gridImport'),
      type: 'line',
      yAxisIndex: 0,
      color: '#fa541c'
    },
    storageBenefit: {
      name: t('chart.storageBenefit'),
      type: 'line',
      yAxisIndex: 1,
      color: '#13c2c2'
    },
    cumulativeStorageBenefit: {
      name: t('chart.cumulativeStorageBenefit'),
      type: 'line',
      yAxisIndex: 1,
      color: '#2f54eb'
    }
  };

  // 当数据或选项变化时，更新图表
  useEffect(() => {
    if (!data || !data.hourlyData || data.hourlyData.length === 0) {
      setChartOptions(null);
      return;
    }

    try {
      setError(null);
      console.log('StorageAnalysisChart - 开始生成图表，视图模式:', options.viewMode);
      console.log('StorageAnalysisChart - 选择的指标:', options.metrics);

      // 根据视图模式生成不同的图表
      switch (options.viewMode) {
        case 'hourly':
          generateHourlyChart(data.hourlyData, options.month, options.day, options.metrics, data.electricityPrice);
          break;
        case 'daily':
          generateDailyChart(data.hourlyData, options.month, options.metrics);
          break;
        case 'monthly':
          generateMonthlyChart(data.hourlyData, options.metrics);
          break;
        default:
          throw new Error(`未支持的视图模式: ${options.viewMode}`);
      }

      // 更新图表键，强制重新渲染
      setChartKey(prev => prev + 1);
    } catch (err) {
      console.error('生成图表时出错:', err);
      setError((err as Error).message || '生成图表时出错');
    }
  }, [data, options]);

  // 获取特定时间点的电价
  const getElectricityPrice = (hour: number, day: number, month: number, electricityPrice: ElectricityPrice | null): { price: number, gridFeedInPrice: number } => {
    if (!electricityPrice) {
      return { price: 0, gridFeedInPrice: 0 };
    }

    try {
      // 默认价格
      let price = 0;
      let gridFeedInPrice = 0;

      // 时间格式化
      const timeStr = `${hour.toString().padStart(2, '0')}:00`;

      // 根据电价政策类型获取价格
      if (electricityPrice.policyType === 'fixed') {
        // 固定电价
        const rule = findMatchingRule(timeStr, electricityPrice.rules);
        if (rule) {
          price = rule.price;
          gridFeedInPrice = rule.gridFeedInPrice || 0;
        }
      } else {
        // 季节性电价
        const seasonPolicy = findMatchingSeason(month, electricityPrice.seasonalPolicies);
        if (seasonPolicy) {
          const rule = findMatchingRule(timeStr, seasonPolicy.rules);
          if (rule) {
            price = rule.price;
            gridFeedInPrice = rule.gridFeedInPrice || 0;
          }
        }
      }

      return { price, gridFeedInPrice };
    } catch (error) {
      console.error('获取电价时出错:', error);
      return { price: 0, gridFeedInPrice: 0 };
    }
  };

  // 查找匹配的时间规则
  const findMatchingRule = (timeStr: string, rules: PriceRule[]): PriceRule | undefined => {
    return rules.find(rule => {
      const start = rule.startTime;
      const end = rule.endTime;

      // 处理跨天的情况
      if (start < end) {
        return timeStr >= start && timeStr < end;
      } else {
        return timeStr >= start || timeStr < end;
      }
    });
  };

  // 查找匹配的季节
  const findMatchingSeason = (month: number, seasons: SeasonalPricePolicy[]): SeasonalPricePolicy | undefined => {
    return seasons.find(season => season.months.includes(month));
  };

  // 生成小时图表
  const generateHourlyChart = (
    hourlyData: ProjectHourlyData[],
    selectedMonth: number,
    selectedDay: number,
    metrics: StorageMetric[],
    electricityPrice: ElectricityPrice | null
  ) => {
    // 准备数据
    let filteredData = hourlyData;

    // 如果选择了特定月份和日期，则过滤数据
    if (selectedMonth > 0) {
      filteredData = hourlyData.filter(h => h.month === selectedMonth);
      if (selectedDay > 0) {
        filteredData = filteredData.filter(h => h.day === selectedDay);
      }
    }

    // 按时间排序
    filteredData.sort((a, b) => {
      if (a.month !== b.month) return a.month - b.month;
      if (a.day !== b.day) return a.day - b.day;
      return a.hour - b.hour;
    });

    // 提取小时标签
    const hours = filteredData.map(h => `${h.hour}:00`);

    // 准备系列数据
    const series: any[] = [];

    // 计算储能收益和累计收益
    let cumulativeBenefit = 0;
    const storageBenefits: number[] = [];
    const cumulativeBenefits: number[] = [];

    // 准备各指标数据
    const metricsData: Record<string, number[]> = {
      storageCharge: [],
      storageDischarge: [],
      pvGeneration: [],
      electricityConsumption: [],
      gridExport: [],
      gridImport: [],
      storageBenefit: [],
      cumulativeStorageBenefit: []
    };

    // 处理每个小时的数据
    filteredData.forEach(hour => {
      // 计算光伏发电总量
      const pvTotal = Object.values(hour.pvGeneration).reduce((sum, val) => sum + (val || 0), 0);

      // 获取电价
      const { price, gridFeedInPrice } = getElectricityPrice(hour.hour, hour.day, hour.month, electricityPrice);

      // 计算储能收益
      let benefit = 0;
      if (hour.storageCharge > 0) {
        // 充电时，收益为负
        benefit = -gridFeedInPrice * hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电时，收益为正
        benefit = price * Math.abs(hour.storageCharge);
      }

      // 累计收益
      cumulativeBenefit += benefit;

      // 存储各指标数据 - 拆分充放电量
      // 充电电量只保留正值，负值置为0
      metricsData.storageCharge.push(hour.storageCharge > 0 ? hour.storageCharge : 0);
      // 放电电量只保留负值的绝对值，正值置为0
      metricsData.storageDischarge.push(hour.storageCharge < 0 ? Math.abs(hour.storageCharge) : 0);

      metricsData.pvGeneration.push(pvTotal);
      metricsData.electricityConsumption.push(hour.electricityConsumption);
      metricsData.gridExport.push(hour.gridExport);
      metricsData.gridImport.push(hour.gridImport);
      metricsData.storageBenefit.push(benefit);
      metricsData.cumulativeStorageBenefit.push(cumulativeBenefit);
    });



    // 创建系列数据
    metrics.forEach(metric => {
      const config = metricConfigs[metric];
      series.push({
        name: config.name,
        type: config.type,
        yAxisIndex: config.yAxisIndex,
        data: metricsData[metric],
        itemStyle: {
          color: config.color
        }
      });
    });

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: `${t('chart.hourlyStorageAnalysis')} - ${selectedMonth}${t('chart.monthUnit')}${selectedDay}${t('chart.dayUnit')}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            const value = param.value;
            let valueFormatted = '';

            // 根据指标类型选择不同的单位
            if (param.seriesName === t('chart.storageBenefit') ||
                param.seriesName === t('chart.cumulativeStorageBenefit')) {
              valueFormatted = `${formatNumber(value, 1)} JPY`;
            } else {
              valueFormatted = `${formatNumber(value, 3)} kWh`;
            }

            result += `${param.marker} ${param.seriesName}: ${valueFormatted}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: metrics.map(m => metricConfigs[m].name),
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('chart.hour')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.energy'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        },
        {
          type: 'value',
          name: t('chart.benefit'),
          axisLabel: {
            formatter: '{value} JPY'
          }
        }
      ],
      series: series
    };

    setChartOptions(options);
  };

  // 生成日图表
  const generateDailyChart = (hourlyData: ProjectHourlyData[], selectedMonth: number, metrics: StorageMetric[]) => {
    // 获取电价数据
    const electricityPrice = data.electricityPrice;
    // 准备数据
    let filteredData = hourlyData;

    // 如果选择了特定月份，则过滤数据
    if (selectedMonth > 0) {
      filteredData = hourlyData.filter(h => h.month === selectedMonth);
    }

    // 每月的天数（非闰年）
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 计算日数据
    const dailyData: Record<string, Record<StorageMetric, number>> = {};

    // 初始化日数据结构
    for (let month = 1; month <= 12; month++) {
      if (selectedMonth > 0 && month !== selectedMonth) continue;

      for (let day = 1; day <= daysInMonth[month - 1]; day++) {
        const key = `${month}-${day}`;
        dailyData[key] = {
          storageCharge: 0,
          storageDischarge: 0,
          pvGeneration: 0,
          electricityConsumption: 0,
          gridExport: 0,
          gridImport: 0,
          storageBenefit: 0,
          cumulativeStorageBenefit: 0
        };
      }
    }

    // 计算每天的数据
    filteredData.forEach(hour => {
      const key = `${hour.month}-${hour.day}`;

      // 计算光伏发电总量
      const pvTotal = Object.values(hour.pvGeneration).reduce((sum, val) => sum + (val || 0), 0);

      // 累加数据 - 拆分充放电量
      if (hour.storageCharge > 0) {
        // 充电量（正值）
        dailyData[key].storageCharge += hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电量（负值转为正值）
        dailyData[key].storageDischarge += Math.abs(hour.storageCharge);
      }

      dailyData[key].pvGeneration += pvTotal;
      dailyData[key].electricityConsumption += hour.electricityConsumption;
      dailyData[key].gridExport += hour.gridExport;
      dailyData[key].gridImport += hour.gridImport;

      // 获取电价
      const { price, gridFeedInPrice } = getElectricityPrice(hour.hour, hour.day, hour.month, electricityPrice);

      // 计算储能收益
      let benefit = 0;
      if (hour.storageCharge > 0) {
        // 充电时，收益为负
        benefit = -gridFeedInPrice * hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电时，收益为正
        benefit = price * Math.abs(hour.storageCharge);
      }

      dailyData[key].storageBenefit += benefit;

      // 不再使用剩余电量
    });

    // 计算累计储能收益
    let cumulativeBenefit = 0;
    // 按照正确的日期顺序排序（先按月份，再按日期）
    Object.entries(dailyData)
      .sort((a, b) => {
        const [aMonth, aDay] = a[0].split('-').map(Number);
        const [bMonth, bDay] = b[0].split('-').map(Number);
        return aMonth === bMonth ? aDay - bDay : aMonth - bMonth;
      })
      .forEach(([key, data]) => {
        cumulativeBenefit += data.storageBenefit;
        dailyData[key].cumulativeStorageBenefit = cumulativeBenefit;
      });

    // 提取日期标签和数据系列
    const days: string[] = [];
    const metricsData: Record<StorageMetric, number[]> = {
      storageCharge: [],
      storageDischarge: [],
      pvGeneration: [],
      electricityConsumption: [],
      gridExport: [],
      gridImport: [],
      storageBenefit: [],
      cumulativeStorageBenefit: []
    };

    // 按日期排序
    Object.entries(dailyData).sort((a, b) => {
      const [aMonth, aDay] = a[0].split('-').map(Number);
      const [bMonth, bDay] = b[0].split('-').map(Number);
      return aMonth === bMonth ? aDay - bDay : aMonth - bMonth;
    }).forEach(([key, data]) => {
      const [month, day] = key.split('-').map(Number);

      // 如果选择了特定月份，只显示日期；否则显示月份和日期
      days.push(selectedMonth > 0 ? `${day}${t('chart.dayUnit')}` : `${month}/${day}`);

      // 提取各指标数据
      metrics.forEach(metric => {
        metricsData[metric].push(data[metric]);
      });
    });

    // 准备系列数据
    const series: any[] = [];

    // 添加各指标系列
    metrics.forEach(metric => {
      const config = metricConfigs[metric];
      let seriesItem: any = {
        name: config.name,
        type: config.type,
        yAxisIndex: config.yAxisIndex,
        data: metricsData[metric],
        itemStyle: {
          color: config.color
        }
      };

      // 为线图添加区域样式
      if (config.type === 'line') {
        seriesItem.areaStyle = {
          opacity: 0.3
        };
        seriesItem.smooth = true;
      }

      series.push(seriesItem);
    });

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: selectedMonth > 0 ?
          `${t('chart.dailyStorageAnalysis')} - ${selectedMonth}${t('chart.monthUnit')}` :
          t('chart.dailyStorageAnalysis'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            const value = param.value;
            let valueFormatted = '';

            // 根据指标类型选择不同的单位
            if (param.seriesName === t('chart.storageBenefit') ||
                param.seriesName === t('chart.cumulativeStorageBenefit')) {
              valueFormatted = `${formatNumber(value, 1)} JPY`;
            } else {
              valueFormatted = `${formatNumber(value, 3)} kWh`;
            }

            result += `${param.marker} ${param.seriesName}: ${valueFormatted}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: metrics.map(m => metricConfigs[m].name),
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('chart.date'),
        axisLabel: {
          interval: Math.floor(days.length / 15),
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.energy'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        },
        {
          type: 'value',
          name: t('chart.benefit'),
          axisLabel: {
            formatter: '{value} JPY'
          }
        }
      ],
      series: series
    };

    setChartOptions(options);
  };

  // 生成月图表
  const generateMonthlyChart = (hourlyData: ProjectHourlyData[], metrics: StorageMetric[]) => {
    // 获取电价数据
    const electricityPrice = data.electricityPrice;
    // 准备数据
    const monthlyData: Record<number, Record<StorageMetric, number>> = {};

    // 初始化月数据结构
    for (let month = 1; month <= 12; month++) {
      monthlyData[month] = {
        storageCharge: 0,
        storageDischarge: 0,
        pvGeneration: 0,
        electricityConsumption: 0,
        gridExport: 0,
        gridImport: 0,
        storageBenefit: 0,
        cumulativeStorageBenefit: 0
      };
    }

    // 每月的天数（非闰年）
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 计算每月的数据
    hourlyData.forEach(hour => {
      const month = hour.month;

      // 计算光伏发电总量
      const pvTotal = Object.values(hour.pvGeneration).reduce((sum, val) => sum + (val || 0), 0);

      // 累加数据 - 拆分充放电量
      if (hour.storageCharge > 0) {
        // 充电量（正值）
        monthlyData[month].storageCharge += hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电量（负值转为正值）
        monthlyData[month].storageDischarge += Math.abs(hour.storageCharge);
      }

      monthlyData[month].pvGeneration += pvTotal;
      monthlyData[month].electricityConsumption += hour.electricityConsumption;
      monthlyData[month].gridExport += hour.gridExport;
      monthlyData[month].gridImport += hour.gridImport;

      // 获取电价
      const { price, gridFeedInPrice } = getElectricityPrice(hour.hour, hour.day, hour.month, electricityPrice);

      // 计算储能收益
      let benefit = 0;
      if (hour.storageCharge > 0) {
        // 充电时，收益为负
        benefit = -gridFeedInPrice * hour.storageCharge;
      } else if (hour.storageCharge < 0) {
        // 放电时，收益为正
        benefit = price * Math.abs(hour.storageCharge);
      }

      monthlyData[month].storageBenefit += benefit;

      // 不再使用剩余电量
    });

    // 计算累计储能收益
    let cumulativeBenefit = 0;
    // 按月份顺序计算累计收益
    for (let month = 1; month <= 12; month++) {
      cumulativeBenefit += monthlyData[month].storageBenefit;
      monthlyData[month].cumulativeStorageBenefit = cumulativeBenefit;
    }

    // 提取月份标签和数据系列
    const months = Array.from({ length: 12 }, (_, i) => `${i + 1}${t('chart.monthUnit')}`);
    const metricsData: Record<StorageMetric, number[]> = {
      storageCharge: [],
      storageDischarge: [],
      pvGeneration: [],
      electricityConsumption: [],
      gridExport: [],
      gridImport: [],
      storageBenefit: [],
      cumulativeStorageBenefit: []
    };

    // 提取各指标数据
    for (let month = 1; month <= 12; month++) {
      metrics.forEach(metric => {
        metricsData[metric].push(monthlyData[month][metric]);
      });
    }

    // 准备系列数据
    const series: any[] = [];

    // 添加各指标系列
    metrics.forEach(metric => {
      const config = metricConfigs[metric];
      let seriesItem: any = {
        name: config.name,
        type: config.type,
        yAxisIndex: config.yAxisIndex,
        data: metricsData[metric],
        itemStyle: {
          color: config.color
        }
      };

      // 为线图添加区域样式
      if (config.type === 'line') {
        seriesItem.areaStyle = {
          opacity: 0.3
        };
        seriesItem.smooth = true;
      }

      series.push(seriesItem);
    });

    // 设置图表选项
    const options: EChartsOption = {
      title: {
        text: t('chart.monthlyStorageAnalysis'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            const value = param.value;
            let valueFormatted = '';

            // 根据指标类型选择不同的单位
            if (param.seriesName === t('chart.storageBenefit') ||
                param.seriesName === t('chart.cumulativeStorageBenefit')) {
              valueFormatted = `${formatNumber(value, 1)} JPY`;
            } else {
              valueFormatted = `${formatNumber(value, 3)} kWh`;
            }

            result += `${param.marker} ${param.seriesName}: ${valueFormatted}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: metrics.map(m => metricConfigs[m].name),
        bottom: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.energy'),
          axisLabel: {
            formatter: '{value} kWh'
          }
        },
        {
          type: 'value',
          name: t('chart.benefit'),
          axisLabel: {
            formatter: '{value} JPY'
          }
        }
      ],
      series: series
    };

    setChartOptions(options);
  };

  // 显示错误信息或空数据提示
  if (!chartOptions) {
    return (
      <Card>
        {error ? (
          <Alert
            message={t('chart.error')}
            description={error}
            type="error"
            showIcon
          />
        ) : (
          <Empty description={t('chart.noData')} />
        )}
      </Card>
    );
  }

  // 显示图表
  return (
    <Card>
      <div key={chartKey} style={{ position: 'relative', width: '100%', height: '400px' }}>
        <ReactECharts
          option={chartOptions}
          style={{ height: '400px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
          notMerge={true}
          lazyUpdate={false}
          theme="light"
        />
      </div>
    </Card>
  );
};

export default StorageAnalysisChart;
