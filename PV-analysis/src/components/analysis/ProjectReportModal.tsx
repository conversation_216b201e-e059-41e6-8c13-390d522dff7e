import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { DownloadOutlined, FilePdfOutlined, FileImageOutlined } from '@ant-design/icons';
import { ProjectData } from '../../types/projectData';
import ProjectReportContent from './ProjectReportContent';
import { generateProjectReport, generateProjectLongImage } from '../../services/reportService';

interface ProjectReportModalProps {
  project: ProjectData;
  visible: boolean;
  onCancel: () => void;
  loading?: boolean;
}

/**
 * 项目报告模态框组件
 */
const ProjectReportModal: React.FC<ProjectReportModalProps> = ({
  project,
  visible,
  onCancel,
  loading = false
}) => {
  const { t } = useTranslation();
  const reportContentRef = useRef<HTMLDivElement>(null);
  const [chartsReady, setChartsReady] = useState<boolean>(false);
  const [generatingPDF, setGeneratingPDF] = useState<boolean>(false);
  const [generatingLongImage, setGeneratingLongImage] = useState<boolean>(false);

  // 处理图表渲染完成
  const handleChartsReady = () => {
    console.log('所有图表已准备就绪');
    setChartsReady(true);
  };

  // 处理下载PDF
  const handleDownloadPDF = async () => {
    if (!reportContentRef.current) {
      message.error('报告内容未准备就绪，请稍后再试');
      return;
    }

    if (!chartsReady) {
      message.warning('图表正在渲染中，请稍后再试');
      return;
    }

    try {
      setGeneratingPDF(true);
      // 给图表一些额外时间确保完全渲染
      console.log('准备生成PDF报告，等待图表完全渲染...');
      setTimeout(async () => {
        try {
          // 强制刷新一次图表
          const chartElements = reportContentRef.current!.querySelectorAll('.echarts-for-react');
          console.log(`找到 ${chartElements.length} 个图表元素，准备刷新`);

          // 调整图表高度，确保在PDF中显示完整
          chartElements.forEach((chart: Element) => {
            if (chart instanceof HTMLElement) {
              chart.style.height = '500px'; // 增加图表高度

              // 获取图表实例并调整大小
              const chartInstance = (chart as any).__echarts_instance__;
              if (chartInstance) {
                chartInstance.resize();
              }
            }
          });

          // 再等待一段时间确保图表完全渲染
          setTimeout(async () => {
            try {
              message.info('正在生成PDF报告，请稍候...');
              await generateProjectReport(project, reportContentRef.current!);
              setGeneratingPDF(false);
              message.success('PDF报告生成成功！');
            } catch (error) {
              console.error('下载PDF报告失败:', error);
              message.error('生成PDF报告失败，请稍后再试');
              setGeneratingPDF(false);
            }
          }, 2500); // 增加等待时间
        } catch (error) {
          console.error('刷新图表失败:', error);
          message.error('生成PDF报告失败，请稍后再试');
          setGeneratingPDF(false);
        }
      }, 2000); // 增加等待时间
    } catch (error) {
      console.error('下载PDF报告失败:', error);
      message.error('生成PDF报告失败，请稍后再试');
      setGeneratingPDF(false);
    }
  };

  // 处理导出长图
  const handleDownloadLongImage = async () => {
    if (!reportContentRef.current) {
      message.error('报告内容未准备就绪，请稍后再试');
      return;
    }

    if (!chartsReady) {
      message.warning('图表正在渲染中，请稍后再试');
      return;
    }

    try {
      setGeneratingLongImage(true);
      // 给图表一些额外时间确保完全渲染
      console.log('准备生成长图，等待图表完全渲染...');
      setTimeout(async () => {
        try {
          // 强制刷新一次图表
          const chartElements = reportContentRef.current!.querySelectorAll('.echarts-for-react');
          console.log(`找到 ${chartElements.length} 个图表元素，准备刷新`);

          // 调整图表高度，确保在长图中显示完整
          chartElements.forEach((chart: Element) => {
            if (chart instanceof HTMLElement) {
              chart.style.height = '500px'; // 增加图表高度

              // 获取图表实例并调整大小
              const chartInstance = (chart as any).__echarts_instance__;
              if (chartInstance) {
                chartInstance.resize();
              }
            }
          });

          // 再等待一段时间确保图表完全渲染
          setTimeout(async () => {
            try {
              message.info('正在生成长图，请稍候...');
              await generateProjectLongImage(project, reportContentRef.current!);
              setGeneratingLongImage(false);
              message.success('长图生成成功！');
            } catch (error) {
              console.error('导出长图失败:', error);
              message.error('生成长图失败，请稍后再试');
              setGeneratingLongImage(false);
            }
          }, 2500); // 增加等待时间
        } catch (error) {
          console.error('刷新图表失败:', error);
          message.error('生成长图失败，请稍后再试');
          setGeneratingLongImage(false);
        }
      }, 2000); // 增加等待时间
    } catch (error) {
      console.error('导出长图失败:', error);
      message.error('生成长图失败，请稍后再试');
      setGeneratingLongImage(false);
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <FilePdfOutlined style={{ marginRight: 8, color: '#ff4d4f' }} />
          {t('report.title')}
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={1000}
      style={{ top: 20 }}
      bodyStyle={{ maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' }}
      footer={[
        <Button key="close" onClick={onCancel}>
          {t('common.close')}
        </Button>,
        <Button
          key="downloadLongImage"
          icon={<FileImageOutlined />}
          onClick={handleDownloadLongImage}
          disabled={loading || generatingPDF || generatingLongImage || !chartsReady}
          loading={generatingLongImage}
          style={{ marginRight: 8 }}
        >
          {generatingLongImage ? t('report.generatingLongImage') : t('report.downloadLongImage')}
        </Button>,
        <Button
          key="download"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleDownloadPDF}
          disabled={loading || generatingPDF || generatingLongImage || !chartsReady}
          loading={generatingPDF}
        >
          {generatingPDF ? t('report.generating') : t('report.downloadPDF')}
        </Button>
      ]}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>{t('report.loading')}</div>
        </div>
      ) : (
        <>
          {!chartsReady && (
            <div style={{ textAlign: 'center', padding: '10px', backgroundColor: '#fffbe6', marginBottom: '10px' }}>
              <Spin size="small" style={{ marginRight: '8px' }} />
              {t('report.preparingCharts')}
            </div>
          )}
          <ProjectReportContent
            project={project}
            forwardedRef={reportContentRef}
            onChartsReady={handleChartsReady}
          />
        </>
      )}
    </Modal>
  );
};

export default ProjectReportModal;
