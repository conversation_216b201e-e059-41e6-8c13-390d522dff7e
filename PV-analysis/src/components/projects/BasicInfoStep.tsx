import React, { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Select, Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { ProjectBasicInfo } from '../../types';
import { StepAlert } from '../../components/common';
import {
  japanRegions,
  getPrefecturesByRegion,
  Prefecture,
  getRegionByPrefecture
} from '../../data/japanRegions';

const { Option } = Select;
const { TextArea } = Input;

interface BasicInfoStepProps {
  data?: ProjectBasicInfo;
  onChange: (data: ProjectBasicInfo) => void;
  onValidate: (valid: boolean) => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [selectedPrefecture, setSelectedPrefecture] = useState<string>('');
  const [availablePrefectures, setAvailablePrefectures] = useState<Prefecture[]>([]);

  // 初始化表单数据
  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);

      // 如果已有县府数据，初始化地方和县府
      const prefectureValue = (data as any).prefectureCode || '';
      if (prefectureValue) {
        setSelectedPrefecture(prefectureValue);

        // 查找所属地方
        const region = getRegionByPrefecture(prefectureValue);
        if (region) {
          setSelectedRegion(region.value);
          setAvailablePrefectures(getPrefecturesByRegion(region.value));
        }
      }

      // 初始化后立即验证表单
      setTimeout(() => {
        console.log('初始化后立即验证表单');
        validateForm();
      }, 100);
    }
  }, [data, form]);

  // 处理地方选择变更
  const handleRegionChange = (value: string) => {
    setSelectedRegion(value);

    // 获取该地方下的县府列表
    const prefectures = getPrefecturesByRegion(value);
    setAvailablePrefectures(prefectures);

    // 清空县府选择
    form.setFieldsValue({
      prefecture: undefined,
      prefectureCode: undefined
    });
    setSelectedPrefecture('');

    // 更新表单数据
    const regionLabel = japanRegions.find(r => r.value === value)?.label || '';
    form.setFieldsValue({
      region: regionLabel,
      regionCode: value
    });

    // 触发表单值变更
    const allValues = form.getFieldsValue();
    onChange(allValues);
    validateForm();
  };

  // 处理县府选择变更
  const handlePrefectureChange = (value: string) => {
    setSelectedPrefecture(value);

    // 更新表单数据
    const prefectureLabel = availablePrefectures.find(p => p.value === value)?.label || '';
    form.setFieldsValue({
      prefecture: prefectureLabel,
      prefectureCode: value
    });

    // 触发表单值变更
    const allValues = form.getFieldsValue();
    onChange(allValues);
    validateForm();
  };

  // 验证表单
  const validateForm = () => {
    console.log('开始验证基础信息表单');

    // 获取当前表单值
    const values = form.getFieldsValue();
    console.log('当前表单值:', values);

    // 检查必填字段
    const requiredFields = [
      'name', 'capacity', 'region', 'prefecture',
      'installationType', 'projectType', 'connectionType'
    ];

    const missingFields = requiredFields.filter(field => !values[field]);
    console.log('缺失字段:', missingFields);

    if (missingFields.length === 0) {
      console.log('表单验证通过');
      onValidate(true);
      return;
    }

    // 如果有缺失字段，尝试通过form.validateFields验证
    form.validateFields()
      .then(() => {
        console.log('validateFields验证通过');
        onValidate(true);
      })
      .catch((errors) => {
        console.log('validateFields验证失败:', errors);
        onValidate(false);
      });
  };

  // 表单值变更时触发
  const handleValuesChange = (_: any, allValues: any) => {
    onChange(allValues);
    validateForm();
  };

  // 获取未完成的必填字段
  const getIncompleteFields = () => {
    const incompleteFields: string[] = [];
    const values = form.getFieldsValue();

    if (!values.name) incompleteFields.push(t('projects.name'));
    if (!values.capacity) incompleteFields.push(t('projects.capacity'));
    if (!values.region) incompleteFields.push(t('projects.region'));
    if (!values.prefecture) incompleteFields.push(t('projects.prefecture'));
    if (!values.installationType) incompleteFields.push(t('projects.installationType'));
    if (!values.projectType) incompleteFields.push(t('projects.projectType'));
    if (!values.connectionType) incompleteFields.push(t('projects.connectionType'));

    return incompleteFields;
  };

  return (
    <div>
      <StepAlert
        message={t('projectWizard.required')}
        description={t('projectWizard.basicInfoDescription')}
        type="info"
        showIcon
        incompleteFields={getIncompleteFields()}
      />

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
        initialValues={data}
      >
        {/* 隐藏字段，用于存储代码值 */}
        <Form.Item name="regionCode" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="prefectureCode" hidden>
          <Input />
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.name')}
              </>}
              rules={[{ required: true, message: t('projects.nameRequired') }]}
            >
              <Input placeholder={t('projects.namePlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="capacity"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.capacity')}
              </>}
              rules={[{ required: true, message: t('projects.capacityRequired') }]}
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                style={{ width: '100%' }}
                addonAfter="kW"
                placeholder="0.0"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="region"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.region')} ({t('projects.area')})
              </>}
              rules={[{ required: true, message: t('projects.regionRequired') }]}
            >
              <Select
                placeholder={t('projects.selectRegion')}
                onChange={(value) => handleRegionChange(value as string)}
                value={selectedRegion}
              >
                {japanRegions.map(region => (
                  <Option key={region.value} value={region.value}>
                    {region.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="prefecture"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.prefecture')} ({t('projects.prefectureUnit')})
              </>}
              rules={[{ required: true, message: t('projects.prefectureRequired') }]}
            >
              <Select
                placeholder={t('projects.selectPrefecture')}
                disabled={!selectedRegion}
                onChange={(value) => handlePrefectureChange(value as string)}
                value={selectedPrefecture}
              >
                {availablePrefectures.map(prefecture => (
                  <Option key={prefecture.value} value={prefecture.value}>
                    {prefecture.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="address"
          label={t('projects.detailedAddress')}
        >
          <Input placeholder={t('projects.detailedAddressPlaceholder')} />
        </Form.Item>

        <Form.Item
          name="postalCode"
          label={t('projects.postalCode')}
        >
          <Input placeholder={t('projects.postalCodePlaceholder')} />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="installationType"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.installationType')}
              </>}
              rules={[{ required: true, message: t('projects.installationTypeRequired') }]}
            >
              <Select placeholder={t('projects.selectInstallationType')}>
                <Option value="rooftop">屋顶式</Option>
                <Option value="ground">地面式</Option>
                <Option value="floating">水面式</Option>
                <Option value="building">建筑一体化</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="projectType"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.projectType')}
              </>}
              rules={[{ required: true, message: t('projects.projectTypeRequired') }]}
            >
              <Select placeholder={t('projects.selectProjectType')}>
                <Option value="commercial">商业项目</Option>
                <Option value="residential">住宅项目</Option>
                <Option value="industrial">工业项目</Option>
                <Option value="utility">公用事业项目</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="connectionType"
              label={<>
                <span style={{ color: '#ff4d4f' }}>*</span> {t('projects.connectionType')}
              </>}
              rules={[{ required: true, message: t('projects.connectionTypeRequired') }]}
            >
              <Select placeholder={t('projects.selectConnectionType')}>
                <Option value="gridTied">并网</Option>
                <Option value="offGrid">离网</Option>
                <Option value="hybrid">混合式</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="estimatedPower"
              label={t('projects.estimatedPower') + ' (kW)'}
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                style={{ width: '100%' }}
                placeholder="0.0"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="estimatedCapacity"
              label={t('projects.estimatedCapacity') + ' (kWh)'}
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                style={{ width: '100%' }}
                placeholder="0.0"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="estimatedInvestment"
              label={t('projects.estimatedInvestment') + ' (万日元)'}
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                style={{ width: '100%' }}
                placeholder="0.0"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label={t('projects.description')}
        >
          <TextArea
            rows={4}
            placeholder={t('projects.descriptionPlaceholder')}
          />
        </Form.Item>

        <Form.Item
          name="manager"
          label={t('projects.manager')}
        >
          <Input placeholder={t('projects.managerPlaceholder')} />
        </Form.Item>
      </Form>
    </div>
  );
};

export default BasicInfoStep;
