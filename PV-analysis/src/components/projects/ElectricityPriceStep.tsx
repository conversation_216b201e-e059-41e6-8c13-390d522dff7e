import React, { useEffect, useState } from 'react';
import { Table, Checkbox, Input, Button, Space, Alert, Card, Row, Col, Tag, Select, Divider } from 'antd';
import { SearchOutlined, EditOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { ElectricityPriceChart } from '../../components/charts';
import { ElectricityPrice } from '../../types/database';
import { EmptyState, StepAlert } from '../../components/common';
import { getElectricityPriceList, getElectricityPriceDetail } from '../../services/electricityPriceService';
import { fetchDataStart, fetchElectricityPricesSuccess } from '../../store/slices/databasesSlice';

const { Option } = Select;

interface ElectricityPriceStepProps {
  data?: string;
  onChange: (dataId: string) => void;
  onValidate: (valid: boolean) => void;
}

const ElectricityPriceStep: React.FC<ElectricityPriceStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { electricityPrices } = useAppSelector((state) => state.databases);

  const [selectedDataId, setSelectedDataId] = useState<string | undefined>(data);
  const [searchText, setSearchText] = useState('');
  const [selectedData, setSelectedData] = useState<ElectricityPrice | null>(null);
  const [viewMode, setViewMode] = useState<'daily' | 'monthly'>('daily');
  const [currentSeasonId, setCurrentSeasonId] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 获取电价政策列表
  const fetchElectricityPriceList = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getElectricityPriceList();
      dispatch(fetchElectricityPricesSuccess(response.items));
      console.log('ElectricityPriceStep: 成功获取电价政策列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('ElectricityPriceStep: 获取电价政策列表失败:', error);
      dispatch(fetchElectricityPricesSuccess([]));
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    // 如果Redux store中没有数据，则从服务器获取
    if (electricityPrices.length === 0) {
      console.log('ElectricityPriceStep: Redux store中没有电价政策数据，从服务器获取');
      fetchElectricityPriceList();
    } else {
      console.log('ElectricityPriceStep: Redux store中已有电价政策数据，数据条数:', electricityPrices.length);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 初始化选中数据
  useEffect(() => {
    if (data) {
      setSelectedDataId(data);
      const selected = electricityPrices.find(item => item.id === data);
      if (selected) {
        setSelectedData(selected);

        // 根据政策类型设置视图模式
        if (selected.policyType === 'seasonal') {
          setViewMode('monthly');
          // 如果有季节政策，设置当前季节ID
          if (selected.seasonalPolicies && selected.seasonalPolicies.length > 0) {
            setCurrentSeasonId(selected.seasonalPolicies[0].id);
          }
        } else {
          setViewMode('daily');
        }
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, electricityPrices]);

  // 验证选择
  useEffect(() => {
    onValidate(!!selectedDataId);
  }, [selectedDataId, onValidate]);

  // 处理选择变更
  const handleSelectionChange = (dataId: string) => {
    setSelectedDataId(dataId);
    onChange(dataId);

    // 查找选中的数据
    const selected = electricityPrices.find(item => item.id === dataId);
    if (selected) {
      setSelectedData(selected);

      // 根据政策类型设置视图模式
      if (selected.policyType === 'seasonal') {
        setViewMode('monthly');
        // 如果有季节政策，设置当前季节ID
        if (selected.seasonalPolicies && selected.seasonalPolicies.length > 0) {
          setCurrentSeasonId(selected.seasonalPolicies[0].id);
        }
      } else {
        setViewMode('daily');
      }
    }
  };

  // 过滤数据
  const filteredData = electricityPrices.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase()) ||
    item.region.toLowerCase().includes(searchText.toLowerCase()) ||
    (item.provider && item.provider.toLowerCase().includes(searchText.toLowerCase()))
  );

  // 表格列定义
  const columns = [
    {
      title: '',
      dataIndex: 'select',
      key: 'select',
      width: 50,
      render: (_: any, record: any) => (
        <Checkbox
          checked={selectedDataId === record.id}
          onChange={() => handleSelectionChange(record.id)}
        />
      ),
    },
    {
      title: t('electricityPrice.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('electricityPrice.region'),
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: t('electricityPrice.policyType'),
      dataIndex: 'policyType',
      key: 'policyType',
      render: (type: 'fixed' | 'seasonal') => (
        <Tag color={type === 'fixed' ? 'blue' : 'green'}>
          {type === 'fixed' ? t('electricityPrice.fixed') : t('electricityPrice.seasonal')}
        </Tag>
      ),
    },
    {
      title: t('electricityPrice.rulesCount'),
      key: 'rulesCount',
      render: (_: any, record: ElectricityPrice) => {
        if (record.policyType === 'fixed') {
          return record.rules?.length || 0;
        } else {
          return record.seasonalPolicies?.length || 0;
        }
      },
    },
    {
      title: t('electricityPrice.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleDateString(),
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status: 'synced' | 'local-only' | 'server-only') => {
        if (status === 'synced') {
          return <Tag color="success">{t('common.synced')}</Tag>;
        } else if (status === 'local-only') {
          return <Tag color="warning">{t('common.localOnly')}</Tag>;
        } else {
          return <Tag color="blue">{t('common.serverOnly')}</Tag>;
        }
      },
    },
  ];

  // 渲染季节选择器
  const renderSeasonSelector = () => {
    if (!selectedData) {
      return null;
    }

    // 如果是统一政策类型
    if (selectedData.policyType === 'fixed') {
      return (
        <Select
          value="unified"
          disabled
          style={{ width: 150, marginLeft: 16 }}
        >
          <Option value="unified">{t('electricityPrice.fixed')}</Option>
        </Select>
      );
    }

    // 如果是分季节政策类型但没有季节数据
    if (!selectedData.seasonalPolicies || selectedData.seasonalPolicies.length === 0) {
      return (
        <Select
          disabled
          value="none"
          style={{ width: 150, marginLeft: 16 }}
        >
          <Option value="none">{t('electricityPrice.noSeasons')}</Option>
        </Select>
      );
    }

    // 分季节政策类型且有季节数据
    return (
      <Select
        value={currentSeasonId || selectedData.seasonalPolicies[0].id}
        onChange={setCurrentSeasonId}
        style={{ width: 150, marginLeft: 16 }}
      >
        {selectedData.seasonalPolicies.map(season => (
          <Option key={season.id} value={season.id}>
            {season.name}
            {season.months && season.months.length > 0 &&
              ` (${season.months.join('、')}月)`}
          </Option>
        ))}
      </Select>
    );
  };

  // 渲染数据统计
  const renderDataStatistics = () => {
    if (!selectedData) return null;

    return (
      <div style={{ marginTop: 24 }}>
        <Divider orientation="left">{t('electricityPrice.dataStatistics')}</Divider>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
          <Card title={t('electricityPrice.name')} style={{ width: 300 }}>
            {selectedData.name}
          </Card>
          <Card title={t('electricityPrice.region')} style={{ width: 300 }}>
            {selectedData.region}
          </Card>
          <Card title={t('electricityPrice.policyType')} style={{ width: 300 }}>
            <Tag color={selectedData.policyType === 'fixed' ? 'blue' : 'green'}>
              {selectedData.policyType === 'fixed'
                ? t('electricityPrice.fixed')
                : t('electricityPrice.seasonal')}
            </Tag>
          </Card>
          {selectedData.policyType === 'fixed' ? (
            <Card title={t('electricityPrice.rulesCount')} style={{ width: 300 }}>
              {selectedData.rules?.length || 0}
            </Card>
          ) : (
            <Card title={t('electricityPrice.seasonsCount')} style={{ width: 300 }}>
              {selectedData.seasonalPolicies?.length || 0}
            </Card>
          )}
        </div>
      </div>
    );
  };

  // 获取未完成的必填字段
  const getIncompleteFields = () => {
    const incompleteFields: string[] = [];

    if (!selectedDataId) {
      incompleteFields.push(t('electricityPrice.policySelection'));
    }

    return incompleteFields;
  };

  return (
    <div>
      <StepAlert
        message={t('projectWizard.required')}
        description={t('projectWizard.electricityPriceDescription')}
        type="info"
        showIcon
        incompleteFields={getIncompleteFields()}
      />

      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Input
            placeholder={t('common.search')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '300px' }}
          />
          <Button
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              // 使用自定义事件通知父组件有导航尝试
              const event = new CustomEvent('navigation-attempt', {
                detail: { targetPath: '/databases/electricity-price' },
                bubbles: true,
                cancelable: true
              });
              window.dispatchEvent(event);
            }}
          >
            {t('databases.electricityPrice.addNew')}
          </Button>
        </Space>
      </div>

      <Card
        title={t('electricityPrice.policyList')}
        style={{ marginBottom: 16 }}
        extra={
          <Button
            type="text"
            icon={<SyncOutlined spin={isLoading} />}
            onClick={fetchElectricityPriceList}
            disabled={isLoading}
          >
            {t('common.refresh')}
          </Button>
        }
      >
        <Table
          rowKey="id"
          columns={columns}
          dataSource={filteredData}
          pagination={{ pageSize: 5 }}
          loading={isLoading}
          onRow={(record) => ({
            onClick: () => handleSelectionChange(record.id),
            style: {
              cursor: 'pointer',
              background: selectedDataId === record.id ? '#f0f7ff' : undefined
            }
          })}
          locale={{ emptyText: t('electricityPrice.noData') }}
        />
      </Card>

      <Card
        title={t('electricityPrice.priceVisualization')}
        extra={
          <Space>
            {selectedData?.policyType === 'seasonal' ? (
              <Select
                value={viewMode}
                onChange={setViewMode}
                style={{ width: 120 }}
              >
                <Option value="monthly">{t('chart.month')}</Option>
              </Select>
            ) : (
              <Select
                value="daily"
                disabled
                style={{ width: 120 }}
              >
                <Option value="daily">{t('electricityPrice.fixed')}</Option>
              </Select>
            )}
            {renderSeasonSelector()}
          </Space>
        }
      >
        {selectedData ? (
          <>
            <ElectricityPriceChart
              data={selectedData}
              viewMode={viewMode}
              seasonId={currentSeasonId}
            />
            {renderDataStatistics()}
          </>
        ) : (
          <EmptyState
            title={t('electricityPrice.noPolicySelected')}
            description={t('electricityPrice.selectPolicyPrompt')}
          />
        )}
      </Card>

      {!selectedDataId && (
        <Alert
          message={t('projectWizard.selectionRequired')}
          description={t('projectWizard.electricityPriceSelectionRequired')}
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}
    </div>
  );
};

export default ElectricityPriceStep;
