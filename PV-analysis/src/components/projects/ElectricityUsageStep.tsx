import React, { useState, useEffect } from 'react';
import { Card, Tabs, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { ElectricityUsage } from '../../types/project';
import { ProjectHourlyData } from '../../types/projectData';
import ElectricityUsageInput, { ElectricityUsageData } from '../common/ElectricityUsageInput';
import ElectricityUsageChart from '../common/ElectricityUsageChart';
import { StepAlert } from '../../components/common';
import { calculateHourlyElectricityUsage } from '../../utils/electricityUsageCalculator';

const { TabPane } = Tabs;

interface ElectricityUsageStepProps {
  data?: ElectricityUsage;
  onChange: (data: ElectricityUsage) => void;
  onValidate: (valid: boolean) => void;
}

const ElectricityUsageStep: React.FC<ElectricityUsageStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const [isValid, setIsValid] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [hourlyData, setHourlyData] = useState<number[]>([]);
  const [chartData, setChartData] = useState<{ hour: number; value: number }[]>([]);
  const [viewMonth, setViewMonth] = useState<number>(1); // 默认显示1月
  const [viewDay, setViewDay] = useState<number>(1); // 默认显示1日
  const [viewType, setViewType] = useState<'hourly' | 'daily' | 'monthly'>('hourly');

  // 当用电数据变化时，计算8760个小时的用电量
  useEffect(() => {
    if (data && data.data && data.data.length > 0) {
      setIsCalculating(true);

      // 使用setTimeout避免UI阻塞
      setTimeout(() => {
        try {
          // 计算8760个小时的用电量
          const calculatedData = calculateHourlyElectricityUsage(data);
          setHourlyData(calculatedData);

          // 更新图表数据
          updateChartData(calculatedData, viewType, viewMonth, viewDay);
        } catch (error) {
          console.error('计算用电量数据失败:', error);
        } finally {
          setIsCalculating(false);
        }
      }, 0);
    }
  }, [data]);

  // 更新图表数据
  const updateChartData = (
    hourlyData: number[],
    type: 'hourly' | 'daily' | 'monthly',
    month: number,
    day: number
  ) => {
    if (hourlyData.length === 0) return;

    // 每月的天数（非闰年）
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    switch (type) {
      case 'hourly': {
        // 计算选定日期的小时索引
        let hourIndex = 0;
        for (let m = 0; m < month - 1; m++) {
          hourIndex += daysInMonth[m] * 24;
        }
        hourIndex += (day - 1) * 24;

        // 提取当天24小时的数据
        const dayData = [];
        for (let h = 0; h < 24; h++) {
          dayData.push({
            hour: h,
            value: hourlyData[hourIndex + h] || 0
          });
        }
        setChartData(dayData);
        break;
      }

      case 'daily': {
        // 计算选定月份的日索引
        let dayStartIndex = 0;
        for (let m = 0; m < month - 1; m++) {
          dayStartIndex += daysInMonth[m];
        }

        // 计算每天的总用电量
        const monthDailyData = [];
        for (let d = 0; d < daysInMonth[month - 1]; d++) {
          let dailySum = 0;
          const dayHourIndex = (dayStartIndex + d) * 24;

          for (let h = 0; h < 24; h++) {
            dailySum += hourlyData[dayHourIndex + h] || 0;
          }

          monthDailyData.push({
            hour: d + 1, // 使用hour字段表示日期
            value: dailySum
          });
        }
        setChartData(monthDailyData);
        break;
      }

      case 'monthly': {
        // 计算每月的总用电量
        const monthlyData = [];
        let hourIndex = 0;

        for (let m = 0; m < 12; m++) {
          let monthlySum = 0;

          for (let d = 0; d < daysInMonth[m]; d++) {
            for (let h = 0; h < 24; h++) {
              monthlySum += hourlyData[hourIndex] || 0;
              hourIndex++;
            }
          }

          monthlyData.push({
            hour: m + 1, // 使用hour字段表示月份
            value: monthlySum
          });
        }
        setChartData(monthlyData);
        break;
      }
    }
  };

  // 处理图表选项变更
  const handleChartOptionsChange = (options: any) => {
    setViewType(options.viewType);
    setViewMonth(options.month);
    setViewDay(options.day);

    // 更新图表数据
    updateChartData(hourlyData, options.viewType, options.month, options.day);
  };

  // 处理数据变更
  const handleDataChange = (newData: ElectricityUsageData) => {
    // 将 ElectricityUsageData 转换为 ElectricityUsage
    const updatedData: ElectricityUsage = {
      type: newData.type,
      data: newData.data,
    };

    // 更新数据
    onChange(updatedData);
  };

  // 处理验证
  const handleValidate = (valid: boolean) => {
    setIsValid(valid);
    onValidate(valid);
  };

  // 获取未完成的必填字段
  const getIncompleteFields = () => {
    const incompleteFields: string[] = [];

    if (!data || !data.data || data.data.length === 0) {
      incompleteFields.push(t('electricityUsage.usageData'));
    }

    return incompleteFields;
  };

  return (
    <div>
      <StepAlert
        message={t('projectWizard.required')}
        description={t('projectWizard.electricityUsageDescription')}
        type="info"
        showIcon
        incompleteFields={getIncompleteFields()}
      />

      <Tabs defaultActiveKey="input">
        <TabPane tab={t('electricityUsage.inputData')} key="input">
          <ElectricityUsageInput
            value={data as ElectricityUsageData}
            onChange={handleDataChange}
            onValidate={handleValidate}
          />
        </TabPane>
        <TabPane tab={t('electricityUsage.visualization')} key="visualization">
          {isCalculating ? (
            <Card>
              <div style={{ display: 'flex', justifyContent: 'center', padding: '40px 0' }}>
                <Spin tip={t('common.calculating')} />
              </div>
            </Card>
          ) : hourlyData.length > 0 ? (
            <ElectricityUsageChart
              data={chartData}
              type={viewType}
              onOptionsChange={handleChartOptionsChange}
            />
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                {t('electricityUsage.noDataToVisualize')}
              </div>
            </Card>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ElectricityUsageStep;
