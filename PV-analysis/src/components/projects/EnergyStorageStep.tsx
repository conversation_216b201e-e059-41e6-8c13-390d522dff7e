import React, { useEffect, useState } from 'react';
import { Table, Button, Input, Select, Card, Form, InputNumber, Space, Alert, Modal, Row, Col, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { EnergyStorage } from '../../types';
import { fetchDataStart, fetchEquipmentSuccess } from '../../store/slices/databasesSlice';
import { getEquipmentList } from '../../services/equipmentService';

const { Option } = Select;

interface EnergyStorageStepProps {
  data?: EnergyStorage[];
  onChange: (data: EnergyStorage[]) => void;
  onValidate: (valid: boolean) => void;
}

const EnergyStorageStep: React.FC<EnergyStorageStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const { equipment } = useAppSelector((state) => state.databases);

  const [storageDevices, setStorageDevices] = useState<EnergyStorage[]>(data || []);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDevice, setEditingDevice] = useState<EnergyStorage | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedEquipment, setSelectedEquipment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 过滤设备列表，只显示储能设备
  const storageEquipment = equipment.filter(item => item.type === 'storage');

  // 获取设备列表
  const fetchEquipmentData = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getEquipmentList();
      dispatch(fetchEquipmentSuccess(response.items));
      console.log('EnergyStorageStep: 成功获取设备列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('EnergyStorageStep: 获取设备列表失败:', error);
      dispatch(fetchEquipmentSuccess([]));
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    // 如果Redux store中没有数据，则从服务器获取
    if (equipment.length === 0) {
      console.log('EnergyStorageStep: Redux store中没有设备数据，从服务器获取');
      fetchEquipmentData();
    } else {
      console.log('EnergyStorageStep: Redux store中已有设备数据，数据条数:', equipment.length);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 初始化数据
  useEffect(() => {
    if (data) {
      setStorageDevices(data);
    }
  }, [data]);

  // 验证数据
  useEffect(() => {
    // 储能设备是可选的，所以始终返回true
    onValidate(true);
  }, [storageDevices, onValidate]);

  // 处理添加设备
  const handleAddDevice = () => {
    setEditingDevice(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑设备
  const handleEditDevice = (device: EnergyStorage) => {
    setEditingDevice(device);
    form.setFieldsValue(device);
    setModalVisible(true);
  };

  // 处理删除设备
  const handleDeleteDevice = (id: string) => {
    const newDevices = storageDevices.filter(device => device.id !== id);
    setStorageDevices(newDevices);
    onChange(newDevices);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingDevice) {
        // 更新现有设备
        const newDevices = storageDevices.map(device =>
          device.id === editingDevice.id ? { ...device, ...values } : device
        );
        setStorageDevices(newDevices);
        onChange(newDevices);
      } else {
        // 添加新设备
        const newDevice: EnergyStorage = {
          id: Date.now().toString(),
          ...values,
        };
        const newDevices = [...storageDevices, newDevice];
        setStorageDevices(newDevices);
        onChange(newDevices);
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理设备选择
  const handleEquipmentSelect = (equipmentId: string) => {
    const selected = equipment.find(item => item.id === equipmentId);
    if (selected) {
      setSelectedEquipment(selected);

      // 填充表单
      form.setFieldsValue({
        name: selected.name,
        manufacturer: selected.manufacturer,
        model: selected.model,
        capacity: selected.specs.capacity,
        power: selected.specs.power,
        efficiency: selected.specs.efficiency,
        cycles: selected.specs.cycles,
        price: selected.price,
        quantity: 1,
      });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('energyStorage.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('energyStorage.manufacturer'),
      dataIndex: 'manufacturer',
      key: 'manufacturer',
    },
    {
      title: t('energyStorage.model'),
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: t('energyStorage.capacity') + ' (kWh)',
      dataIndex: 'capacity',
      key: 'capacity',
    },
    {
      title: t('energyStorage.power') + ' (kW)',
      dataIndex: 'power',
      key: 'power',
    },
    {
      title: t('energyStorage.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: t('energyStorage.totalCapacity') + ' (kWh)',
      key: 'totalCapacity',
      render: (text: string, record: EnergyStorage) => (record.capacity * record.quantity).toFixed(1),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (text: string, record: EnergyStorage) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditDevice(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteDevice(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 渲染设备表单
  const renderDeviceForm = () => {
    return (
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="equipmentId"
              label={t('energyStorage.selectEquipment')}
            >
              <Select
                placeholder={t('energyStorage.selectEquipmentPlaceholder')}
                onChange={handleEquipmentSelect}
                showSearch
                filterOption={(input, option) =>
                  option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {storageEquipment.map(item => (
                  <Option key={item.id} value={item.id}>{item.name} - {item.manufacturer}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('energyStorage.basicInfo')}</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={t('energyStorage.name')}
              rules={[{ required: true, message: t('energyStorage.nameRequired') }]}
            >
              <Input placeholder={t('energyStorage.namePlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="manufacturer"
              label={t('energyStorage.manufacturer')}
              rules={[{ required: true, message: t('energyStorage.manufacturerRequired') }]}
            >
              <Input placeholder={t('energyStorage.manufacturerPlaceholder')} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="model"
              label={t('energyStorage.model')}
              rules={[{ required: true, message: t('energyStorage.modelRequired') }]}
            >
              <Input placeholder={t('energyStorage.modelPlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="quantity"
              label={t('energyStorage.quantity')}
              rules={[{ required: true, message: t('energyStorage.quantityRequired') }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('energyStorage.specifications')}</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="capacity"
              label={t('energyStorage.capacity') + ' (kWh)'}
              rules={[{ required: true, message: t('energyStorage.capacityRequired') }]}
            >
              <InputNumber min={0} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="power"
              label={t('energyStorage.power') + ' (kW)'}
              rules={[{ required: true, message: t('energyStorage.powerRequired') }]}
            >
              <InputNumber min={0} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="efficiency"
              label={t('energyStorage.efficiency') + ' (%)'}
              rules={[{ required: true, message: t('energyStorage.efficiencyRequired') }]}
            >
              <InputNumber min={0} max={100} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="cycles"
              label={t('energyStorage.cycles')}
              rules={[{ required: true, message: t('energyStorage.cyclesRequired') }]}
            >
              <InputNumber min={0} step={100} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="price"
              label={t('energyStorage.price') + ' (JPY)'}
              rules={[{ required: true, message: t('energyStorage.priceRequired') }]}
            >
              <InputNumber min={0} step={1000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  return (
    <div>
      <Alert
        message={t('projectWizard.optional')}
        description={t('projectWizard.energyStorageDescription')}
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Input
            placeholder={t('common.search')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '300px' }}
          />
          <Button
            type="text"
            icon={<SyncOutlined spin={isLoading} />}
            onClick={fetchEquipmentData}
            disabled={isLoading}
          >
            {t('common.refresh')}
          </Button>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddDevice}
        >
          {t('energyStorage.addDevice')}
        </Button>
      </div>

      <Card
        title={t('energyStorage.equipmentList')}
        style={{ marginBottom: '16px' }}
      >
        <Table
          dataSource={storageEquipment}
          columns={[
            {
              title: t('energyStorage.name'),
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: t('energyStorage.manufacturer'),
              dataIndex: 'manufacturer',
              key: 'manufacturer',
            },
            {
              title: t('energyStorage.model'),
              dataIndex: 'model',
              key: 'model',
            },
            {
              title: t('energyStorage.capacity') + ' (kWh)',
              dataIndex: 'specs.capacity',
              key: 'capacity',
              render: (text: string, record: any) => record.specs?.capacity || '-',
            },
            {
              title: t('common.actions'),
              key: 'actions',
              render: (text: string, record: any) => (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    setSelectedEquipment(record);
                    form.resetFields();
                    form.setFieldsValue({
                      name: record.name,
                      manufacturer: record.manufacturer,
                      model: record.model,
                      capacity: record.specs.capacity,
                      power: record.specs.power,
                      efficiency: record.specs.efficiency,
                      cycles: record.specs.cycles,
                      price: record.price,
                      quantity: 1,
                    });
                    setModalVisible(true);
                  }}
                >
                  {t('common.select')}
                </Button>
              ),
            },
          ]}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          loading={isLoading}
          locale={{ emptyText: t('energyStorage.noEquipment') }}
        />
      </Card>

      <Divider>{t('energyStorage.selectedDevices')}</Divider>

      <Table
        dataSource={storageDevices}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 5 }}
        locale={{ emptyText: t('energyStorage.noDevices') }}
      />

      {storageDevices.length > 0 && (
        <Card style={{ marginTop: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('energyStorage.summary')}</div>
              <div>{t('energyStorage.totalDevices')}: {storageDevices.length}</div>
              <div>{t('energyStorage.totalQuantity')}: {storageDevices.reduce((sum, device) => sum + device.quantity, 0)}</div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('energyStorage.totalCapacity')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {storageDevices.reduce((sum, device) => sum + (device.capacity * device.quantity), 0).toFixed(1)} kWh
              </div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('energyStorage.totalCost')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {storageDevices.reduce((sum, device) => sum + (device.price * device.quantity), 0).toLocaleString()} JPY
              </div>
            </div>
          </div>
        </Card>
      )}

      <Modal
        title={editingDevice ? t('energyStorage.editDevice') : t('energyStorage.addDevice')}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        {renderDeviceForm()}
      </Modal>
    </div>
  );
};

export default EnergyStorageStep;
