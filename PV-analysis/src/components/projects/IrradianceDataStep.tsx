import React, { useEffect, useState } from 'react';
import { Table, Checkbox, Input, Button, Space, Card, Row, Col, Tag, Select, Slider, Divider, Alert } from 'antd';
import { SearchOutlined, UploadOutlined, DownloadOutlined, EditOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { IrradianceChart } from '../../components/charts';
import { IrradianceData, IrradianceVisualizationOptions } from '../../types/database';
import { setIrradianceVisualizationOptions, fetchDataStart, fetchIrradianceDataSuccess } from '../../store/slices/databasesSlice';
import { EmptyState, StepAlert } from '../../components/common';
import { getIrradianceList, getIrradianceDetail } from '../../services/irradianceService';

const { Option } = Select;

interface IrradianceDataStepProps {
  data?: string;
  onChange: (dataId: string) => void;
  onValidate: (valid: boolean) => void;
}

const IrradianceDataStep: React.FC<IrradianceDataStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { irradianceData, irradianceVisualizationOptions } = useAppSelector((state) => state.databases);

  const [selectedDataId, setSelectedDataId] = useState<string | undefined>(data);
  const [searchText, setSearchText] = useState('');
  const [selectedData, setSelectedData] = useState<IrradianceData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 获取光照数据列表
  const fetchIrradianceDataList = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getIrradianceList();
      dispatch(fetchIrradianceDataSuccess(response.items));
      console.log('IrradianceDataStep: 成功获取光照数据列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('IrradianceDataStep: 获取光照数据列表失败:', error);
      dispatch(fetchIrradianceDataSuccess([]));
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    // 如果Redux store中没有数据，则从服务器获取
    if (irradianceData.length === 0) {
      console.log('IrradianceDataStep: Redux store中没有光照数据，从服务器获取');
      fetchIrradianceDataList();
    } else {
      console.log('IrradianceDataStep: Redux store中已有光照数据，数据条数:', irradianceData.length);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 初始化选中数据
  useEffect(() => {
    if (data) {
      setSelectedDataId(data);
      const selected = irradianceData.find(item => item.id === data);
      if (selected) {
        setSelectedData(selected);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, irradianceData]);

  // 验证选择
  useEffect(() => {
    onValidate(!!selectedDataId);
  }, [selectedDataId, onValidate]);

  // 处理选择变更
  const handleSelectionChange = (dataId: string) => {
    setSelectedDataId(dataId);
    onChange(dataId);

    // 查找选中的数据
    const selected = irradianceData.find(item => item.id === dataId);
    if (selected) {
      setSelectedData(selected);
    }
  };

  // 处理可视化选项变更
  const handleVisualizationOptionsChange = (options: Partial<IrradianceVisualizationOptions>) => {
    dispatch(setIrradianceVisualizationOptions(options));
  };

  // 过滤数据
  const filteredData = irradianceData.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase()) ||
    item.location.toLowerCase().includes(searchText.toLowerCase())
  );

  // 表格列定义
  const columns = [
    {
      title: '',
      dataIndex: 'select',
      key: 'select',
      width: 50,
      render: (_: any, record: any) => (
        <Checkbox
          checked={selectedDataId === record.id}
          onChange={() => handleSelectionChange(record.id)}
        />
      ),
    },
    {
      title: t('irradiance.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('irradiance.location'),
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: t('irradiance.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleDateString(),
    },
    {
      title: t('irradiance.year'),
      dataIndex: 'year',
      key: 'year',
    },
    {
      title: t('irradiance.dataSize'),
      dataIndex: 'dataSize',
      key: 'dataSize',
      render: (text: number) => `${text}条记录`,
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status: 'synced' | 'local-only' | 'server-only' | 'invalid') => {
        if (status === 'synced') {
          return <Tag color="success">{t('common.synced')}</Tag>;
        } else if (status === 'local-only') {
          return <Tag color="warning">{t('common.localOnly')}</Tag>;
        } else {
          return <Tag color="blue">{t('common.serverOnly')}</Tag>;
        }
      },
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: any) => (
        <Space size="small">
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleSelectionChange(record.id);
            }}
          />
        </Space>
      ),
    },
  ];

  // 渲染可视化选项
  const renderVisualizationOptions = () => {
    const { metric, viewMode, month, day } = irradianceVisualizationOptions;

    return (
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '16px', marginBottom: 16 }}>
          {/* 数据指标 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('irradiance.metric')}:</span>
            <Select
              value={metric}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ metric: value })}
            >
              <Option value="G_Gh">{t('irradiance.globalHorizontalIrradiance')}</Option>
              <Option value="Ta">{t('irradiance.temperature')}</Option>
              <Option value="Sd">{t('irradiance.directTime')}</Option>
            </Select>
          </div>

          {/* 查看方式 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('irradiance.viewMode')}:</span>
            <Select
              value={viewMode}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ viewMode: value })}
            >
              <Option value="hourly">{t('irradiance.hourly')}</Option>
              <Option value="daily">{t('irradiance.daily')}</Option>
              <Option value="monthly">{t('irradiance.monthly')}</Option>
            </Select>
          </div>

          {/* 月份选择 - 仅在非月视图时显示 */}
          {viewMode !== 'monthly' && (
            <div>
              <span style={{ marginRight: 8 }}>{t('irradiance.month')}:</span>
              <Select
                value={month !== undefined ? month : 1}
                style={{ width: 80 }}
                onChange={(value) => handleVisualizationOptionsChange({ month: value })}
              >
                <Option key={0} value={0}>{t('irradiance.allYear')}</Option>
                {Array.from({ length: 12 }, (_, i) => i + 1).map(m => (
                  <Option key={m} value={m}>{m}{t('irradiance.monthUnit')}</Option>
                ))}
              </Select>
            </div>
          )}
        </div>

        {/* 日期滑块 - 仅在小时视图时显示 */}
        {viewMode === 'hourly' && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <span style={{ marginRight: 8, minWidth: '40px' }}>{t('irradiance.day')}:</span>
            <Slider
              min={1}
              max={31}
              value={day || 1}
              style={{ flex: 1, marginRight: 8 }}
              onChange={(value) => handleVisualizationOptionsChange({ day: value })}
            />
            <span style={{ minWidth: '40px', textAlign: 'right' }}>{day || 1}{t('irradiance.dayUnit')}</span>
          </div>
        )}
      </div>
    );
  };

  // 渲染数据统计
  const renderDataStatistics = () => {
    if (!selectedData) return null;

    return (
      <div style={{ marginTop: 24 }}>
        <Divider orientation="left">{t('irradiance.dataStatistics')}</Divider>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
          <Card title={t('irradiance.name')} style={{ width: 300 }}>
            {selectedData.name}
          </Card>
          <Card title={t('irradiance.location')} style={{ width: 300 }}>
            {selectedData.location}
          </Card>
          <Card title={t('irradiance.coordinates')} style={{ width: 300 }}>
            {selectedData.latitude}, {selectedData.longitude}
          </Card>
        </div>
      </div>
    );
  };

  // 获取未完成的必填字段
  const getIncompleteFields = () => {
    const incompleteFields: string[] = [];

    if (!selectedDataId) {
      incompleteFields.push(t('irradiance.datasetSelection'));
    }

    return incompleteFields;
  };

  return (
    <div>
      <StepAlert
        message={t('projectWizard.required')}
        description={t('projectWizard.irradianceDataDescription')}
        type="info"
        showIcon
        incompleteFields={getIncompleteFields()}
      />

      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Input
            placeholder={t('common.search')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '300px' }}
          />
          <Button
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              // 使用自定义事件通知父组件有导航尝试
              const event = new CustomEvent('navigation-attempt', {
                detail: { targetPath: '/databases/irradiance' },
                bubbles: true,
                cancelable: true
              });
              window.dispatchEvent(event);
            }}
          >
            {t('irradiance.upload')}
          </Button>
        </Space>
      </div>

      <Card
        title={t('irradiance.datasetList')}
        style={{ marginBottom: 16 }}
        extra={
          <Button
            type="text"
            icon={<SyncOutlined spin={isLoading} />}
            onClick={fetchIrradianceDataList}
            disabled={isLoading}
          >
            {t('common.refresh')}
          </Button>
        }
      >
        <Table
          rowKey="id"
          columns={columns}
          dataSource={filteredData}
          pagination={{ pageSize: 10 }}
          loading={isLoading}
          onRow={(record) => ({
            onClick: () => handleSelectionChange(record.id),
            style: {
              cursor: 'pointer',
              background: selectedDataId === record.id ? '#f0f7ff' : undefined
            }
          })}
          locale={{ emptyText: t('irradiance.noData') }}
        />
      </Card>

      <Card title={t('irradiance.dataVisualization')}>
        {selectedData ? (
          <>
            {renderVisualizationOptions()}
            <IrradianceChart
              data={selectedData}
              options={irradianceVisualizationOptions}
            />
            {renderDataStatistics()}
          </>
        ) : (
          <EmptyState
            title={t('irradiance.noDataSelected')}
            description={t('irradiance.selectDataPrompt')}
          />
        )}
      </Card>

      {!selectedDataId && (
        <Alert
          message={t('projectWizard.selectionRequired')}
          description={t('projectWizard.irradianceSelectionRequired')}
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}
    </div>
  );
};

export default IrradianceDataStep;
