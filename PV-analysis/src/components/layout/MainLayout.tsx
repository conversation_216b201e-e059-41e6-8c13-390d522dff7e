import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, MenuProps } from 'antd';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ProjectOutlined,
  DatabaseOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../store';
import { setLanguage } from '../../store/slices/settingsSlice';
import { logout } from '../../store/slices/authSlice';

const { Header, Sider, Content } = Layout;

const MainLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { siteName, siteLogo } = useAppSelector((state) => state.settings);

  // 侧边栏折叠状态
  const [collapsed, setCollapsed] = useState(false);

  // 菜单主题状态
  const [menuTheme, setMenuTheme] = useState<'light' | 'dark'>(
    document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  );

  // 切换侧边栏折叠状态
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 导航拦截状态 - 不需要跟踪状态，只需要触发事件

  // 导航拦截函数
  const interceptNavigation = (path: string) => {
    // 确保路径是绝对路径，而不是相对于当前路径
    const absolutePath = path.startsWith('/') ? path : `/${path}`;

    // 检查当前路径是否为新建项目页面或编辑项目页面
    const isInProjectCreation = location.pathname === '/projects/new' || location.pathname.startsWith('/projects/edit/');

    // 检查当前路径是否已经是错误的嵌套路径（如/projects/new/settings）
    const isInNestedPath = location.pathname.includes('/projects/new/') ||
                          (location.pathname.includes('/projects/edit/') && location.pathname.split('/').length > 4);

    console.log('当前路径:', location.pathname, '目标路径:', absolutePath);
    console.log('是否在项目创建流程中:', isInProjectCreation, '是否在嵌套路径中:', isInNestedPath);

    // 如果当前在嵌套路径中（如/projects/new/settings），需要特殊处理
    if (isInNestedPath) {
      console.log('检测到嵌套路径，强制导航到:', absolutePath);
      // 使用window.location强制导航，完全刷新页面
      window.location.href = absolutePath;
      return;
    }

    // 如果在项目创建流程中，使用自定义事件通知项目页面
    if (isInProjectCreation) {
      console.log('触发导航拦截，目标路径:', absolutePath);

      // 使用自定义事件通知项目页面
      const event = new CustomEvent('navigation-attempt', {
        detail: { targetPath: absolutePath },
        bubbles: true,
        cancelable: true
      });
      window.dispatchEvent(event);

      // 不立即导航，等待项目页面处理
      return;
    }

    // 如果不是项目编辑页面，直接导航
    console.log('直接导航到:', absolutePath);
    navigate(absolutePath);
  };

  // 处理菜单点击
  const handleMenuClick = (info: { key: string }) => {
    interceptNavigation(info.key);
  };

  // 处理语言切换
  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang);
    dispatch(setLanguage(lang));
  };

  // 处理注销
  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/projects')) return ['projects'];
    if (path.startsWith('/databases')) return ['databases'];
    if (path.startsWith('/settings')) return ['settings'];
    return [];
  };

  // 用户下拉菜单项
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: t('settings.account.profile')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: t('auth.logout'),
      onClick: handleLogout
    }
  ];

  // 语言下拉菜单项
  const languageMenuItems: MenuProps['items'] = [
    {
      key: 'zh',
      label: '中文',
      onClick: () => handleLanguageChange('zh')
    },
    {
      key: 'en',
      label: 'English',
      onClick: () => handleLanguageChange('en')
    },
    {
      key: 'ja',
      label: '日本語',
      onClick: () => handleLanguageChange('ja')
    }
  ];

  // 监听主题变化
  useEffect(() => {
    const handleThemeChange = (e: Event) => {
      // 更新菜单主题
      const customEvent = e as CustomEvent;
      const newTheme = customEvent.detail?.theme === 'dark' ? 'dark' : 'light';
      setMenuTheme(newTheme);

      // 强制重新渲染组件
      setCollapsed(prev => {
        setTimeout(() => setCollapsed(prev), 0);
        return prev;
      });
    };

    window.addEventListener('themechange', handleThemeChange);
    return () => {
      window.removeEventListener('themechange', handleThemeChange);
    };
  }, []);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{ background: 'var(--background-color)' }}
        className={document.documentElement.classList.contains('dark') ? 'dark-sider' : 'light-sider'}>
        <div style={{ height: '64px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {!collapsed && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {siteLogo ? (
                <img
                  src={siteLogo}
                  alt="Logo"
                  style={{ height: '32px', marginRight: '8px' }}
                  onError={(e) => {
                    console.error('Logo加载失败，尝试修复路径', e);
                    // 如果加载失败且路径是相对路径，尝试转换为绝对路径
                    const imgElement = e.target as HTMLImageElement;
                    if (!imgElement.src.startsWith('http') && !imgElement.src.startsWith('data:')) {
                      // 构建正确的API URL
                      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
                      const apiBaseUrl = baseUrl.replace('/api', '');
                      const url = imgElement.src.startsWith('/') ? imgElement.src : `/${imgElement.src}`;
                      imgElement.src = `${apiBaseUrl}${url}`;
                    }
                  }}
                />
              ) : null}
              <h1 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                color: 'var(--primary-color)',
                margin: 0
              }}>
                {siteName || t('app.title')}
              </h1>
            </div>
          )}
          {collapsed && (
            <div>
              {siteLogo ? (
                <img
                  src={siteLogo}
                  alt="Logo"
                  style={{ height: '32px' }}
                  onError={(e) => {
                    console.error('Logo加载失败，尝试修复路径', e);
                    // 如果加载失败且路径是相对路径，尝试转换为绝对路径
                    const imgElement = e.target as HTMLImageElement;
                    if (!imgElement.src.startsWith('http') && !imgElement.src.startsWith('data:')) {
                      // 构建正确的API URL
                      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
                      const apiBaseUrl = baseUrl.replace('/api', '');
                      const url = imgElement.src.startsWith('/') ? imgElement.src : `/${imgElement.src}`;
                      imgElement.src = `${apiBaseUrl}${url}`;
                    }
                  }}
                />
              ) : (
                <span style={{ fontSize: '1.5rem', color: 'var(--primary-color)' }}>PV</span>
              )}
            </div>
          )}
        </div>
        <Menu
          theme={menuTheme}
          mode="inline"
          selectedKeys={getSelectedKeys()}
          onClick={handleMenuClick}
          className={`main-menu ${menuTheme === 'dark' ? 'ant-menu-dark' : 'ant-menu-light'}`}
        >
          <Menu.Item key="projects" icon={<ProjectOutlined />}>
            {t('nav.projects')}
          </Menu.Item>
          <Menu.SubMenu key="databases" icon={<DatabaseOutlined />} title={t('nav.databases')}>
            <Menu.Item key="databases/irradiance">
              {t('nav.irradiance')}
            </Menu.Item>
            <Menu.Item key="databases/electricity-price">
              {t('nav.electricityPrice')}
            </Menu.Item>
            <Menu.Item key="databases/suppliers">
              {t('nav.suppliers')}
            </Menu.Item>
            <Menu.Item key="databases/equipment">
              {t('nav.equipment')}
            </Menu.Item>
          </Menu.SubMenu>
          <Menu.Item key="settings" icon={<SettingOutlined />}>
            {t('nav.settings')}
          </Menu.Item>
        </Menu>
      </Sider>
      <Layout>
        <Header style={{
          background: 'var(--background-color)',
          padding: 0,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid var(--border-color)'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggleCollapsed}
            style={{ width: '64px', height: '64px' }}
          />
          <div style={{ display: 'flex', alignItems: 'center', marginRight: '16px' }}>
            <Dropdown menu={{ items: languageMenuItems }} placement="bottomRight">
              <Button type="text" icon={<GlobalOutlined />} style={{ marginRight: '8px' }}>
                {!collapsed && t('app.language')}
              </Button>
            </Dropdown>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Button type="text" icon={<UserOutlined />} style={{ display: 'flex', alignItems: 'center' }}>
                {!collapsed && <span style={{ marginLeft: '4px' }}>{user?.username}</span>}
              </Button>
            </Dropdown>
          </div>
        </Header>
        <Content style={{
          margin: '16px',
          padding: '16px',
          background: 'var(--background-color)',
          borderRadius: '8px',
          overflow: 'auto',
          border: '1px solid var(--border-color)'
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
