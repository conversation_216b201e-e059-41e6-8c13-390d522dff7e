import React, { useEffect, useRef } from 'react';
import { Card, Empty } from 'antd';
import { useTranslation } from 'react-i18next';
import * as echarts from 'echarts/core';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
} from 'echarts/components';
import { Bar<PERSON>hart, LineChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { ElectricityPrice } from '../../types/database';

// 注册必要的组件
echarts.use([
  GridComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
]);

interface ElectricityPriceChartProps {
  data: ElectricityPrice;
  viewMode: 'daily' | 'monthly';
  seasonId?: string; // 季节ID，用于显示特定季节的电价
}

/**
 * 电价可视化图表组件
 */
const ElectricityPriceChart: React.FC<ElectricityPriceChartProps> = ({ data, viewMode, seasonId }) => {
  const { t } = useTranslation();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    // 如果已经存在图表实例，先销毁
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新的图表实例
    chartInstance.current = echarts.init(chartRef.current);

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  // 获取要显示的规则
  const getRulesToDisplay = () => {
    if (!data) return [];

    // 如果是固定电价政策，直接返回规则
    if (data.policyType === 'fixed' || !data.seasonalPolicies || data.seasonalPolicies.length === 0) {
      return data.rules || [];
    }

    // 如果是季节性电价政策，根据seasonId返回对应季节的规则
    if (seasonId) {
      const season = data.seasonalPolicies.find(p => p.id === seasonId);
      return season ? season.rules : [];
    }

    // 如果没有指定seasonId，返回第一个季节的规则
    return data.seasonalPolicies[0]?.rules || [];
  };

  // 更新图表数据
  useEffect(() => {
    if (!chartInstance.current || !data) return;

    try {
      // 获取要显示的规则
      const rulesToDisplay = getRulesToDisplay();
      if (rulesToDisplay.length === 0) return;

      // 准备数据
      const hours = Array.from({ length: 24 }, (_, i) => i);
      const priceData = new Array(24).fill(0);
      const typeData = new Array(24).fill('');

      // 处理每个规则
      rulesToDisplay.forEach(rule => {
        try {
          if (!rule.startTime || !rule.endTime) {
            console.warn('规则缺少开始或结束时间:', rule);
            return;
          }

          const [startHour, startMinute] = (rule.startTime || '0:0').split(':').map(Number);
          const [endHour, endMinute] = (rule.endTime || '0:0').split(':').map(Number);

          // 计算开始和结束的小时（包括小数部分）
          const startDecimalHour = startHour + startMinute / 60;
          let endDecimalHour = endHour + endMinute / 60;

          // 处理跨天的情况
          if (endDecimalHour <= startDecimalHour) {
            endDecimalHour += 24;
          }

          // 填充价格数据
          for (let i = 0; i < 24; i++) {
            // 检查当前小时是否在规则时间范围内
            if (i >= startDecimalHour && i < endDecimalHour) {
              priceData[i] = rule.price || 0;
              typeData[i] = rule.type || 'normal';
            } else if (i + 24 >= startDecimalHour && i + 24 < endDecimalHour) {
              // 处理跨天的情况
              priceData[i] = rule.price || 0;
              typeData[i] = rule.type || 'normal';
            }
          }
        } catch (ruleError) {
          console.error('处理规则时出错:', ruleError, rule);
        }
      });

    // 准备柱状图数据
      const barData = priceData.map((price, index) => {
        return {
          value: price,
          itemStyle: {
            color: getColorByType(typeData[index])
          }
        };
      });

      // 准备图表选项
      const option = {
        title: {
          text: t('electricityPrice.priceChart'),
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params: any) {
            try {
              if (!params || !params[0]) return '';

              const hour = params[0].axisValue;
              const price = params[0].data.value || params[0].data;
              const type = typeData[parseInt(hour)];
              const typeName = getTypeName(type);
              return `${hour}:00 - ${(parseInt(hour) + 1) % 24}:00<br/>${t('electricityPrice.price')}: ${price.toFixed(1)} ${t('common.currency')}<br/>${t('electricityPrice.type')}: ${typeName}`;
            } catch (tooltipError) {
              console.error('生成提示信息时出错:', tooltipError);
              return '';
            }
          }
        },
        toolbox: {
          feature: {
            saveAsImage: { title: t('common.saveAsImage') }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: hours.map(h => h.toString()),
          name: t('electricityPrice.hour'),
          nameLocation: 'middle',
          nameGap: 30,
          axisLabel: {
            interval: 2
          }
        },
        yAxis: {
          type: 'value',
          name: `${t('electricityPrice.price')} (${t('common.currency')})`,
          nameLocation: 'middle',
          nameGap: 50
        },
        series: [
          {
            name: t('electricityPrice.price'),
            type: 'bar',
            data: barData,
            barWidth: '80%',
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              fontSize: 10
            }
          }
        ]
      };

      // 设置图表选项
      chartInstance.current.setOption(option);
    } catch (error) {
      console.error('更新图表数据时出错:', error);
    }
  }, [data, viewMode, seasonId, t]);

  // 根据类型获取颜色
  const getColorByType = (type: string) => {
    switch (type) {
      case 'super-peak':
        return '#ff8800'; // 尖峰 - 橙色
      case 'peak':
        return '#ff4d4f'; // 峰时 - 红色
      case 'normal':
        return '#1677ff'; // 平时 - 蓝色
      case 'valley':
        return '#52c41a'; // 谷时 - 绿色
      default:
        return '#1677ff'; // 默认蓝色
    }
  };

  // 获取类型名称
  const getTypeName = (type: string) => {
    switch (type) {
      case 'super-peak':
        return t('electricityPrice.superPeak', '尖峰');
      case 'peak':
        return t('electricityPrice.peak');
      case 'normal':
        return t('electricityPrice.normal');
      case 'valley':
        return t('electricityPrice.valley');
      default:
        return '';
    }
  };

  // 如果没有数据，显示空状态
  if (!data) {
    return (
      <Card>
        <Empty
          description={t('electricityPrice.noData')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  // 如果是固定电价政策但没有规则
  if (data.policyType === 'fixed' && (!data.rules || data.rules.length === 0)) {
    return (
      <Card>
        <Empty
          description={t('electricityPrice.noRules')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  // 如果是季节性电价政策但没有季节
  if (data.policyType === 'seasonal' && (!data.seasonalPolicies || data.seasonalPolicies.length === 0)) {
    return (
      <Card>
        <Empty
          description={t('electricityPrice.noSeasons')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <div ref={chartRef} style={{ width: '100%', height: '400px' }} />
  );
};

export default ElectricityPriceChart;
