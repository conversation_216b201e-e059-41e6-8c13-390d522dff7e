import React, { useState } from 'react';
import { Card, Form, Switch, Button, Space, message, Divider, Typography, Tooltip, Select } from 'antd';
import { BellOutlined, MailOutlined, NotificationOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { NotificationSettings } from '../../types/settings';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 通知设置面板组件
 */
const NotificationSettingsPanel: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  // 模拟初始通知设置
  const initialSettings: NotificationSettings = {
    email: true,
    system: true,
    projectComplete: true,
    updates: false
  };

  // 处理表单提交
  const handleSubmit = (values: NotificationSettings) => {
    setIsLoading(true);

    // 模拟API调用
    setTimeout(() => {
      console.log('保存通知设置:', values);
      message.success(t('settings.notifications.saveSuccess'));
      setIsLoading(false);
    }, 500);
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    message.info(t('settings.notifications.resetSuccess'));
  };

  return (
    <Card
      title={
        <Space>
          <BellOutlined />
          {t('settings.notifications.title')}
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialSettings}
        onFinish={handleSubmit}
      >
        <Title level={5}>{t('settings.notifications.channelSettings')}</Title>
        
        {/* 电子邮件通知 */}
        <Form.Item
          name="email"
          label={
            <Space>
              <MailOutlined />
              {t('settings.notifications.email')}
              <Tooltip title={t('settings.notifications.emailTooltip')}>
                <QuestionCircleOutlined />
              </Tooltip>
            </Space>
          }
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        {/* 系统通知 */}
        <Form.Item
          name="system"
          label={
            <Space>
              <NotificationOutlined />
              {t('settings.notifications.system')}
              <Tooltip title={t('settings.notifications.systemTooltip')}>
                <QuestionCircleOutlined />
              </Tooltip>
            </Space>
          }
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Divider />
        <Title level={5}>{t('settings.notifications.eventSettings')}</Title>

        {/* 项目分析完成通知 */}
        <Form.Item
          name="projectComplete"
          label={t('settings.notifications.projectComplete')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        {/* 系统更新通知 */}
        <Form.Item
          name="updates"
          label={t('settings.notifications.updates')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Divider />
        <Title level={5}>{t('settings.notifications.frequencySettings')}</Title>

        {/* 通知频率 */}
        <Form.Item
          name="frequency"
          label={t('settings.notifications.frequency')}
          initialValue="immediate"
        >
          <Select>
            <Option value="immediate">{t('settings.notifications.immediate')}</Option>
            <Option value="daily">{t('settings.notifications.daily')}</Option>
            <Option value="weekly">{t('settings.notifications.weekly')}</Option>
          </Select>
        </Form.Item>

        {/* 表单按钮 */}
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={isLoading}>
              {t('common.save')}
            </Button>
            <Button onClick={handleReset}>
              {t('common.reset')}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default NotificationSettingsPanel;
