import React, { useState, useEffect } from 'react';
import { Card, Tree, Button, Space, Input, Modal, Form, message, Typography, Tooltip, Divider } from 'antd';
import { FolderOutlined, FileOutlined, PlusOutlined, EditOutlined, DeleteOutlined, FolderAddOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ConfirmDialog } from '../common';

const { Title, Text } = Typography;
const { DirectoryTree } = Tree;
const { Search } = Input;

// 目录节点类型
interface DirectoryNode {
  key: string;
  title: string;
  isLeaf?: boolean;
  children?: DirectoryNode[];
}

/**
 * 目录管理面板组件
 */
const DirectoryManagementPanel: React.FC = () => {
  const { t } = useTranslation();
  const [treeData, setTreeData] = useState<DirectoryNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [form] = Form.useForm();
  const [selectedNode, setSelectedNode] = useState<DirectoryNode | null>(null);
  const [isAddingFolder, setIsAddingFolder] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [nodeToDelete, setNodeToDelete] = useState<DirectoryNode | null>(null);

  // 模拟获取目录数据
  useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      const mockData: DirectoryNode[] = [
        {
          key: '1',
          title: '数据目录',
          children: [
            {
              key: '1-1',
              title: '光照数据',
              children: [
                { key: '1-1-1', title: '东京', isLeaf: true },
                { key: '1-1-2', title: '大阪', isLeaf: true },
                { key: '1-1-3', title: '名古屋', isLeaf: true },
              ],
            },
            {
              key: '1-2',
              title: '电价政策',
              children: [
                { key: '1-2-1', title: '东京电力', isLeaf: true },
                { key: '1-2-2', title: '关西电力', isLeaf: true },
              ],
            },
            {
              key: '1-3',
              title: '设备数据',
              children: [
                { key: '1-3-1', title: '光伏组件', isLeaf: true },
                { key: '1-3-2', title: '储能设备', isLeaf: true },
                { key: '1-3-3', title: '逆变器', isLeaf: true },
              ],
            },
          ],
        },
        {
          key: '2',
          title: '项目目录',
          children: [
            { key: '2-1', title: '已完成项目', isLeaf: true },
            { key: '2-2', title: '进行中项目', isLeaf: true },
            { key: '2-3', title: '草稿项目', isLeaf: true },
          ],
        },
        {
          key: '3',
          title: '备份目录',
          children: [
            { key: '3-1', title: '自动备份', isLeaf: true },
            { key: '3-2', title: '手动备份', isLeaf: true },
          ],
        },
      ];
      setTreeData(mockData);
      setExpandedKeys(['1', '2', '3']);
    }, 500);
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    if (value) {
      // 查找匹配的节点并展开其父节点
      const expandedKeys = findExpandedKeys(treeData, value);
      setExpandedKeys(expandedKeys);
      setAutoExpandParent(true);
    } else {
      setAutoExpandParent(false);
    }
  };

  // 递归查找匹配的节点并返回其父节点的key
  const findExpandedKeys = (data: DirectoryNode[], value: string): string[] => {
    const result: string[] = [];
    
    const traverse = (nodes: DirectoryNode[], parentKey?: string) => {
      nodes.forEach(node => {
        if (node.title.toLowerCase().includes(value.toLowerCase())) {
          if (parentKey) {
            result.push(parentKey);
          }
          result.push(node.key);
        }
        if (node.children) {
          traverse(node.children, node.key);
        }
      });
    };
    
    traverse(data);
    return [...new Set(result)]; // 去重
  };

  // 处理展开/折叠
  const handleExpand = (expandedKeys: string[]) => {
    setExpandedKeys(expandedKeys);
    setAutoExpandParent(false);
  };

  // 处理添加文件夹
  const handleAddFolder = (parentNode?: DirectoryNode) => {
    setModalTitle(t('settings.directory.addFolder'));
    setSelectedNode(parentNode || null);
    setIsAddingFolder(true);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑节点
  const handleEditNode = (node: DirectoryNode) => {
    setModalTitle(t('settings.directory.editNode'));
    setSelectedNode(node);
    setIsAddingFolder(false);
    form.setFieldsValue({ name: node.title });
    setModalVisible(true);
  };

  // 处理删除节点
  const handleDeleteNode = (node: DirectoryNode) => {
    setNodeToDelete(node);
    setDeleteModalVisible(true);
  };

  // 确认删除节点
  const confirmDeleteNode = () => {
    if (nodeToDelete) {
      // 模拟删除操作
      const newTreeData = deleteNode(treeData, nodeToDelete.key);
      setTreeData(newTreeData);
      message.success(t('settings.directory.deleteSuccess'));
      setDeleteModalVisible(false);
    }
  };

  // 递归删除节点
  const deleteNode = (data: DirectoryNode[], key: string): DirectoryNode[] => {
    return data.filter(node => {
      if (node.key === key) {
        return false;
      }
      if (node.children) {
        node.children = deleteNode(node.children, key);
      }
      return true;
    });
  };

  // 处理表单提交
  const handleSubmit = (values: { name: string }) => {
    if (isAddingFolder) {
      // 添加新文件夹
      const newNode: DirectoryNode = {
        key: `new-${Date.now()}`,
        title: values.name,
        children: [],
      };
      
      if (selectedNode) {
        // 添加到选中节点的子节点
        const newTreeData = addChildNode(treeData, selectedNode.key, newNode);
        setTreeData(newTreeData);
        // 展开父节点
        if (!expandedKeys.includes(selectedNode.key)) {
          setExpandedKeys([...expandedKeys, selectedNode.key]);
        }
      } else {
        // 添加到根节点
        setTreeData([...treeData, newNode]);
      }
      
      message.success(t('settings.directory.addSuccess'));
    } else {
      // 编辑节点
      if (selectedNode) {
        const newTreeData = updateNodeTitle(treeData, selectedNode.key, values.name);
        setTreeData(newTreeData);
        message.success(t('settings.directory.updateSuccess'));
      }
    }
    
    setModalVisible(false);
  };

  // 递归添加子节点
  const addChildNode = (data: DirectoryNode[], parentKey: string, newNode: DirectoryNode): DirectoryNode[] => {
    return data.map(node => {
      if (node.key === parentKey) {
        return {
          ...node,
          children: node.children ? [...node.children, newNode] : [newNode],
        };
      }
      if (node.children) {
        return {
          ...node,
          children: addChildNode(node.children, parentKey, newNode),
        };
      }
      return node;
    });
  };

  // 递归更新节点标题
  const updateNodeTitle = (data: DirectoryNode[], key: string, newTitle: string): DirectoryNode[] => {
    return data.map(node => {
      if (node.key === key) {
        return {
          ...node,
          title: newTitle,
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateNodeTitle(node.children, key, newTitle),
        };
      }
      return node;
    });
  };

  // 渲染树节点标题，高亮搜索结果
  const renderTitle = (node: DirectoryNode) => {
    const title = node.title;
    const index = title.toLowerCase().indexOf(searchValue.toLowerCase());
    
    if (index === -1 || !searchValue) {
      return (
        <Space>
          {node.isLeaf ? <FileOutlined /> : <FolderOutlined />}
          <span>{title}</span>
          <Space size="small">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleEditNode(node);
              }}
            />
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteNode(node);
              }}
            />
            {!node.isLeaf && (
              <Button
                type="text"
                size="small"
                icon={<FolderAddOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddFolder(node);
                }}
              />
            )}
          </Space>
        </Space>
      );
    }
    
    const beforeStr = title.substring(0, index);
    const matchStr = title.substring(index, index + searchValue.length);
    const afterStr = title.substring(index + searchValue.length);
    
    return (
      <Space>
        {node.isLeaf ? <FileOutlined /> : <FolderOutlined />}
        <span>
          {beforeStr}
          <span style={{ color: '#f50' }}>{matchStr}</span>
          {afterStr}
        </span>
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleEditNode(node);
            }}
          />
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteNode(node);
            }}
          />
          {!node.isLeaf && (
            <Button
              type="text"
              size="small"
              icon={<FolderAddOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleAddFolder(node);
              }}
            />
          )}
        </Space>
      </Space>
    );
  };

  // 递归处理树节点，添加自定义标题
  const processTreeData = (data: DirectoryNode[]): DirectoryNode[] => {
    return data.map(node => {
      const newNode = {
        ...node,
        title: renderTitle(node),
      };
      
      if (node.children) {
        newNode.children = processTreeData(node.children);
      }
      
      return newNode;
    });
  };

  return (
    <Card
      title={
        <Space>
          <FolderOutlined />
          {t('settings.directory.title')}
        </Space>
      }
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => handleAddFolder()}
        >
          {t('settings.directory.addRootFolder')}
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Search
          placeholder={t('settings.directory.searchPlaceholder')}
          onChange={(e) => handleSearch(e.target.value)}
          style={{ marginBottom: 8 }}
        />
        
        <DirectoryTree
          treeData={processTreeData(treeData)}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onExpand={handleExpand}
          defaultExpandAll
        />
      </Space>

      {/* 添加/编辑节点模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label={t('settings.directory.name')}
            rules={[{ required: true, message: t('common.required') }]}
          >
            <Input />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common.save')}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除节点确认对话框 */}
      <ConfirmDialog
        title={t('settings.directory.deleteNodeTitle')}
        content={t('settings.directory.deleteNodeConfirm')}
        visible={deleteModalVisible}
        onConfirm={confirmDeleteNode}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </Card>
  );
};

export default DirectoryManagementPanel;
