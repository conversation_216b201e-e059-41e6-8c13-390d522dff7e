import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Divider, Modal, message, Progress, Table, Typography, Tag, Tooltip } from 'antd';
import { DeleteOutlined, QuestionCircleOutlined, ReloadOutlined, SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { cacheManager } from '../../utils/dataSynchronization';
import { ConfirmDialog } from '../../components/common';
const { Title, Text } = Typography;

// 缓存项类型定义
interface CacheItem {
  key: string;
  id: string;
  name: string;
  size: number;
  lastAccessed: number;
  chunked: boolean;
  compressed: boolean;
  chunkCount: number;
  type: string;
}

/**
 * 缓存管理面板组件
 * 用于在设置页面中提供缓存管理功能
 */
const CacheManagementPanel: React.FC = () => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [clearAllModalVisible, setClearAllModalVisible] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string>('');
  const [cacheStats, setCacheStats] = useState<{
    itemCount: number;
    usedSpace: number;
    maxSpace: number;
    usagePercentage: number;
    items: Array<{
      id: string;
      name: string;
      size: number;
      timestamp: number;
      chunked: boolean;
      compressed: boolean;
      chunkCount: number;
    }>;
  }>({
    itemCount: 0,
    usedSpace: 0,
    maxSpace: 0,
    usagePercentage: 0,
    items: [],
  });

  // 获取缓存统计信息
  useEffect(() => {
    try {
      const stats = cacheManager.getCacheStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('获取缓存统计信息失败:', error);
      message.error(t('settings.cache.fetchStatsFailed'));
    }
  }, [refreshTrigger, t]);

  // 刷新缓存统计信息
  const refreshCacheStats = () => {
    setRefreshTrigger(prev => prev + 1);
    message.success(t('settings.cache.statsRefreshed'));
  };

  // 处理清除所有缓存
  const handleClearAllCache = () => {
    setClearAllModalVisible(true);
  };

  // 确认清除所有缓存
  const confirmClearAllCache = async () => {
    try {
      setIsLoading(true);
      console.log('开始清除所有缓存');

      // 清除所有缓存
      cacheManager.clearCache();
      console.log('所有缓存已清除');

      // 延迟一下再刷新，确保清除操作完成
      setTimeout(() => {
        console.log('刷新缓存统计信息');
        refreshCacheStats();
        message.success(t('settings.cache.allCacheCleared'));
        setIsLoading(false);
        setClearAllModalVisible(false);
      }, 500);
    } catch (error) {
      console.error('清除所有缓存失败:', error);
      message.error(t('common.error'));
      setIsLoading(false);
      setClearAllModalVisible(false);
    }
  };

  // 取消清除所有缓存
  const cancelClearAllCache = () => {
    setClearAllModalVisible(false);
  };

  // 处理删除单个缓存项
  const handleDeleteCacheItem = (id: string) => {
    setDeleteItemId(id);
    setDeleteModalVisible(true);
  };

  // 确认删除缓存项
  const confirmDeleteCacheItem = async () => {
    try {
      setIsLoading(true);
      console.log('开始删除缓存项:', deleteItemId);

      // 删除缓存项
      cacheManager.removeItem(deleteItemId);
      console.log('缓存项已删除');

      // 延迟一下再刷新，确保删除操作完成
      setTimeout(() => {
        console.log('刷新缓存统计信息');
        refreshCacheStats();
        message.success(t('settings.cache.cacheItemDeleted'));
        setIsLoading(false);
        setDeleteModalVisible(false);
      }, 500);
    } catch (error) {
      console.error('删除缓存项失败:', error);
      message.error(t('common.error'));
      setIsLoading(false);
      setDeleteModalVisible(false);
    }
  };

  // 取消删除缓存项
  const cancelDeleteCacheItem = () => {
    setDeleteModalVisible(false);
  };

  // 表格列定义
  const columns = [
    {
      title: t('settings.cache.dataName'),
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text ellipsis>{text}</Text>,
    },
    {
      title: '数据类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        switch (type) {
          case '项目数据':
            return <Tag color="cyan">{type}</Tag>;
          case '光照数据':
            return <Tag color="green">{type}</Tag>;
          case '电价政策':
            return <Tag color="orange">{type}</Tag>;
          case '设备数据':
            return <Tag color="blue">{type}</Tag>;
          case '供应商数据':
            return <Tag color="purple">{type}</Tag>;
          default:
            return <Tag color="default">{type}</Tag>;
        }
      },
      filters: [
        { text: '项目数据', value: '项目数据' },
        { text: '光照数据', value: '光照数据' },
        { text: '电价政策', value: '电价政策' },
        { text: '设备数据', value: '设备数据' },
        { text: '供应商数据', value: '供应商数据' },
      ],
      onFilter: (value: string, record: CacheItem) => record.type === value,
    },
    {
      title: t('settings.cache.dataSize'),
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => {
        const sizeInKB = size / 1024;
        const sizeInMB = sizeInKB / 1024;
        return sizeInMB >= 1
          ? `${sizeInMB.toFixed(2)} MB`
          : `${sizeInKB.toFixed(2)} KB`;
      },
      sorter: (a: CacheItem, b: CacheItem) => a.size - b.size,
    },
    {
      title: t('settings.cache.storageType'),
      dataIndex: 'storageType',
      key: 'storageType',
      render: (_: any, record: CacheItem) => {
        if (record.compressed) {
          return (
            <Tooltip title="使用二进制压缩存储">
              <Tag color="purple">压缩存储</Tag>
            </Tooltip>
          );
        } else if (record.chunked) {
          return (
            <Tooltip title={`${record.chunkCount} 个数据块`}>
              <Tag color="blue">分块存储</Tag>
            </Tooltip>
          );
        } else {
          return (
            <Tooltip title="单一JSON存储">
              <Tag color="green">标准存储</Tag>
            </Tooltip>
          );
        }
      },
    },
    {
      title: t('settings.cache.lastAccessed'),
      dataIndex: 'lastAccessed',
      key: 'lastAccessed',
      render: (timestamp: number) => new Date(timestamp).toLocaleString(),
      sorter: (a: CacheItem, b: CacheItem) => a.lastAccessed - b.lastAccessed,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: CacheItem) => (
        <Button
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteCacheItem(record.id)}
          size="small"
        >
          {t('common.delete')}
        </Button>
      ),
    },
  ];

  // 准备表格数据
  const tableData = cacheStats.items.map(item => ({
    key: item.id,
    id: item.id,
    name: item.name,
    size: item.size,
    lastAccessed: item.timestamp,
    chunked: item.chunked,
    compressed: item.compressed,
    chunkCount: item.chunkCount,
    type: item.type || '光照数据', // 确保有类型字段
  }));

  return (
    <Card
      title={
        <Space>
          <SaveOutlined />
          {t('settings.cache.title')}
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshCacheStats}
            loading={isLoading}
          >
            {t('settings.cache.refresh')}
          </Button>
          <Button
            type="link"
            icon={<QuestionCircleOutlined />}
            onClick={() => Modal.info({
              title: t('settings.cache.helpTitle'),
              content: (
                <div>
                  <p>{t('settings.cache.helpContent')}</p>
                </div>
              ),
            })}
          />
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Title level={5}>{t('settings.cache.usageStatistics')}</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>{t('settings.cache.usedSpace')}: {(cacheStats.usedSpace / 1024 / 1024).toFixed(1)} MB</Text>
              <Text>{t('settings.cache.totalSpace')}: {(cacheStats.maxSpace / 1024 / 1024).toFixed(1)} MB</Text>
            </div>
            <Progress
              percent={parseFloat(cacheStats.usagePercentage.toFixed(1))}
              status={cacheStats.usagePercentage > 90 ? "exception" : "normal"}
              strokeColor={
                cacheStats.usagePercentage > 80 ? "#faad14" :
                cacheStats.usagePercentage > 60 ? "#1890ff" : "#52c41a"
              }
            />
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>{t('settings.cache.itemCount')}: {cacheStats.itemCount}</Text>
              <Text>
                {cacheStats.usagePercentage > 80 ? (
                  <Tag color="warning">{t('settings.cache.almostFull')}</Tag>
                ) : cacheStats.usagePercentage > 60 ? (
                  <Tag color="processing">{t('settings.cache.moderate')}</Tag>
                ) : (
                  <Tag color="success">{t('settings.cache.good')}</Tag>
                )}
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
              <Text>
                压缩存储: {cacheStats.items.filter(item => item.compressed).length} 项
              </Text>
              <Text>
                分块存储: {cacheStats.items.filter(item => item.chunked).length} 项
              </Text>
              <Text>
                标准存储: {cacheStats.items.filter(item => !item.chunked && !item.compressed).length} 项
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
              <Text>
                <Tag color="green">光照数据</Tag>: {cacheStats.items.filter(item => item.type === '光照数据').length} 项
              </Text>
              <Text>
                <Tag color="cyan">项目数据</Tag>: {cacheStats.items.filter(item => item.type === '项目数据').length} 项
              </Text>
            </div>
          </Space>
        </div>

        <Divider />

        <div>
          <Title level={5}>{t('settings.cache.cachedItems')}</Title>
          <Table
            columns={columns}
            dataSource={tableData}
            size="small"
            pagination={{ pageSize: 10 }}
            loading={isLoading}
          />
        </div>

        <Divider />

        <Button
          danger
          icon={<DeleteOutlined />}
          onClick={handleClearAllCache}
          loading={isLoading}
          block
        >
          {t('settings.cache.clearAllCache')}
        </Button>
      </Space>

      {/* 删除缓存项确认对话框 */}
      <ConfirmDialog
        title={t('settings.cache.deleteCacheItemTitle')}
        content={t('settings.cache.deleteCacheItemConfirm')}
        visible={deleteModalVisible}
        onConfirm={confirmDeleteCacheItem}
        onCancel={cancelDeleteCacheItem}
      />

      {/* 清除所有缓存确认对话框 */}
      <ConfirmDialog
        title={t('settings.cache.clearAllCacheTitle')}
        content={t('settings.cache.clearAllCacheConfirm')}
        visible={clearAllModalVisible}
        onConfirm={confirmClearAllCache}
        onCancel={cancelClearAllCache}
      />
    </Card>
  );
};

export default CacheManagementPanel;
