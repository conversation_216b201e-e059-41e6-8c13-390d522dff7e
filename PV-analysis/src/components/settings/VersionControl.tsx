import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Progress,
  Alert,
  Modal,
  List,
  Typography,
  Spin,
  Divider,
  Row,
  Col,
  message
} from 'antd';
import {
  CloudDownloadOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  getVersionInfo,
  checkForUpdates,
  performUpdate,
  getUpdateStatus,
  restartApplication,
  type VersionInfo,
  type UpdateStatus
} from '../../services/versionService';

const { Title, Text, Paragraph } = Typography;

/**
 * 版本控制组件
 */
const VersionControl: React.FC = () => {
  const { t } = useTranslation();
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [showChangelog, setShowChangelog] = useState(false);

  // 加载版本信息
  const loadVersionInfo = async () => {
    try {
      setLoading(true);
      const info = await getVersionInfo();
      setVersionInfo(info);
    } catch (error) {
      console.error('加载版本信息失败:', error);
      message.error('加载版本信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 检查更新
  const handleCheckUpdate = async () => {
    try {
      setChecking(true);
      const info = await checkForUpdates();
      setVersionInfo(info);

      if (info.hasUpdate) {
        message.success(t('settings.version.newVersionAvailable'));
      } else {
        message.info(t('settings.version.alreadyLatest'));
      }
    } catch (error) {
      console.error('检查更新失败:', error);
      message.error('检查更新失败');
    } finally {
      setChecking(false);
    }
  };

  // 执行更新
  const handleUpdate = async () => {
    console.log('handleUpdate called');
    Modal.confirm({
      title: t('settings.version.updateConfirm'),
      content: t('settings.version.updateConfirmMessage'),
      icon: <ExclamationCircleOutlined />,
      onOk: async () => {
        try {
          console.log('Starting update...');
          setUpdating(true);
          const status = await performUpdate();
          console.log('Update status received:', status);
          setUpdateStatus(status);

          // 开始轮询更新状态
          pollUpdateStatus();
        } catch (error) {
          console.error('更新失败:', error);
          message.error(`更新失败: ${error instanceof Error ? error.message : String(error)}`);
          setUpdating(false);
        }
      }
    });
  };

  // 轮询更新状态
  const pollUpdateStatus = () => {
    const interval = setInterval(async () => {
      try {
        const status = await getUpdateStatus();
        setUpdateStatus(status);

        if (status.status === 'success') {
          clearInterval(interval);
          setUpdating(false);
          Modal.success({
            title: t('settings.version.updateCompleted'),
            content: t('settings.version.updateCompletedMessage'),
            onOk: () => {
              restartApplication();
              setTimeout(() => {
                window.location.reload();
              }, 2000);
            }
          });
        } else if (status.status === 'error') {
          clearInterval(interval);
          setUpdating(false);
          Modal.error({
            title: t('settings.version.updateFailed'),
            content: status.error || t('settings.version.updateFailedMessage')
          });
        }
      } catch (error) {
        console.error('获取更新状态失败:', error);
        clearInterval(interval);
        setUpdating(false);
      }
    }, 2000);
  };

  // 组件挂载时加载版本信息
  useEffect(() => {
    loadVersionInfo();
  }, []);

  // 渲染更新状态
  const renderUpdateStatus = () => {
    if (!updateStatus) return null;

    const { status, progress, message: statusMessage } = updateStatus;

    let statusColor = 'blue';
    let statusIcon = <SyncOutlined spin />;

    switch (status) {
      case 'success':
        statusColor = 'green';
        statusIcon = <CheckCircleOutlined />;
        break;
      case 'error':
        statusColor = 'red';
        statusIcon = <ExclamationCircleOutlined />;
        break;
    }

    return (
      <Alert
        type={status === 'error' ? 'error' : 'info'}
        showIcon
        icon={statusIcon}
        message={
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text>{statusMessage}</Text>
            {progress > 0 && (
              <Progress
                percent={progress}
                status={status === 'error' ? 'exception' : 'active'}
              />
            )}
          </Space>
        }
        style={{ marginBottom: 16 }}
      />
    );
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">{t('settings.version.loadingVersion')}</Text>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <CloudDownloadOutlined />
          {t('settings.version.title')}
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {renderUpdateStatus()}

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Card size="small" title={t('settings.version.currentVersion')}>
              <Space direction="vertical">
                <Tag color="blue" style={{ fontSize: '14px', padding: '4px 8px' }}>
                  v{versionInfo?.current || '1.0.0'}
                </Tag>
                <Text type="secondary">
                  {t('settings.version.releaseDate')}: {versionInfo?.releaseDate || '2023-12-01'}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col xs={24} sm={12}>
            <Card size="small" title={t('settings.version.latestVersion')}>
              <Space direction="vertical">
                <Tag
                  color={versionInfo?.hasUpdate ? 'orange' : 'green'}
                  style={{ fontSize: '14px', padding: '4px 8px' }}
                >
                  v{versionInfo?.latest || '1.0.0'}
                </Tag>
                {versionInfo?.hasUpdate ? (
                  <Text type="warning">{t('settings.version.hasUpdate')}</Text>
                ) : (
                  <Text type="success">{t('settings.version.upToDate')}</Text>
                )}
              </Space>
            </Card>
          </Col>
        </Row>

        <Divider />

        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleCheckUpdate}
            loading={checking}
          >
            {t('settings.version.checkUpdate')}
          </Button>

          {versionInfo?.hasUpdate && (
            <Button
              type="primary"
              icon={<CloudDownloadOutlined />}
              onClick={handleUpdate}
              loading={updating}
            >
              {t('settings.version.updateNow')}
            </Button>
          )}

          {versionInfo?.changelog && versionInfo.changelog.length > 0 && (
            <Button
              icon={<InfoCircleOutlined />}
              onClick={() => setShowChangelog(true)}
            >
              {t('settings.version.viewChangelog')}
            </Button>
          )}
        </Space>

        {/* 更新日志模态框 */}
        <Modal
          title={t('settings.version.changelog')}
          open={showChangelog}
          onCancel={() => setShowChangelog(false)}
          footer={[
            <Button key="close" onClick={() => setShowChangelog(false)}>
              {t('settings.version.close')}
            </Button>
          ]}
          width={600}
        >
          <List
            dataSource={versionInfo?.changelog || []}
            renderItem={(item) => (
              <List.Item>
                <Text>• {item}</Text>
              </List.Item>
            )}
          />
        </Modal>
      </Space>
    </Card>
  );
};

export default VersionControl;
