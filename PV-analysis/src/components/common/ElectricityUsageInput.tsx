import React, { useState, useEffect } from 'react';
import { Radio, Card, Tabs, Button, Table, InputNumber, Form, Select, Row, Col, Alert, Space, Typography } from 'antd';
import { UploadOutlined, DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import FileUpload from './FileUpload';
import UsagePatternForm, { UsagePattern } from './UsagePatternForm';
import ElectricityUsageChart from './ElectricityUsageChart';

const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { Option } = Select;

// 用电数据类型
export type ElectricityUsageType = 'sameEveryday' | 'sameEveryWeek' | 'monthlyDifferent' | 'dailyDifferent' | 'patternByDay' | 'patternByMonth';

// 用电数据接口
export interface ElectricityUsageData {
  type: ElectricityUsageType;
  data: any[];
}

interface ElectricityUsageInputProps {
  value?: ElectricityUsageData;
  onChange?: (data: ElectricityUsageData) => void;
  onValidate?: (valid: boolean) => void;
}

const ElectricityUsageInput: React.FC<ElectricityUsageInputProps> = ({
  value,
  onChange,
  onValidate
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 用电数据类型
  const [usageType, setUsageType] = useState<ElectricityUsageType>(value?.type || 'sameEveryday');

  // 用电数据
  const [usageData, setUsageData] = useState<any[]>(value?.data || []);

  // 当前活动的标签页
  const [activeTab, setActiveTab] = useState('manual');

  // 上传进度
  const [uploadProgress, setUploadProgress] = useState(0);

  // 是否正在上传
  const [isUploading, setIsUploading] = useState(false);

  // 紧凑表格显示状态
  const [showCompactTable, setShowCompactTable] = useState(false);

  // 每周视图选中的日期
  const [selectedDay, setSelectedDay] = useState<string>('0');

  // 月份视图选中的月份
  const [selectedMonth, setSelectedMonth] = useState<string>('1');

  // 初始化数据
  useEffect(() => {
    if (value) {
      setUsageType(value.type || 'sameEveryday');
      setUsageData(value.data || []);
    }
  }, [value]);

  // 验证数据
  useEffect(() => {
    const isValid = usageData.length > 0;
    if (onValidate) {
      onValidate(isValid);
    }
  }, [usageData, onValidate]);

  // 初始化选中的日期和月份
  useEffect(() => {
    // 初始化选中的日期
    if (usageType === 'sameEveryWeek' && usageData.length > 0) {
      const groupedData = usageData.reduce((acc: any, item: any) => {
        if (!acc[item.day]) {
          acc[item.day] = [];
        }
        acc[item.day].push(item);
        return acc;
      }, {});

      if (Object.keys(groupedData).length > 0) {
        setSelectedDay(Object.keys(groupedData)[0]);
      }
    }

    // 初始化选中的月份
    if (usageType === 'monthlyDifferent' && usageData.length > 0) {
      const groupedData = usageData.reduce((acc: any, item: any) => {
        if (!acc[item.month]) {
          acc[item.month] = [];
        }
        acc[item.month].push(item);
        return acc;
      }, {});

      if (Object.keys(groupedData).length > 0) {
        setSelectedMonth(Object.keys(groupedData)[0]);
      }
    }
  }, [usageType, usageData]);

  // 处理类型变更
  const handleTypeChange = (e: any) => {
    const newType = e.target.value;
    setUsageType(newType);

    // 清空数据
    setUsageData([]);

    // 更新数据
    const newData: ElectricityUsageData = {
      type: newType,
      data: [],
    };

    if (onChange) {
      onChange(newData);
    }
  };

  // 处理数据变更
  const handleDataChange = (newData: any[]) => {
    console.log("handleDataChange called with data:", newData);

    // 确保数据有效
    if (!newData || !Array.isArray(newData)) {
      console.error("Invalid data passed to handleDataChange:", newData);
      return;
    }

    // 更新本地状态
    setUsageData(newData);

    // 更新数据
    const updatedData: ElectricityUsageData = {
      type: usageType,
      data: newData,
    };

    // 通知父组件数据变更
    if (onChange) {
      console.log("Notifying parent component of data change:", updatedData);
      onChange(updatedData);
    }
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // 模拟上传进度
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 99) {
            clearInterval(interval);
            return 99;
          }
          return prev + 10;
        });
      }, 200);

      // 读取文件内容
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const lines = content.split('\n');

          // 解析CSV数据
          const parsedData: any[] = [];

          // 根据不同的数据类型解析
          if (usageType === 'sameEveryday') {
            // 解析24小时数据
            const headerLine = lines[0].split(',');
            const dataLine = lines[1].split(',');

            for (let i = 0; i < 24; i++) {
              parsedData.push({
                hour: i,
                value: parseFloat(dataLine[i]) || 0
              });
            }
          } else if (usageType === 'sameEveryWeek') {
            // 解析7天24小时数据
            for (let day = 0; day < 7; day++) {
              const dataLine = lines[day + 1].split(',');
              for (let hour = 0; hour < 24; hour++) {
                parsedData.push({
                  day,
                  hour,
                  value: parseFloat(dataLine[hour]) || 0
                });
              }
            }
          } else if (usageType === 'monthlyDifferent') {
            // 解析12个月24小时数据
            for (let month = 0; month < 12; month++) {
              const dataLine = lines[month + 1].split(',');
              for (let hour = 0; hour < 24; hour++) {
                parsedData.push({
                  month: month + 1,
                  hour,
                  value: parseFloat(dataLine[hour]) || 0
                });
              }
            }
          } else if (usageType === 'dailyDifferent') {
            // 解析365天24小时数据
            for (let day = 0; day < 365; day++) {
              const dataLine = lines[day + 1].split(',');
              for (let hour = 0; hour < 24; hour++) {
                parsedData.push({
                  day: day + 1,
                  hour,
                  value: parseFloat(dataLine[hour]) || 0
                });
              }
            }
          }

          // 更新数据
          handleDataChange(parsedData);

          // 完成上传
          clearInterval(interval);
          setUploadProgress(100);
          setTimeout(() => {
            setIsUploading(false);
          }, 500);
        } catch (error) {
          console.error('解析CSV文件失败:', error);
          setIsUploading(false);
        }
      };

      reader.readAsText(file);
    } catch (error) {
      console.error('上传文件失败:', error);
      setIsUploading(false);
    }
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    let templateContent = '';
    let fileName = '';

    if (usageType === 'sameEveryday') {
      // 24小时模板
      templateContent = 'Hour,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n';
      templateContent += 'Value,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0';
      fileName = 'electricity_usage_daily_template.csv';
    } else if (usageType === 'sameEveryWeek') {
      // 7天24小时模板
      templateContent = 'Hour,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n';
      for (let day = 0; day < 7; day++) {
        templateContent += `Day${day},0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0\n`;
      }
      fileName = 'electricity_usage_weekly_template.csv';
    } else if (usageType === 'monthlyDifferent') {
      // 12个月24小时模板
      templateContent = 'Hour,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n';
      for (let month = 1; month <= 12; month++) {
        templateContent += `Month${month},0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0\n`;
      }
      fileName = 'electricity_usage_monthly_template.csv';
    } else if (usageType === 'dailyDifferent') {
      // 365天24小时模板
      templateContent = 'Hour,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23\n';
      for (let day = 1; day <= 365; day++) {
        templateContent += `Day${day},0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0\n`;
      }
      fileName = 'electricity_usage_yearly_template.csv';
    }

    // 创建下载链接
    const blob = new Blob([templateContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 渲染不同类型的数据输入界面
  const renderInputForm = () => {
    switch (usageType) {
      case 'sameEveryday':
        return renderSameEverydayForm();
      case 'patternByDay':
        return renderPatternByDayForm();
      case 'patternByMonth':
        return renderPatternByMonthForm();
      case 'dailyDifferent':
        return renderDailyDifferentForm();
      default:
        return null;
    }
  };

  // 处理模式数据变更
  const handlePatternDataChange = (newPatterns: any[]) => {
    handleDataChange(newPatterns);
  };

  // 渲染按星期几的用电模式界面
  const renderPatternByDayForm = () => {
    // 获取当前数据
    const patterns = usageData.length > 0 ? usageData : [];

    // 检查数据是否完备
    const isDataComplete = patterns.length > 0 && patterns.some(pattern =>
      pattern.hourlyData && pattern.hourlyData.length === 24 &&
      pattern.hourlyData.every(item => item.value !== undefined && item.value !== null)
    );

    // 获取第一个模式的24小时数据用于图表显示
    const hourlyData = isDataComplete && patterns.length > 0 ?
      patterns[0].hourlyData.map(item => ({ hour: item.hour, value: item.value })) :
      [];

    return (
      <div>
        <Alert
          message={t('electricityUsage.patternByDay')}
          description={t('electricityUsage.patternByDayDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <UsagePatternForm
          value={patterns}
          onChange={handlePatternDataChange}
          patternType="weekday"
        />

        {isDataComplete && (
          <ElectricityUsageChart
            data={hourlyData}
            type="hourly"
            style={{ marginTop: 24 }}
          />
        )}
      </div>
    );
  };

  // 渲染按月份的用电模式界面
  const renderPatternByMonthForm = () => {
    // 获取当前数据
    const patterns = usageData.length > 0 ? usageData : [];

    // 检查数据是否完备
    const isDataComplete = patterns.length > 0 && patterns.some(pattern =>
      pattern.hourlyData && pattern.hourlyData.length === 24 &&
      pattern.hourlyData.every(item => item.value !== undefined && item.value !== null)
    );

    // 获取第一个模式的24小时数据用于图表显示
    const hourlyData = isDataComplete && patterns.length > 0 ?
      patterns[0].hourlyData.map(item => ({ hour: item.hour, value: item.value })) :
      [];

    return (
      <div>
        <Alert
          message={t('electricityUsage.patternByMonth')}
          description={t('electricityUsage.patternByMonthDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <UsagePatternForm
          value={patterns}
          onChange={handlePatternDataChange}
          patternType="month"
        />

        {isDataComplete && (
          <ElectricityUsageChart
            data={hourlyData}
            type="hourly"
            style={{ marginTop: 24 }}
          />
        )}
      </div>
    );
  };

  // 渲染每天相同的数据输入界面
  const renderSameEverydayForm = () => {
    // 获取当前数据
    const hourlyData = usageData.length > 0 ? usageData : Array(24).fill(0).map((_, hour) => ({ hour, value: 0 }));

    // 将24小时数据分为三列
    const firstColumn = hourlyData.filter(item => item.hour < 8);
    const secondColumn = hourlyData.filter(item => item.hour >= 8 && item.hour < 16);
    const thirdColumn = hourlyData.filter(item => item.hour >= 16);

    // 表格列定义
    const columns = [
      {
        title: t('electricityUsage.hour'),
        dataIndex: 'hour',
        key: 'hour',
        width: 80,
        render: (hour: number) => `${hour}:00`,
      },
      {
        title: t('electricityUsage.value') + ' (kWh)',
        dataIndex: 'value',
        key: 'value',
        render: (value: number, record: any) => {
          const index = hourlyData.findIndex(item => item.hour === record.hour);
          return (
            <InputNumber
              min={0}
              precision={1}
              value={value}
              step={0.1}
              stringMode={true}
              onChange={(newValue) => {
                console.log("SameEveryday InputNumber onChange:", record.hour, newValue);
                if (newValue !== null) {
                  const numValue = parseFloat(newValue.toString());
                  if (!isNaN(numValue)) {
                    // 创建新的数据数组，确保每个对象也是新的
                    const newData = hourlyData.map((item, idx) =>
                      idx === index ? { ...item, value: numValue } : { ...item }
                    );
                    handleDataChange(newData);
                  }
                } else {
                  // 创建新的数据数组，确保每个对象也是新的
                  const newData = hourlyData.map((item, idx) =>
                    idx === index ? { ...item, value: 0 } : { ...item }
                  );
                  handleDataChange(newData);
                }
              }}
              onBlur={(e) => {
                console.log("SameEveryday InputNumber onBlur:", record.hour, e.target.value);
                // 确保在失去焦点时也更新数据
                const numValue = parseFloat(e.target.value);
                if (!isNaN(numValue)) {
                  // 创建新的数据数组，确保每个对象也是新的
                  const newData = hourlyData.map((item, idx) =>
                    idx === index ? { ...item, value: numValue } : { ...item }
                  );
                  handleDataChange(newData);
                }
              }}
              onPressEnter={(e) => {
                console.log("SameEveryday InputNumber onPressEnter:", record.hour, e.target.value);
                const numValue = parseFloat(e.target.value);
                if (!isNaN(numValue)) {
                  // 创建新的数据数组，确保每个对象也是新的
                  const newData = hourlyData.map((item, idx) =>
                    idx === index ? { ...item, value: numValue } : { ...item }
                  );
                  handleDataChange(newData);
                }
              }}
              style={{ width: '100%' }}
            />
          );
        },
      },
    ];

    // 检查数据是否完备
    const isDataComplete = hourlyData.every(item => item.value !== undefined && item.value !== null);

    // 处理完成输入按钮点击
    const handleCompleteInput = () => {
      setShowCompactTable(true);
    };

    // 渲染紧凑表格
    const renderCompactTable = () => {
      // 将数据重新组织为6行8列的表格
      const hours = Array(8).fill(0).map((_, i) => i);
      const hours2 = Array(8).fill(0).map((_, i) => i + 8);
      const hours3 = Array(8).fill(0).map((_, i) => i + 16);

      const values1 = hours.map(hour => {
        const item = hourlyData.find(d => d.hour === hour);
        return item ? item.value : 0;
      });

      const values2 = hours2.map(hour => {
        const item = hourlyData.find(d => d.hour === hour);
        return item ? item.value : 0;
      });

      const values3 = hours3.map(hour => {
        const item = hourlyData.find(d => d.hour === hour);
        return item ? item.value : 0;
      });

      return (
        <div style={{ marginTop: 16, marginBottom: 16 }}>
          <Table
            size="small"
            pagination={false}
            bordered
            columns={[
              { title: '', dataIndex: 'hour0', key: 'hour0' },
              { title: '0:00', dataIndex: 'h0', key: 'h0' },
              { title: '1:00', dataIndex: 'h1', key: 'h1' },
              { title: '2:00', dataIndex: 'h2', key: 'h2' },
              { title: '3:00', dataIndex: 'h3', key: 'h3' },
              { title: '4:00', dataIndex: 'h4', key: 'h4' },
              { title: '5:00', dataIndex: 'h5', key: 'h5' },
              { title: '6:00', dataIndex: 'h6', key: 'h6' },
              { title: '7:00', dataIndex: 'h7', key: 'h7' },
            ]}
            dataSource={[
              {
                key: 'values1',
                hour0: t('electricityUsage.value') + ' (kWh)',
                h0: values1[0], h1: values1[1], h2: values1[2], h3: values1[3],
                h4: values1[4], h5: values1[5], h6: values1[6], h7: values1[7]
              },
              {
                key: 'hours2',
                hour0: '',
                h0: '8:00', h1: '9:00', h2: '10:00', h3: '11:00',
                h4: '12:00', h5: '13:00', h6: '14:00', h7: '15:00'
              },
              {
                key: 'values2',
                hour0: t('electricityUsage.value') + ' (kWh)',
                h0: values2[0], h1: values2[1], h2: values2[2], h3: values2[3],
                h4: values2[4], h5: values2[5], h6: values2[6], h7: values2[7]
              },
              {
                key: 'hours3',
                hour0: '',
                h0: '16:00', h1: '17:00', h2: '18:00', h3: '19:00',
                h4: '20:00', h5: '21:00', h6: '22:00', h7: '23:00'
              },
              {
                key: 'values3',
                hour0: t('electricityUsage.value') + ' (kWh)',
                h0: values3[0], h1: values3[1], h2: values3[2], h3: values3[3],
                h4: values3[4], h5: values3[5], h6: values3[6], h7: values3[7]
              },
            ]}
          />
          <Button
            type="primary"
            onClick={() => setShowCompactTable(false)}
            style={{ marginTop: 16 }}
          >
            {t('electricityUsage.editData')}
          </Button>
        </div>
      );
    };

    // 渲染输入表格
    const renderInputTables = () => {
      return (
        <>
          <Row gutter={16}>
            <Col span={8}>
              <Table
                dataSource={firstColumn}
                columns={columns}
                rowKey="hour"
                pagination={false}
                size="small"
                bordered
              />
            </Col>
            <Col span={8}>
              <Table
                dataSource={secondColumn}
                columns={columns}
                rowKey="hour"
                pagination={false}
                size="small"
                bordered
              />
            </Col>
            <Col span={8}>
              <Table
                dataSource={thirdColumn}
                columns={columns}
                rowKey="hour"
                pagination={false}
                size="small"
                bordered
              />
            </Col>
          </Row>
          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Button
              type="primary"
              onClick={handleCompleteInput}
              disabled={!isDataComplete}
            >
              {t('electricityUsage.completeInput')}
            </Button>
          </div>
        </>
      );
    };

    return (
      <div>
        <Alert
          message={t('electricityUsage.sameEveryday')}
          description={t('projectWizard.electricityUsageDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {showCompactTable ? renderCompactTable() : renderInputTables()}

        {isDataComplete && (
          <ElectricityUsageChart
            data={hourlyData}
            type="hourly"
            style={{ marginTop: 24 }}
          />
        )}
      </div>
    );
  };

  // 渲染每周相同的数据输入界面
  const renderSameEveryWeekForm = () => {
    // 获取当前数据
    let weeklyData = usageData;

    // 如果没有数据，初始化一周的数据
    if (weeklyData.length === 0) {
      weeklyData = [];
      for (let day = 0; day < 7; day++) {
        for (let hour = 0; hour < 24; hour++) {
          weeklyData.push({ day, hour, value: 0 });
        }
      }
    }

    // 获取星期几的名称
    const getDayName = (day: number) => {
      const days = [
        t('electricityUsage.monday'),
        t('electricityUsage.tuesday'),
        t('electricityUsage.wednesday'),
        t('electricityUsage.thursday'),
        t('electricityUsage.friday'),
        t('electricityUsage.saturday'),
        t('electricityUsage.sunday'),
      ];
      return days[day];
    };

    // 表格列定义
    const columns = [
      {
        title: t('electricityUsage.day'),
        dataIndex: 'day',
        key: 'day',
        render: (day: number) => getDayName(day),
      },
      {
        title: t('electricityUsage.hour'),
        dataIndex: 'hour',
        key: 'hour',
        render: (hour: number) => `${hour}:00`,
      },
      {
        title: t('electricityUsage.value') + ' (kWh)',
        dataIndex: 'value',
        key: 'value',
        render: (value: number, record: any, index: number) => (
          <InputNumber
            min={0}
            precision={1}
            value={value}
            step={0.1}
            stringMode={true}
            onChange={(newValue) => {
              console.log("Weekly InputNumber onChange:", record.day, record.hour, newValue);
              if (newValue !== null) {
                const numValue = parseFloat(newValue.toString());
                if (!isNaN(numValue)) {
                  // 创建新的数据数组，确保每个对象也是新的
                  const newData = weeklyData.map((item, idx) =>
                    idx === index ? { ...item, value: numValue } : { ...item }
                  );
                  handleDataChange(newData);
                }
              } else {
                // 创建新的数据数组，确保每个对象也是新的
                const newData = weeklyData.map((item, idx) =>
                  idx === index ? { ...item, value: 0 } : { ...item }
                );
                handleDataChange(newData);
              }
            }}
            onBlur={(e) => {
              console.log("Weekly InputNumber onBlur:", record.day, record.hour, e.target.value);
              // 确保在失去焦点时也更新数据
              const numValue = parseFloat(e.target.value);
              if (!isNaN(numValue)) {
                // 创建新的数据数组，确保每个对象也是新的
                const newData = weeklyData.map((item, idx) =>
                  idx === index ? { ...item, value: numValue } : { ...item }
                );
                handleDataChange(newData);
              }
            }}
            onPressEnter={(e) => {
              console.log("Weekly InputNumber onPressEnter:", record.day, record.hour, e.target.value);
              const numValue = parseFloat(e.target.value);
              if (!isNaN(numValue)) {
                // 创建新的数据数组，确保每个对象也是新的
                const newData = weeklyData.map((item, idx) =>
                  idx === index ? { ...item, value: numValue } : { ...item }
                );
                handleDataChange(newData);
              }
            }}
          />
        ),
      },
    ];

    // 按天分组数据
    const groupedData = weeklyData.reduce((acc: any, item: any) => {
      if (!acc[item.day]) {
        acc[item.day] = [];
      }
      acc[item.day].push(item);
      return acc;
    }, {});

    // 检查数据是否完备
    const isDataComplete = weeklyData.length > 0 && weeklyData.every(item => item.value !== undefined && item.value !== null);

    // 提取当前选中日期的24小时数据用于图表显示
    const hourlyData = isDataComplete && groupedData[selectedDay] ?
      groupedData[selectedDay].map((item: any) => ({ hour: item.hour, value: item.value })) :
      [];

    // 处理标签页切换
    const handleTabChange = (activeKey: string) => {
      setSelectedDay(activeKey);
    };

    return (
      <div>
        <Alert
          message={t('electricityUsage.sameEveryWeek')}
          description={t('projectWizard.electricityUsageDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Tabs type="card" onChange={handleTabChange} activeKey={selectedDay}>
          {Object.keys(groupedData).map((day) => (
            <TabPane tab={getDayName(parseInt(day))} key={day}>
              <Table
                dataSource={groupedData[day]}
                columns={columns.filter(col => col.dataIndex !== 'day')}
                rowKey={(record) => `${record.day}-${record.hour}`}
                pagination={false}
                size="small"
                bordered
              />
            </TabPane>
          ))}
        </Tabs>

        {isDataComplete && (
          <ElectricityUsageChart
            data={hourlyData}
            type="hourly"
            style={{ marginTop: 24 }}
          />
        )}
      </div>
    );
  };

  // 渲染分月份每天相同的数据输入界面
  const renderMonthlyDifferentForm = () => {
    // 获取当前数据
    let monthlyData = usageData;

    // 如果没有数据，初始化12个月的数据
    if (monthlyData.length === 0) {
      monthlyData = [];
      for (let month = 1; month <= 12; month++) {
        for (let hour = 0; hour < 24; hour++) {
          monthlyData.push({ month, hour, value: 0 });
        }
      }
    }

    // 获取月份名称
    const getMonthName = (month: number) => {
      return t(`common.month${month}`);
    };

    // 表格列定义
    const columns = [
      {
        title: t('electricityUsage.month'),
        dataIndex: 'month',
        key: 'month',
        render: (month: number) => getMonthName(month),
      },
      {
        title: t('electricityUsage.hour'),
        dataIndex: 'hour',
        key: 'hour',
        render: (hour: number) => `${hour}:00`,
      },
      {
        title: t('electricityUsage.value') + ' (kWh)',
        dataIndex: 'value',
        key: 'value',
        render: (value: number, record: any, index: number) => (
          <InputNumber
            min={0}
            precision={1}
            value={value}
            step={0.1}
            stringMode={true}
            onChange={(newValue) => {
              console.log("Monthly InputNumber onChange:", record.month, record.hour, newValue);
              if (newValue !== null) {
                const numValue = parseFloat(newValue.toString());
                if (!isNaN(numValue)) {
                  // 创建新的数据数组，确保每个对象也是新的
                  const newData = monthlyData.map((item, idx) =>
                    idx === index ? { ...item, value: numValue } : { ...item }
                  );
                  handleDataChange(newData);
                }
              } else {
                // 创建新的数据数组，确保每个对象也是新的
                const newData = monthlyData.map((item, idx) =>
                  idx === index ? { ...item, value: 0 } : { ...item }
                );
                handleDataChange(newData);
              }
            }}
            onBlur={(e) => {
              console.log("Monthly InputNumber onBlur:", record.month, record.hour, e.target.value);
              // 确保在失去焦点时也更新数据
              const numValue = parseFloat(e.target.value);
              if (!isNaN(numValue)) {
                // 创建新的数据数组，确保每个对象也是新的
                const newData = monthlyData.map((item, idx) =>
                  idx === index ? { ...item, value: numValue } : { ...item }
                );
                handleDataChange(newData);
              }
            }}
            onPressEnter={(e) => {
              console.log("Monthly InputNumber onPressEnter:", record.month, record.hour, e.target.value);
              const numValue = parseFloat(e.target.value);
              if (!isNaN(numValue)) {
                // 创建新的数据数组，确保每个对象也是新的
                const newData = monthlyData.map((item, idx) =>
                  idx === index ? { ...item, value: numValue } : { ...item }
                );
                handleDataChange(newData);
              }
            }}
          />
        ),
      },
    ];

    // 按月份分组数据
    const groupedData = monthlyData.reduce((acc: any, item: any) => {
      if (!acc[item.month]) {
        acc[item.month] = [];
      }
      acc[item.month].push(item);
      return acc;
    }, {});

    // 检查数据是否完备
    const isDataComplete = monthlyData.length > 0 && monthlyData.every(item => item.value !== undefined && item.value !== null);

    // 提取当前选中月份的24小时数据用于图表显示
    const hourlyData = isDataComplete && groupedData[selectedMonth] ?
      groupedData[selectedMonth].map((item: any) => ({ hour: item.hour, value: item.value })) :
      [];

    // 处理标签页切换
    const handleTabChange = (activeKey: string) => {
      setSelectedMonth(activeKey);
    };

    return (
      <div>
        <Alert
          message={t('electricityUsage.monthlyDifferent')}
          description={t('projectWizard.electricityUsageDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Tabs type="card" onChange={handleTabChange} activeKey={selectedMonth}>
          {Object.keys(groupedData).map((month) => (
            <TabPane tab={getMonthName(parseInt(month))} key={month}>
              <Table
                dataSource={groupedData[month]}
                columns={columns.filter(col => col.dataIndex !== 'month')}
                rowKey={(record) => `${record.month}-${record.hour}`}
                pagination={false}
                size="small"
                bordered
              />
            </TabPane>
          ))}
        </Tabs>

        {isDataComplete && (
          <ElectricityUsageChart
            data={hourlyData}
            type="hourly"
            style={{ marginTop: 24 }}
            onOptionsChange={(options) => {
              console.log("Chart options changed:", options);
            }}
          />
        )}
      </div>
    );
  };

  // 渲染每天不同的数据输入界面
  const renderDailyDifferentForm = () => {
    // 检查数据是否完备
    const isDataComplete = usageData.length > 0;

    // 如果有数据，提取24小时数据用于图表显示
    const hourlyData = isDataComplete ?
      usageData.filter(item => item.day === 1).map(item => ({ hour: item.hour, value: item.value })) :
      [];

    return (
      <div>
        <Alert
          message={t('electricityUsage.dailyDifferent')}
          description={t('electricityUsage.templateDescription')}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Space direction="vertical" align="center">
            <Text>{t('electricityUsage.dailyDifferent')}</Text>
            <Text type="secondary">{t('electricityUsage.templateDescription')}</Text>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
            >
              {t('electricityUsage.downloadTemplate')}
            </Button>
          </Space>
        </div>

        {isDataComplete && (
          <ElectricityUsageChart
            data={hourlyData}
            type="hourly"
            style={{ marginTop: 24 }}
            onOptionsChange={(options) => {
              console.log("Chart options changed:", options);
            }}
          />
        )}
      </div>
    );
  };

  return (
    <div className="electricity-usage-input">
      <Form layout="vertical">
        <Form.Item label={t('electricityUsage.dataType')}>
          <Radio.Group onChange={handleTypeChange} value={usageType}>
            <Radio.Button value="sameEveryday">{t('electricityUsage.sameEveryday')}</Radio.Button>
            <Radio.Button value="patternByDay">{t('electricityUsage.patternByDay')}</Radio.Button>
            <Radio.Button value="patternByMonth">{t('electricityUsage.patternByMonth')}</Radio.Button>
            <Radio.Button value="dailyDifferent">{t('electricityUsage.dailyDifferent')}</Radio.Button>
          </Radio.Group>
        </Form.Item>
      </Form>

      <Card style={{ marginTop: '16px' }}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={t('electricityUsage.manualInput')} key="manual">
            {renderInputForm()}
          </TabPane>
          <TabPane tab={t('electricityUsage.fileUpload')} key="upload">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message={t('electricityUsage.templateDescription')}
                type="info"
                showIcon
              />
              <Row gutter={16} align="middle">
                <Col>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleDownloadTemplate}
                  >
                    {t('electricityUsage.downloadTemplate')}
                  </Button>
                </Col>
                <Col>
                  <FileUpload
                    accept=".csv"
                    maxSize={10}
                    onUpload={handleFileUpload}
                    uploadProgress={uploadProgress}
                    isUploading={isUploading}
                    buttonText={t('electricityUsage.uploadTemplate')}
                    buttonIcon={<UploadOutlined />}
                  />
                </Col>
              </Row>
            </Space>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ElectricityUsageInput;
