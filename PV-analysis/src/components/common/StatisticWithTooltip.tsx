import React from 'react';
import { Statistic, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { StatisticProps } from 'antd/es/statistic/Statistic';

interface StatisticWithTooltipProps extends StatisticProps {
  tooltip?: string;
}

/**
 * 带有悬停提示的统计数据组件
 * 在标题后面添加一个信息图标，鼠标悬停时显示提示信息
 */
const StatisticWithTooltip: React.FC<StatisticWithTooltipProps> = ({
  title,
  tooltip,
  ...restProps
}) => {
  return (
    <Statistic
      title={
        <span>
          {title}
          {tooltip && (
            <Tooltip title={tooltip} placement="top">
              <InfoCircleOutlined style={{ marginLeft: 8, color: '#1890ff' }} />
            </Tooltip>
          )}
        </span>
      }
      {...restProps}
    />
  );
};

export default StatisticWithTooltip;
