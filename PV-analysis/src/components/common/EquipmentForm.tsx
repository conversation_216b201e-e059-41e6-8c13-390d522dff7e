import React, { useEffect, useState } from 'react';
import { Form, Input, Select, InputNumber, Divider, Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { Equipment, PVSpecs, StorageSpecs, InverterSpecs } from '../../types/database';
import PVSpecsForm from './equipment/PVSpecsForm';
import StorageSpecsForm from './equipment/StorageSpecsForm';
import InverterSpecsForm from './equipment/InverterSpecsForm';
import AttachmentUpload from './AttachmentUpload';

const { Option } = Select;
const { TextArea } = Input;

interface EquipmentFormProps {
  initialValues?: Partial<Equipment>;
  form: any;
  mode?: 'create' | 'edit';
}

/**
 * 设备表单组件
 * 用于创建和编辑设备信息
 */
const EquipmentForm: React.FC<EquipmentFormProps> = ({
  initialValues,
  form
}) => {
  const { t } = useTranslation();
  const [equipmentType, setEquipmentType] = useState<'pv' | 'storage' | 'inverter' | 'other'>(
    initialValues?.type || 'pv'
  );

  // 当初始值变化时，重置表单
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
      });
      setEquipmentType(initialValues.type || 'pv');
    } else {
      form.resetFields();
    }
  }, [initialValues, form]);

  // 设备类型选项
  const equipmentTypeOptions = [
    { value: 'pv', label: t('databases.equipment.pv') },
    { value: 'storage', label: t('databases.equipment.storage') },
    { value: 'inverter', label: t('databases.equipment.inverter') },
    { value: 'other', label: t('databases.equipment.other') }
  ];

  // 处理设备类型变更
  const handleTypeChange = (value: 'pv' | 'storage' | 'inverter' | 'other') => {
    setEquipmentType(value);

    // 重置specs字段
    form.setFieldsValue({
      specs: {}
    });
  };

  // 渲染设备类型表单
  const renderSpecsForm = () => {
    switch (equipmentType) {
      case 'pv':
        return <PVSpecsForm form={form} initialValues={initialValues?.specs as PVSpecs} />;
      case 'storage':
        return <StorageSpecsForm form={form} initialValues={initialValues?.specs as StorageSpecs} />;
      case 'inverter':
        return <InverterSpecsForm form={form} initialValues={initialValues?.specs as InverterSpecs} />;
      case 'other':
        return (
          <Form.Item
            name={['specs', 'description']}
            label={t('databases.equipment.description')}
          >
            <TextArea rows={4} />
          </Form.Item>
        );
      default:
        return null;
    }
  };

  return (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="name"
            label={t('databases.equipment.name')}
            rules={[{ required: true, message: t('databases.equipment.nameRequired') }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="type"
            label={t('databases.equipment.type')}
            rules={[{ required: true, message: t('databases.equipment.typeRequired') }]}
          >
            <Select
              placeholder={t('common.pleaseSelect')}
              onChange={(value) => handleTypeChange(value as 'pv' | 'storage' | 'inverter' | 'other')}
            >
              {equipmentTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="manufacturer"
            label={t('databases.equipment.manufacturer')}
            rules={[{ required: true, message: t('databases.equipment.manufacturerRequired') }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="model"
            label={t('databases.equipment.model')}
            rules={[{ required: true, message: t('databases.equipment.modelRequired') }]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('databases.equipment.specs')}</Divider>

      {renderSpecsForm()}

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="price"
            label={t('databases.equipment.price')}
            rules={[{ required: true, message: t('databases.equipment.priceRequired') }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              addonAfter="JPY"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="supplierId"
            label={t('databases.equipment.supplier')}
          >
            <Input disabled={true} placeholder={t('databases.equipment.noSupplier')} />
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('databases.equipment.attachments.title')}</Divider>

      <Form.Item
        name="attachments"
        label={t('databases.equipment.attachments.label')}
        tooltip={t('databases.equipment.attachments.tooltip')}
      >
        <AttachmentUpload
          equipmentId={initialValues?.id}
          value={initialValues?.attachments || []}
          onChange={(attachments) => {
            // 更新表单中的附件字段
            form.setFieldsValue({ attachments });
            console.log('附件已更新:', attachments);
          }}
        />
      </Form.Item>
    </Form>
  );
};

export default EquipmentForm;
