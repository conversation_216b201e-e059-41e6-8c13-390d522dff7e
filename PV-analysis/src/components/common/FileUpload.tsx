import React, { useState } from 'react';
import { Upload, Button, message, Progress } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { UploadProps, UploadFile } from 'antd/es/upload/interface';

interface FileUploadProps {
  accept?: string;
  maxSize?: number; // 单位：MB
  multiple?: boolean;
  showUploadList?: boolean;
  onUpload: (file: File) => Promise<void>;
  uploadProgress?: number;
  isUploading?: boolean;
  buttonText?: string;
  buttonIcon?: React.ReactNode;
  buttonType?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
}

const FileUpload: React.FC<FileUploadProps> = ({
  accept = '.csv',
  maxSize = 10,
  multiple = false,
  showUploadList = true,
  onUpload,
  uploadProgress = 0,
  isUploading = false,
  buttonText,
  buttonIcon = <UploadOutlined />,
  buttonType = 'primary',
}) => {
  const { t } = useTranslation();
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handleBeforeUpload = (file: File) => {
    // 检查文件类型
    const isAcceptedType = accept
      .split(',')
      .some(type => file.name.toLowerCase().endsWith(type.replace('*', '').trim()));
    
    if (!isAcceptedType) {
      message.error(t('common.fileTypeError', { type: accept }));
      return Upload.LIST_IGNORE;
    }
    
    // 检查文件大小
    const isLessThanMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLessThanMaxSize) {
      message.error(t('common.fileSizeError', { size: maxSize }));
      return Upload.LIST_IGNORE;
    }
    
    return true;
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  const customRequest = async ({ file, onSuccess, onError }: any) => {
    try {
      await onUpload(file);
      onSuccess();
    } catch (error) {
      onError(error);
    }
  };

  return (
    <div>
      <Upload
        accept={accept}
        multiple={multiple}
        fileList={fileList}
        beforeUpload={handleBeforeUpload}
        onChange={handleChange}
        customRequest={customRequest}
        showUploadList={showUploadList}
        disabled={isUploading}
      >
        <Button 
          type={buttonType} 
          icon={buttonIcon} 
          loading={isUploading}
          disabled={isUploading}
        >
          {buttonText || t('common.upload')}
        </Button>
      </Upload>
      
      {isUploading && (
        <Progress 
          percent={uploadProgress} 
          status={uploadProgress < 100 ? 'active' : 'success'} 
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );
};

export default FileUpload;
