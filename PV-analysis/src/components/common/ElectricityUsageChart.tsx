import React, { useState, useEffect, useMemo } from 'react';
import { Card, Radio, Select, Slider } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';

const { Option } = Select;

interface ElectricityUsageChartProps {
  data: Array<{ hour: number; value: number }>;
  type?: 'hourly' | 'daily' | 'monthly';
  style?: React.CSSProperties;
  onOptionsChange?: (options: ElectricityUsageChartOptions) => void;
}

interface ElectricityUsageChartOptions {
  viewType: 'hourly' | 'daily' | 'monthly';
  month: number; // 1-12表示月份，0表示全年
  day: number; // 1-31表示日期
}

/**
 * 用电曲线图组件
 * 支持按小时、按日、按月三种显示方式
 */
const ElectricityUsageChart: React.FC<ElectricityUsageChartProps> = ({
  data,
  type = 'hourly',
  style,
  onOptionsChange
}) => {
  const { t } = useTranslation();
  const [viewType, setViewType] = useState<'hourly' | 'daily' | 'monthly'>(type);
  const [month, setMonth] = useState<number>(1); // 默认显示1月
  const [day, setDay] = useState<number>(1); // 默认显示1日
  const [chartOption, setChartOption] = useState<EChartsOption>({});

  // 检查数据是否完备
  const isDataComplete = data && data.length === 24 && data.every(item => item.value !== undefined);

  // 当视图类型变化时，通知父组件
  useEffect(() => {
    if (onOptionsChange) {
      onOptionsChange({
        viewType,
        month,
        day
      });
    }
  }, [viewType, month, day, onOptionsChange]);

  // 当数据或视图类型或月份日期变化时，更新图表选项
  useEffect(() => {
    if (!isDataComplete) return;

    // 根据视图类型生成不同的图表选项
    switch (viewType) {
      case 'hourly':
        setChartOption(generateHourlyChartOption(month, day));
        break;
      case 'daily':
        setChartOption(generateDailyChartOption(month));
        break;
      case 'monthly':
        setChartOption(generateMonthlyChartOption());
        break;
      default:
        setChartOption(generateHourlyChartOption(month, day));
    }
  }, [data, viewType, month, day]);

  // 生成按小时视图的图表选项
  const generateHourlyChartOption = (month: number = 1, day: number = 1): EChartsOption => {
    // 优化数据处理，避免多次映射
    const hours = [];
    const values = [];

    // 一次性处理数据
    for (let i = 0; i < data.length; i++) {
      hours.push(`${data[i].hour}:00`);
      values.push(data[i].value);
    }

    // 判断是否显示全年数据
    const monthDisplay = month === 0 ? monthNames[0] : monthNames[month];

    return {
      title: {
        text: `${monthDisplay} ${day}${t('common.dayUnit')} ${t('electricityUsage.hourlyChart')}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c} kWh',
        confine: true,
        enterable: false,
        hideDelay: 100
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('electricityUsage.hour'),
        nameLocation: 'middle',
        nameGap: 30
      },
      yAxis: {
        type: 'value',
        name: t('electricityUsage.value') + ' (kWh)',
        nameLocation: 'middle',
        nameGap: 50
      },
      series: [
        {
          name: t('electricityUsage.value'),
          type: 'line',
          data: values,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          }
        }
      ],
      grid: {
        left: '5%',
        right: '5%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      }
    };
  };

  // 获取月份的天数
  const getDaysInMonth = (month: number) => {
    if (month === 0) return 30; // 全年模式，使用默认30天

    // 2月特殊处理（简化，不考虑闰年）
    if (month === 2) return 28;

    // 小月
    if ([4, 6, 9, 11].includes(month)) return 30;

    // 大月
    return 31;
  };

  // 使用useMemo缓存月份名称，避免重复计算
  const monthNames = useMemo(() => {
    return [
      t('common.fullYear'),
      t('common.january'),
      t('common.february'),
      t('common.march'),
      t('common.april'),
      t('common.may'),
      t('common.june'),
      t('common.july'),
      t('common.august'),
      t('common.september'),
      t('common.october'),
      t('common.november'),
      t('common.december')
    ];
  }, [t]);

  // 生成按日视图的图表选项
  const generateDailyChartOption = (month: number = 0): EChartsOption => {
    // 计算每天的用电量
    const dailyData = [];
    const cumulativeData = [];
    let cumulative = 0;

    const daysInMonth = getDaysInMonth(month);
    const dailyTotal = data.reduce((sum, item) => sum + item.value, 0);

    // 生成每天的数据 - 优化循环，避免重复计算
    for (let day = 1; day <= daysInMonth; day++) {
      dailyData.push(dailyTotal);
      cumulative += dailyTotal;
      cumulativeData.push(cumulative);
    }

    const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
    const monthDisplay = month === 0 ? monthNames[0] : monthNames[month];

    return {
      title: {
        text: `${monthDisplay} ${t('electricityUsage.dailyChart')}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        enterable: false,
        hideDelay: 100,
        formatter: function(params: any) {
          const day = params[0].axisValue;
          const dailyValue = params[0].value;
          const cumulativeValue = params[1].value;
          return `${t('electricityUsage.day')}: ${day}<br/>${t('electricityUsage.value')}: ${dailyValue.toFixed(1)} kWh<br/>${t('electricityUsage.totalConsumption')}: ${cumulativeValue.toFixed(1)} kWh`;
        }
      },
      legend: {
        data: [t('electricityUsage.value'), t('electricityUsage.totalConsumption')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('electricityUsage.day'),
        nameLocation: 'middle',
        nameGap: 30
      },
      yAxis: [
        {
          type: 'value',
          name: t('electricityUsage.value') + ' (kWh)',
          position: 'left',
          nameLocation: 'middle',
          nameGap: 50,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#1890ff'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: t('electricityUsage.totalConsumption') + ' (kWh)',
          position: 'right',
          nameLocation: 'middle',
          nameGap: 50,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#faad14'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: t('electricityUsage.value'),
          type: 'bar',
          data: dailyData,
          yAxisIndex: 0,
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: t('electricityUsage.totalConsumption'),
          type: 'line',
          data: cumulativeData,
          yAxisIndex: 1,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 2,
            color: '#faad14'
          },
          itemStyle: {
            color: '#faad14'
          }
        }
      ],
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      }
    };
  };

  // 生成按月视图的图表选项
  const generateMonthlyChartOption = (): EChartsOption => {
    // 计算每月的用电量 - 优化计算逻辑
    const monthlyData = [];
    const cumulativeData = [];
    let cumulative = 0;

    // 计算一天的总用电量（只计算一次）
    const dailyTotal = data.reduce((sum, item) => sum + item.value, 0);

    // 12个月
    for (let month = 1; month <= 12; month++) {
      const daysInMonth = getDaysInMonth(month);
      // 计算每月的总用电量（根据每月天数，每天24小时的总和）
      const monthlyTotal = dailyTotal * daysInMonth;
      monthlyData.push(monthlyTotal);

      // 计算累计用电量
      cumulative += monthlyTotal;
      cumulativeData.push(cumulative);
    }

    // 截取月份名称（不包括第一个"全年"选项）
    const months = monthNames.slice(1);

    return {
      title: {
        text: t('electricityUsage.monthlyChart'),
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        enterable: false,
        hideDelay: 100,
        formatter: function(params: any) {
          const month = params[0].axisValue;
          const monthlyValue = params[0].value;
          const cumulativeValue = params[1].value;
          return `${t('electricityUsage.month')}: ${month}<br/>${t('electricityUsage.value')}: ${monthlyValue.toFixed(1)} kWh<br/>${t('electricityUsage.totalConsumption')}: ${cumulativeValue.toFixed(1)} kWh`;
        }
      },
      legend: {
        data: [t('electricityUsage.value'), t('electricityUsage.totalConsumption')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: months,
        name: t('electricityUsage.month'),
        nameLocation: 'middle',
        nameGap: 30
      },
      yAxis: [
        {
          type: 'value',
          name: t('electricityUsage.value') + ' (kWh)',
          position: 'left',
          nameLocation: 'middle',
          nameGap: 50,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#1890ff'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: t('electricityUsage.totalConsumption') + ' (kWh)',
          position: 'right',
          nameLocation: 'middle',
          nameGap: 50,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#faad14'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: t('electricityUsage.value'),
          type: 'bar',
          data: monthlyData,
          yAxisIndex: 0,
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: t('electricityUsage.totalConsumption'),
          type: 'line',
          data: cumulativeData,
          yAxisIndex: 1,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 2,
            color: '#faad14'
          },
          itemStyle: {
            color: '#faad14'
          }
        }
      ],
      grid: {
        left: '5%',
        right: '5%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      }
    };
  };

  // 如果数据不完备，显示空状态
  if (!isDataComplete) {
    return null;
  }

  // 渲染可视化选项
  const renderVisualizationOptions = () => {
    return (
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '16px', marginBottom: 16 }}>
          {/* 查看方式 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('electricityUsage.viewMode')}:</span>
            <Radio.Group value={viewType} onChange={e => setViewType(e.target.value)}>
              <Radio.Button value="hourly">{t('electricityUsage.hourly')}</Radio.Button>
              <Radio.Button value="daily">{t('electricityUsage.daily')}</Radio.Button>
              <Radio.Button value="monthly">{t('electricityUsage.monthly')}</Radio.Button>
            </Radio.Group>
          </div>

          {/* 月份选择 - 仅在非月视图时显示 */}
          {viewType !== 'monthly' && (
            <div>
              <span style={{ marginRight: 8 }}>{t('electricityUsage.month')}:</span>
              <Select
                value={month}
                style={{ width: 80 }}
                onChange={(value) => setMonth(value)}
              >
                <Option key={0} value={0}>{t('common.fullYear')}</Option>
                {Array.from({ length: 12 }, (_, i) => i + 1).map(m => (
                  <Option key={m} value={m}>{m}{t('common.monthUnit')}</Option>
                ))}
              </Select>
            </div>
          )}
        </div>

        {/* 日期滑块 - 仅在小时视图时显示 */}
        {viewType === 'hourly' && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <span style={{ marginRight: 8, minWidth: '40px' }}>{t('electricityUsage.day')}:</span>
            <Slider
              min={1}
              max={31}
              value={day}
              style={{ flex: 1, marginRight: 8 }}
              onChange={(value) => setDay(value)}
            />
            <span style={{ minWidth: '40px', textAlign: 'right' }}>{day}{t('common.dayUnit')}</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card
      title={t('electricityUsage.usageChart')}
      style={style}
    >
      {renderVisualizationOptions()}
      <ReactECharts
        option={chartOption}
        style={{ height: 400 }}
        notMerge={true}
        lazyUpdate={true}
        opts={{
          renderer: 'canvas',
          devicePixelRatio: window.devicePixelRatio
        }}
      />
    </Card>
  );
};

export default ElectricityUsageChart;
