import React, { useState } from 'react';
import { Tooltip, Modal, Button, Space, Typography, message } from 'antd';
import {
  FileOutlined,
  PictureOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileZipOutlined,
  DownloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { EquipmentAttachment } from '../../types/database';
import { downloadAttachment } from '../../services/attachmentService';

const { Text } = Typography;

interface AttachmentPreviewProps {
  attachments?: EquipmentAttachment[];
  size?: 'small' | 'default' | 'large';
  showFileName?: boolean;
  maxDisplay?: number;
  layout?: 'horizontal' | 'vertical' | 'grid';
}

/**
 * 附件预览组件
 * 用于显示设备附件的缩略图或格式图标
 */
const AttachmentPreview: React.FC<AttachmentPreviewProps> = ({
  attachments = [],
  size = 'default',
  showFileName = false,
  maxDisplay = 3,
  layout = 'horizontal'
}) => {
  const { t } = useTranslation();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentAttachment, setCurrentAttachment] = useState<EquipmentAttachment | null>(null);
  const [downloadConfirmVisible, setDownloadConfirmVisible] = useState(false);

  // 获取尺寸
  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 48;
      default:
        return 32;
    }
  };

  // 获取文件图标
  const getFileIcon = (fileType: string, fileName: string) => {
    if (fileType.startsWith('image/')) {
      return <PictureOutlined style={{ fontSize: getIconSize(), color: '#1890ff' }} />;
    } else if (fileType.includes('pdf') || fileName.endsWith('.pdf')) {
      return <FilePdfOutlined style={{ fontSize: getIconSize(), color: '#ff4d4f' }} />;
    } else if (fileType.includes('word') || fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
      return <FileWordOutlined style={{ fontSize: getIconSize(), color: '#1890ff' }} />;
    } else if (fileType.includes('excel') || fileName.endsWith('.xls') || fileName.endsWith('.xlsx')) {
      return <FileExcelOutlined style={{ fontSize: getIconSize(), color: '#52c41a' }} />;
    } else if (fileType.includes('zip') || fileName.endsWith('.zip') || fileName.endsWith('.rar')) {
      return <FileZipOutlined style={{ fontSize: getIconSize(), color: '#faad14' }} />;
    } else if (fileType.includes('text') || fileName.endsWith('.txt')) {
      return <FileTextOutlined style={{ fontSize: getIconSize(), color: '#722ed1' }} />;
    } else {
      return <FileOutlined style={{ fontSize: getIconSize(), color: '#8c8c8c' }} />;
    }
  };

  // 处理附件点击
  const handleAttachmentClick = (attachment: EquipmentAttachment) => {
    setCurrentAttachment(attachment);

    // 如果是图片，直接预览
    if (attachment.fileType.startsWith('image/')) {
      setPreviewVisible(true);
    } else if (attachment.fileType.includes('pdf')) {
      // PDF也可以预览
      setPreviewVisible(true);
    } else {
      // 其他文件弹出下载确认对话框
      setDownloadConfirmVisible(true);
    }
  };

  // 处理下载
  const handleDownload = async () => {
    if (currentAttachment) {
      try {
        await downloadAttachment(currentAttachment);
        message.success(t('databases.equipment.attachments.downloadSuccess'));
      } catch (error) {
        console.error('下载附件失败:', error);
        message.error(t('databases.equipment.attachments.downloadError'));
      }

      setDownloadConfirmVisible(false);
      setPreviewVisible(false);
    }
  };

  // 渲染附件列表
  const renderAttachments = () => {
    const displayAttachments = attachments.slice(0, maxDisplay);
    const remainingCount = attachments.length - maxDisplay;

    const attachmentItems = displayAttachments.map((attachment, index) => (
      <Tooltip
        key={attachment.id}
        title={attachment.name}
        placement="top"
      >
        <div
          className="attachment-preview-item"
          onClick={() => handleAttachmentClick(attachment)}
          style={{
            cursor: 'pointer',
            display: 'inline-flex',
            flexDirection: layout === 'vertical' ? 'column' : 'row',
            alignItems: 'center',
            margin: '0 8px 8px 0',
            padding: '4px',
            border: '1px solid #f0f0f0',
            borderRadius: '4px',
            backgroundColor: '#fafafa'
          }}
        >
          {getFileIcon(attachment.fileType, attachment.name)}
          {showFileName && (
            <Text
              ellipsis
              style={{
                marginLeft: layout === 'vertical' ? 0 : 8,
                marginTop: layout === 'vertical' ? 4 : 0,
                maxWidth: layout === 'vertical' ? getIconSize() * 2 : 120
              }}
            >
              {attachment.name}
            </Text>
          )}
        </div>
      </Tooltip>
    ));

    // 如果有更多附件，显示+N
    if (remainingCount > 0) {
      attachmentItems.push(
        <Tooltip key="more" title={t('databases.equipment.attachments.moreAttachments', { count: remainingCount })}>
          <div
            className="attachment-preview-more"
            style={{
              cursor: 'pointer',
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 8px 8px 0',
              padding: '4px 8px',
              border: '1px solid #f0f0f0',
              borderRadius: '4px',
              backgroundColor: '#fafafa'
            }}
          >
            +{remainingCount}
          </div>
        </Tooltip>
      );
    }

    return (
      <div
        className="attachment-preview-container"
        style={{
          display: layout === 'grid' ? 'grid' : 'flex',
          gridTemplateColumns: layout === 'grid' ? 'repeat(auto-fill, minmax(80px, 1fr))' : 'none',
          flexDirection: layout === 'vertical' ? 'column' : 'row',
          flexWrap: layout === 'horizontal' ? 'wrap' : 'nowrap',
          gap: '8px'
        }}
      >
        {attachmentItems}
      </div>
    );
  };

  return (
    <>
      {attachments.length > 0 ? (
        renderAttachments()
      ) : (
        <Text type="secondary">{t('databases.equipment.attachments.noAttachments')}</Text>
      )}

      {/* 预览对话框 */}
      <Modal
        title={currentAttachment?.name}
        open={previewVisible}
        footer={[
          <Button key="download" icon={<DownloadOutlined />} onClick={handleDownload}>
            {t('common.download')}
          </Button>,
          <Button key="close" type="primary" onClick={() => setPreviewVisible(false)}>
            {t('common.close')}
          </Button>
        ]}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        {currentAttachment?.fileType.startsWith('image/') ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <img
              src={currentAttachment.url}
              alt={currentAttachment.name}
              style={{
                maxWidth: '100%',
                maxHeight: '70vh',
                objectFit: 'contain',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }}
              onError={(e) => {
                console.error('图片加载失败:', currentAttachment.url);
                message.error(t('databases.equipment.attachments.previewError'));

                // 尝试使用缓存破坏技术重新加载图片
                const timestamp = new Date().getTime();
                const newSrc = currentAttachment.url.includes('?')
                  ? `${currentAttachment.url}&_t=${timestamp}`
                  : `${currentAttachment.url}?_t=${timestamp}`;

                console.log('尝试使用缓存破坏技术重新加载图片:', newSrc);
                (e.target as HTMLImageElement).src = newSrc;

                // 如果再次失败，显示错误图标
                (e.target as HTMLImageElement).onerror = () => {
                  console.error('图片二次加载失败');
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItYWxlcnQtdHJpYW5nbGUiPjxwYXRoIGQ9Ik0xMC4yOSAzLjg2TDEuODIgMThhMiAyIDAgMCAwIDEuNzEgM2gxNi45NGEyIDIgMCAwIDAgMS43MS0zTDEzLjcxIDMuODZhMiAyIDAgMCAwLTMuNDIgMHoiPjwvcGF0aD48bGluZSB4MT0iMTIiIHkxPSI5IiB4Mj0iMTIiIHkyPSIxMyI+PC9saW5lPjxsaW5lIHgxPSIxMiIgeTE9IjE3IiB4Mj0iMTIuMDEiIHkyPSIxNyI+PC9saW5lPjwvc3ZnPg==';
                  (e.target as HTMLImageElement).style.width = '64px';
                  (e.target as HTMLImageElement).style.height = '64px';
                };
              }}
            />
          </div>
        ) : currentAttachment?.fileType.includes('pdf') ? (
          <iframe
            src={currentAttachment.url}
            width="100%"
            height="500px"
            title={currentAttachment.name}
            style={{ border: 'none' }}
            sandbox="allow-same-origin allow-scripts allow-forms"
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            {getFileIcon(currentAttachment?.fileType || '', currentAttachment?.name || '')}
            <p style={{ marginTop: 16 }}>{t('databases.equipment.attachments.cannotPreview')}</p>
          </div>
        )}
      </Modal>

      {/* 下载确认对话框 */}
      <Modal
        title={t('databases.equipment.attachments.downloadConfirmTitle')}
        open={downloadConfirmVisible}
        onOk={handleDownload}
        onCancel={() => setDownloadConfirmVisible(false)}
        okText={t('common.download')}
        cancelText={t('common.cancel')}
      >
        <p>{t('databases.equipment.attachments.downloadConfirm', { name: currentAttachment?.name })}</p>
        <Space>
          {getFileIcon(currentAttachment?.fileType || '', currentAttachment?.name || '')}
          <div>
            <div>{currentAttachment?.name}</div>
            <Text type="secondary">
              {(currentAttachment?.fileSize && currentAttachment.fileSize > 1024 * 1024)
                ? `${(currentAttachment.fileSize / (1024 * 1024)).toFixed(1)} MB`
                : (currentAttachment?.fileSize ? `${(currentAttachment.fileSize / 1024).toFixed(1)} KB` : '')}
            </Text>
          </div>
        </Space>
      </Modal>
    </>
  );
};

export default AttachmentPreview;
