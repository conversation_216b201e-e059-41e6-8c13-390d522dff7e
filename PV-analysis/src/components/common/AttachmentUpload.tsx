import React, { useState, useEffect } from 'react';
import { Upload, Button, message, Modal, List, Typography, Space, Tooltip } from 'antd';
import { UploadOutlined, FileOutlined, PictureOutlined, FileTextOutlined, DeleteOutlined, EyeOutlined, DownloadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { useTranslation } from 'react-i18next';
import { EquipmentAttachment } from '../../types/database';
import { uploadAttachment, deleteAttachment, downloadAttachment, getAttachments } from '../../services/attachmentService';

const { Text } = Typography;

interface AttachmentUploadProps {
  value?: EquipmentAttachment[];
  onChange?: (attachments: EquipmentAttachment[]) => void;
  maxCount?: number;
  equipmentId?: string; // 设备ID，用于关联附件
}

/**
 * 附件上传组件
 * 用于上传设备相关文件，如技术手册、图片等
 */
const AttachmentUpload: React.FC<AttachmentUploadProps> = ({
  value = [],
  onChange,
  maxCount = 10,
  equipmentId
}) => {
  const { t } = useTranslation();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [deleteId, setDeleteId] = useState<string>('');

  // 将EquipmentAttachment转换为UploadFile
  React.useEffect(() => {
    if (value && value.length > 0) {
      const files = value.map(attachment => ({
        uid: attachment.id,
        name: attachment.name,
        status: 'done',
        url: attachment.url,
        type: attachment.fileType,
        size: attachment.fileSize,
        // 保存原始附件数据
        response: attachment
      }));
      setFileList(files);
    }
  }, [value]);

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <PictureOutlined />;
    } else if (fileType.includes('pdf')) {
      return <FileTextOutlined />;
    } else {
      return <FileOutlined />;
    }
  };

  // 获取附件类型
  const getAttachmentType = (file: UploadFile): 'manual' | 'image' | 'datasheet' | 'other' => {
    const fileName = file.name.toLowerCase();
    const fileType = file.type || '';

    if (fileType.startsWith('image/')) {
      return 'image';
    } else if (fileName.includes('manual') || fileName.includes('guide') || fileName.endsWith('.pdf')) {
      return 'manual';
    } else if (fileName.includes('datasheet') || fileName.includes('spec')) {
      return 'datasheet';
    } else {
      return 'other';
    }
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    console.log('开始处理文件上传:', file.name);

    // 在创建模式下，equipmentId可能为undefined
    // 我们可以先创建一个临时ID，等设备创建后再关联
    const tempId = equipmentId || `temp_${new Date().getTime()}`;
    console.log('使用的设备ID:', tempId);

    try {
      // 确定附件类型
      const fileName = file.name.toLowerCase();
      const fileType = file.type;
      console.log('文件类型:', fileType);

      let attachmentType: 'manual' | 'image' | 'datasheet' | 'other' = 'other';

      if (fileType.startsWith('image/')) {
        attachmentType = 'image';
      } else if (fileName.includes('manual') || fileName.includes('guide') || fileName.endsWith('.pdf')) {
        attachmentType = 'manual';
      } else if (fileName.includes('datasheet') || fileName.includes('spec')) {
        attachmentType = 'datasheet';
      }
      console.log('确定的附件类型:', attachmentType);

      // 添加一个临时文件到列表，显示上传中状态
      const tempFile: UploadFile = {
        uid: `temp_${new Date().getTime()}`,
        name: file.name,
        status: 'uploading',
        percent: 0,
        size: file.size,
        type: file.type
      };

      // 更新文件列表，显示上传中状态
      setFileList(prev => [...prev, tempFile]);
      console.log('添加临时文件到列表，显示上传中状态:', tempFile);

      // 上传附件
      console.log('调用uploadAttachment服务...');
      const attachment = await uploadAttachment(file, attachmentType, tempId);
      console.log('附件上传成功:', attachment);

      // 更新文件列表，移除临时文件，添加上传成功的文件
      const newFile: UploadFile = {
        uid: attachment.id,
        name: attachment.name,
        status: 'done',
        url: attachment.url,
        type: attachment.fileType,
        size: attachment.fileSize,
        response: attachment
      };

      console.log('创建的新文件对象:', newFile);

      // 更新文件列表，移除临时文件
      setFileList(prev => {
        const filtered = prev.filter(f => f.uid !== tempFile.uid);
        return [...filtered, newFile];
      });

      console.log('更新后的文件列表，已移除临时文件');

      // 触发onChange
      if (onChange) {
        const attachments = [...value, attachment];
        console.log('触发onChange，新的附件列表:', attachments);
        onChange(attachments);
      }

      return true; // 上传成功
    } catch (error) {
      console.error('上传附件失败:', error);

      // 移除临时文件
      setFileList(prev => prev.filter(f => !f.uid.toString().startsWith('temp_')));

      throw error; // 抛出错误，让调用者处理
    }
  };

  // 处理删除附件
  const handleDelete = async (id: string) => {
    if (!equipmentId) {
      message.error(t('databases.equipment.attachments.noEquipmentId'));
      return;
    }

    setDeleteId(id);
    setConfirmVisible(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    try {
      console.log('开始删除附件, ID:', deleteId);

      // 先从本地列表中移除
      const newFileList = fileList.filter(file => file.uid !== deleteId);
      setFileList(newFileList);

      // 触发onChange，从value中移除
      if (onChange) {
        const attachments = value.filter(attachment => attachment.id !== deleteId);
        onChange(attachments);
      }

      // 然后调用服务器API删除
      await deleteAttachment(deleteId, equipmentId!);

      message.success(t('databases.equipment.attachments.deleteSuccess'));
    } catch (error) {
      console.error('删除附件失败:', error);
      message.error(t('databases.equipment.attachments.deleteError'));

      // 删除失败，恢复列表
      if (onChange) {
        // 重新获取附件列表
        const attachments = await getAttachments(equipmentId!);
        onChange(attachments);
      }
    } finally {
      setConfirmVisible(false);
      setDeleteId('');
    }
  };

  // 处理文件预览
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      return;
    }

    // 获取附件对象
    const attachment = file.response as EquipmentAttachment;

    // 确保URL是完整的
    let previewUrl = '';

    if (attachment && attachment.url) {
      // 优先使用附件对象中的URL
      previewUrl = attachment.url;
      if (!previewUrl.startsWith('http')) {
        const baseUrl = window.location.origin;
        const url = previewUrl.startsWith('/') ? previewUrl : `/${previewUrl}`;
        previewUrl = `${baseUrl}${url}`;
      }
    } else {
      // 回退到文件对象的URL
      previewUrl = file.url || (file.preview as string);
      if (previewUrl && !previewUrl.startsWith('http')) {
        const baseUrl = window.location.origin;
        const url = previewUrl.startsWith('/') ? previewUrl : `/${previewUrl}`;
        previewUrl = `${baseUrl}${url}`;
      }
    }

    console.log('预览URL:', previewUrl);

    // 如果是图片，显示预览
    if (file.type?.startsWith('image/')) {
      setPreviewImage(previewUrl);
      setPreviewOpen(true);
      setPreviewTitle(file.name || '');
    } else if (file.type?.includes('pdf')) {
      // 如果是PDF，显示PDF预览
      setPreviewImage(previewUrl);
      setPreviewOpen(true);
      setPreviewTitle(file.name || '');
    } else {
      // 如果是其他类型文件，显示下载确认对话框
      Modal.confirm({
        title: t('databases.equipment.attachments.downloadConfirmTitle'),
        content: (
          <div>
            <p>{t('databases.equipment.attachments.downloadConfirm', { name: file.name })}</p>
            <Space>
              {getFileIcon(file.type || '')}
              <div>
                <div>{file.name}</div>
                <Text type="secondary">
                  {(file.size && file.size > 1024 * 1024)
                    ? `${(file.size / (1024 * 1024)).toFixed(1)} MB`
                    : (file.size ? `${(file.size / 1024).toFixed(1)} KB` : '')}
                </Text>
              </div>
            </Space>
          </div>
        ),
        okText: t('common.download'),
        cancelText: t('common.cancel'),
        onOk: () => {
          // 查找对应的附件对象
          const attachment = file.response as EquipmentAttachment;
          if (attachment) {
            downloadAttachment(attachment)
              .then(() => message.success(t('databases.equipment.attachments.downloadSuccess')))
              .catch(() => message.error(t('databases.equipment.attachments.downloadError')));
          } else {
            // 如果找不到附件对象，使用传统方式下载
            const a = document.createElement('a');
            a.href = previewUrl;
            a.download = file.name || '';
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        }
      });
    }
  };

  // 自定义上传按钮
  const uploadButton = (
    <Button icon={<UploadOutlined />}>
      {t('databases.equipment.attachments.upload')}
    </Button>
  );

  // 自定义文件列表项
  const renderItem = (file: UploadFile) => {
    return (
      <List.Item
        key={file.uid}
        actions={[
          <Tooltip title={t('common.preview')}>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreview(file)}
            />
          </Tooltip>,
          <Tooltip title={t('common.download')}>
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => {
                if (file.response) {
                  downloadAttachment(file.response as EquipmentAttachment)
                    .then(() => message.success(t('databases.equipment.attachments.downloadSuccess')))
                    .catch(() => message.error(t('databases.equipment.attachments.downloadError')));
                } else {
                  message.error(t('databases.equipment.attachments.downloadError'));
                }
              }}
            />
          </Tooltip>,
          <Tooltip title={t('common.delete')}>
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(file.uid)}
            />
          </Tooltip>
        ]}
      >
        <List.Item.Meta
          avatar={getFileIcon(file.type || '')}
          title={file.name}
          description={
            <Space>
              <Text type="secondary">
                {(file.size && file.size > 1024 * 1024)
                  ? `${(file.size / (1024 * 1024)).toFixed(1)} MB`
                  : (file.size ? `${(file.size / 1024).toFixed(1)} KB` : '')}
              </Text>
              {file.status === 'uploading' && <Text type="secondary">{t('common.uploading')}...</Text>}
            </Space>
          }
        />
      </List.Item>
    );
  };

  // 处理文件选择
  const handleFileSelect = (info: any) => {
    console.log('文件选择事件:', info);

    // 获取文件对象
    const file = info.file.originFileObj || info.file;

    if (file && file instanceof File) {
      console.log('选择的文件:', file.name, file.type, file.size);

      // 显示上传中状态
      message.loading({
        content: `${t('databases.equipment.attachments.uploading')}: ${file.name}`,
        key: 'uploading',
        duration: 0
      });

      // 上传文件
      handleUpload(file).then(() => {
        // 上传成功后关闭提示
        message.success({
          content: t('databases.equipment.attachments.uploadSuccess'),
          key: 'uploading',
          duration: 2
        });
      }).catch((error) => {
        // 上传失败显示错误
        message.error({
          content: `${t('databases.equipment.attachments.uploadError')}: ${error.message || '未知错误'}`,
          key: 'uploading',
          duration: 4
        });
        console.error('上传文件失败:', error);
      });
    } else {
      console.warn('无效的文件对象:', info.file);
    }
  };

  return (
    <div className="attachment-upload">
      <Upload
        beforeUpload={(file) => {
          console.log('beforeUpload被调用，文件:', file.name);
          // 验证文件类型和大小
          const isValidType = file.type.startsWith('image/') ||
                             file.type === 'application/pdf' ||
                             file.type === 'application/msword' ||
                             file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                             file.type === 'application/vnd.ms-excel' ||
                             file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                             file.name.endsWith('.csv');

          const isLt50M = file.size / 1024 / 1024 < 50;

          if (!isValidType) {
            message.error(t('databases.equipment.attachments.invalidFileType'));
            return Upload.LIST_IGNORE;
          }

          if (!isLt50M) {
            message.error(t('databases.equipment.attachments.fileTooLarge'));
            return Upload.LIST_IGNORE;
          }

          return false; // 阻止默认上传行为，由我们自己处理上传
        }}
        onChange={handleFileSelect}
        listType="text"
        fileList={[]} // 不显示默认上传列表，我们使用自定义列表
        maxCount={maxCount}
        multiple={false}
        showUploadList={false}
        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.csv"
      >
        {fileList.length >= maxCount ? null : uploadButton}
      </Upload>

      {fileList.length > 0 && (
        <List
          className="attachment-list"
          itemLayout="horizontal"
          dataSource={fileList}
          renderItem={renderItem}
          style={{ marginTop: 16 }}
        />
      )}

      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={[
          <Button key="download" icon={<DownloadOutlined />} onClick={() => {
            // 查找对应的附件对象
            const attachment = fileList.find(f => f.name === previewTitle)?.response as EquipmentAttachment;
            if (attachment) {
              downloadAttachment(attachment)
                .then(() => message.success(t('databases.equipment.attachments.downloadSuccess')))
                .catch(() => message.error(t('databases.equipment.attachments.downloadError')));
            } else {
              // 如果找不到附件对象，使用传统方式下载
              const a = document.createElement('a');
              a.href = previewImage;
              a.download = previewTitle;
              a.target = '_blank';
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }
          }}>
            {t('common.download')}
          </Button>,
          <Button key="close" type="primary" onClick={() => setPreviewOpen(false)}>
            {t('common.close')}
          </Button>
        ]}
        onCancel={() => setPreviewOpen(false)}
        width={800}
      >
        {previewImage && (
          previewTitle.toLowerCase().endsWith('.pdf') ? (
            <iframe
              src={previewImage}
              width="100%"
              height="500px"
              title={previewTitle}
              style={{ border: 'none' }}
              sandbox="allow-same-origin allow-scripts allow-forms"
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <img
                alt={previewTitle}
                style={{
                  maxWidth: '100%',
                  maxHeight: '70vh',
                  objectFit: 'contain',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                }}
                src={previewImage}
                onError={(e) => {
                  console.error('图片加载失败:', previewImage);
                  message.error(t('databases.equipment.attachments.previewError'));

                  // 尝试使用缓存破坏技术重新加载图片
                  const timestamp = new Date().getTime();
                  const newSrc = previewImage.includes('?')
                    ? `${previewImage}&_t=${timestamp}`
                    : `${previewImage}?_t=${timestamp}`;

                  console.log('尝试使用缓存破坏技术重新加载图片:', newSrc);
                  (e.target as HTMLImageElement).src = newSrc;

                  // 如果再次失败，显示错误图标
                  (e.target as HTMLImageElement).onerror = () => {
                    console.error('图片二次加载失败');
                    (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItYWxlcnQtdHJpYW5nbGUiPjxwYXRoIGQ9Ik0xMC4yOSAzLjg2TDEuODIgMThhMiAyIDAgMCAwIDEuNzEgM2gxNi45NGEyIDIgMCAwIDAgMS43MS0zTDEzLjcxIDMuODZhMiAyIDAgMCAwLTMuNDIgMHoiPjwvcGF0aD48bGluZSB4MT0iMTIiIHkxPSI5IiB4Mj0iMTIiIHkyPSIxMyI+PC9saW5lPjxsaW5lIHgxPSIxMiIgeTE9IjE3IiB4Mj0iMTIuMDEiIHkyPSIxNyI+PC9saW5lPjwvc3ZnPg==';
                    (e.target as HTMLImageElement).style.width = '64px';
                    (e.target as HTMLImageElement).style.height = '64px';
                  };
                }}
              />
            </div>
          )
        )}
      </Modal>

      <Modal
        title={t('common.confirm')}
        open={confirmVisible}
        onOk={confirmDelete}
        onCancel={() => setConfirmVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <p>{t('databases.equipment.attachments.deleteConfirm')}</p>
      </Modal>
    </div>
  );
};

export default AttachmentUpload;
