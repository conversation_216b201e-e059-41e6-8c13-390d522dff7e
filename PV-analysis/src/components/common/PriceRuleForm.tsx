import React, { useState, useEffect } from 'react';
import { Form, Input, Button, TimePicker, Select, Space, InputNumber, Table, Tag } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import TimeRangeSelector from './TimeRangeSelector';

const { Option } = Select;

// 电价规则类型
export interface PriceRule {
  key?: string;
  startTime: string; // 格式：HH:MM
  endTime: string; // 格式：HH:MM
  price: number; // 用电价格，单位：JPY/kWh
  gridFeedInPrice?: number; // 光伏上网电价，单位：JPY/kWh
  type: 'peak' | 'normal' | 'valley' | 'super-peak'; // 峰时、平时、谷时、尖峰
}

interface PriceRuleFormProps {
  value?: PriceRule[];
  onChange?: (rules: PriceRule[]) => void;
}

/**
 * 电价规则表单组件
 * 用于添加、编辑、删除电价规则
 */
const PriceRuleForm: React.FC<PriceRuleFormProps> = ({ value = [], onChange }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [rules, setRules] = useState<PriceRule[]>(value);
  const [editingKey, setEditingKey] = useState<string>('');
  const [nextKey, setNextKey] = useState<number>(1);

  // 当外部value变化时更新内部状态
  useEffect(() => {
    const rulesWithKeys = value.map((rule, index) => ({
      ...rule,
      key: rule.key || `rule-${index + 1}`,
    }));
    setRules(rulesWithKeys);
    setNextKey(rulesWithKeys.length + 1);
  }, [value]);

  // 处理规则变化
  const handleRulesChange = (newRules: PriceRule[]) => {
    setRules(newRules);
    if (onChange) {
      // 移除key属性，因为它只是用于表格渲染
      const rulesWithoutKeys = newRules.map(({ key, ...rest }) => rest);
      onChange(rulesWithoutKeys);
    }
  };

  // 处理时间范围选择
  const handleRangeSelected = (startHour: number, endHour: number) => {
    // 格式化时间
    const formatHour = (hour: number) => {
      const h = Math.floor(hour) % 24;
      return h < 10 ? `0${h}:00` : `${h}:00`;
    };

    console.log(`选择的时间范围: ${startHour}:00 - ${endHour}:00`);

    // 设置表单字段值
    form.setFieldsValue({
      startTime: dayjs(formatHour(startHour), 'HH:mm'),
      endTime: dayjs(formatHour(endHour), 'HH:mm')
    });
  };

  // 检查时间段是否重叠
  const checkTimeOverlap = (startTime: string, endTime: string): PriceRule[] => {
    // 解析时间
    const parseTime = (timeStr: string) => {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours + minutes / 60;
    };

    const start = parseTime(startTime);
    const end = parseTime(endTime);

    // 处理跨天的情况
    const normalizedEnd = end <= start ? end + 24 : end;

    // 找出所有与新规则重叠的规则
    const overlappingRules: PriceRule[] = [];
    const nonOverlappingRules: PriceRule[] = [];

    rules.forEach(rule => {
      const ruleStart = parseTime(rule.startTime);
      let ruleEnd = parseTime(rule.endTime);

      // 处理跨天的情况
      if (ruleEnd <= ruleStart) {
        ruleEnd += 24;
      }

      // 检查是否重叠
      if (
        (start < ruleEnd && normalizedEnd > ruleStart) || // 部分重叠
        (start <= ruleStart && normalizedEnd >= ruleEnd) // 完全包含
      ) {
        overlappingRules.push(rule);
      } else {
        nonOverlappingRules.push(rule);
      }
    });

    return nonOverlappingRules;
  };

  // 添加规则
  const handleAddRule = () => {
    form.validateFields().then(values => {
      const startTime = values.startTime.format('HH:mm');
      const endTime = values.endTime.format('HH:mm');

      // 检查时间重叠，获取不重叠的规则
      const nonOverlappingRules = checkTimeOverlap(startTime, endTime);

      // 创建新规则
      const newRule: PriceRule = {
        key: `rule-${nextKey}`,
        startTime,
        endTime,
        price: values.price,
        gridFeedInPrice: values.gridFeedInPrice,
        type: values.type,
      };

      // 添加新规则到不重叠的规则列表中
      const newRules = [...nonOverlappingRules, newRule];

      // 更新规则列表
      handleRulesChange(newRules);
      setNextKey(nextKey + 1);
      form.resetFields();
    });
  };

  // 编辑规则
  const handleEditRule = (record: PriceRule) => {
    form.setFieldsValue({
      startTime: dayjs(record.startTime, 'HH:mm'),
      endTime: dayjs(record.endTime, 'HH:mm'),
      price: record.price,
      gridFeedInPrice: record.gridFeedInPrice,
      type: record.type,
    });
    setEditingKey(record.key || '');
  };

  // 更新规则
  const handleUpdateRule = () => {
    form.validateFields().then(values => {
      const startTime = values.startTime.format('HH:mm');
      const endTime = values.endTime.format('HH:mm');

      // 获取当前编辑的规则
      const currentRule = rules.find(rule => rule.key === editingKey);
      if (!currentRule) return;

      // 移除当前编辑的规则
      const rulesWithoutCurrent = rules.filter(rule => rule.key !== editingKey);

      // 检查时间重叠，获取不重叠的规则
      const nonOverlappingRules = checkTimeOverlap(startTime, endTime);

      // 创建更新后的规则
      const updatedRule = {
        ...currentRule,
        startTime,
        endTime,
        price: values.price,
        gridFeedInPrice: values.gridFeedInPrice,
        type: values.type,
      };

      // 添加更新后的规则到不重叠的规则列表中
      const updatedRules = [...nonOverlappingRules, updatedRule];

      handleRulesChange(updatedRules);
      setEditingKey('');
      form.resetFields();
    });
  };

  // 删除规则
  const handleDeleteRule = (key: string) => {
    const updatedRules = rules.filter(rule => rule.key !== key);
    handleRulesChange(updatedRules);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingKey('');
    form.resetFields();
  };

  // 表格列定义
  const columns = [
    {
      title: t('electricityPrice.startTime'),
      dataIndex: 'startTime',
      key: 'startTime',
    },
    {
      title: t('electricityPrice.endTime'),
      dataIndex: 'endTime',
      key: 'endTime',
    },
    {
      title: t('electricityPrice.price'),
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `${price.toFixed(1)} ${t('common.currency')}`,
    },
    {
      title: t('electricityPrice.gridFeedInPrice'),
      dataIndex: 'gridFeedInPrice',
      key: 'gridFeedInPrice',
      render: (price?: number) => price ? `${price.toFixed(1)} ${t('common.currency')}` : '-',
    },
    {
      title: t('electricityPrice.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: 'peak' | 'normal' | 'valley' | 'super-peak') => {
        let color = '';
        let text = '';

        switch (type) {
          case 'super-peak':
            color = 'orange';
            text = t('electricityPrice.superPeak', '尖峰');
            break;
          case 'peak':
            color = 'red';
            text = t('electricityPrice.peak');
            break;
          case 'normal':
            color = 'blue';
            text = t('electricityPrice.normal');
            break;
          case 'valley':
            color = 'green';
            text = t('electricityPrice.valley');
            break;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: PriceRule) => (
        <Space size="small">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditRule(record)}
            disabled={editingKey !== ''}
          />
          <Button
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDeleteRule(record.key || '')}
            disabled={editingKey !== ''}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Table
        rowKey="key"
        columns={columns}
        dataSource={rules}
        pagination={false}
        size="small"
        style={{ marginBottom: 16 }}
        locale={{ emptyText: <div style={{ padding: '20px 0' }}>{t('electricityPrice.noRules')}</div> }}
      />

      {/* 时间范围选择器 */}
      <TimeRangeSelector
        rules={rules}
        onRangeSelected={handleRangeSelected}
      />

      <div style={{ border: '1px solid #f0f0f0', padding: '16px', borderRadius: '4px', backgroundColor: '#fafafa' }}>
        <Form
          form={form}
          layout="horizontal"
          style={{ marginBottom: 16 }}
        >
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
            <Form.Item
              name="startTime"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.startTime')}</span>}
              rules={[{ required: true, message: t('electricityPrice.startTimeRequired') }]}
            >
              <TimePicker
                format="HH:mm"
                placeholder={t('electricityPrice.selectTime', '请选择时间')}
                style={{ width: '160px' }}
              />
            </Form.Item>

            <Form.Item
              name="endTime"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.endTime')}</span>}
              rules={[{ required: true, message: t('electricityPrice.endTimeRequired') }]}
            >
              <TimePicker
                format="HH:mm"
                placeholder={t('electricityPrice.selectTime', '请选择时间')}
                style={{ width: '160px' }}
              />
            </Form.Item>

            <Form.Item
              name="price"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.price')}</span>}
              rules={[{ required: true, message: t('electricityPrice.priceRequired') }]}
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                placeholder="0.0"
                addonAfter={t('common.currency')}
                style={{ width: 160 }}
              />
            </Form.Item>

            <Form.Item
              name="gridFeedInPrice"
              label={t('electricityPrice.gridFeedInPrice')}
            >
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                placeholder="0.0"
                addonAfter={t('common.currency')}
                style={{ width: 160 }}
              />
            </Form.Item>

            <Form.Item
              name="type"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.type')}</span>}
              rules={[{ required: true, message: t('electricityPrice.typeRequired') }]}
            >
              <Select
                style={{ width: 160 }}
                placeholder={t('electricityPrice.selectType', '请选择类型')}
              >
                <Option value="super-peak">{t('electricityPrice.superPeak', '尖峰')}</Option>
                <Option value="peak">{t('electricityPrice.peak')}</Option>
                <Option value="normal">{t('electricityPrice.normal')}</Option>
                <Option value="valley">{t('electricityPrice.valley')}</Option>
              </Select>
            </Form.Item>

            <Form.Item style={{ marginLeft: 'auto' }}>
              {editingKey === '' ? (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddRule}
                  style={{ backgroundColor: '#1677ff', borderColor: '#1677ff' }}
                >
                  {t('common.add')}
                </Button>
              ) : (
                <Space>
                  <Button
                    type="primary"
                    onClick={handleUpdateRule}
                  >
                    {t('common.update')}
                  </Button>
                  <Button
                    onClick={handleCancelEdit}
                  >
                    {t('common.cancel')}
                  </Button>
                </Space>
              )}
            </Form.Item>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default PriceRuleForm;
