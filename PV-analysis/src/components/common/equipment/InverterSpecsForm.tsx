import React from 'react';
import { Form, InputNumber, Tooltip, Row, Col, Divider } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { InverterSpecs } from '../../../types/database';

interface InverterSpecsFormProps {
  form?: any; // 设为可选参数
  initialValues?: InverterSpecs;
}

/**
 * 逆变器规格表单组件
 */
const InverterSpecsForm: React.FC<InverterSpecsFormProps> = ({
  initialValues
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'power']}
            label={
              <span>
                {t('databases.equipment.inverterSpecs.power')}
                <Tooltip title={t('databases.equipment.inverterSpecs.powerTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.inverterSpecs.powerRequired') }]}
            initialValue={initialValues?.power}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={1}
              addonAfter="kW"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['specs', 'efficiency']}
            label={
              <span>
                {t('databases.equipment.inverterSpecs.efficiency')}
                <Tooltip title={t('databases.equipment.inverterSpecs.efficiencyTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.inverterSpecs.efficiencyRequired') }]}
            initialValue={initialValues?.efficiency}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={100}
              precision={1}
              addonAfter="%"
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label={t('databases.equipment.inverterSpecs.mpptRange')}>
        <Form.Item
          name={['specs', 'mpptRange', 'min']}
          label={t('databases.equipment.inverterSpecs.minVoltage')}
          rules={[{ required: true, message: t('databases.equipment.inverterSpecs.minVoltageRequired') }]}
          initialValue={initialValues?.mpptRange?.min}
          style={{ display: 'inline-block', width: 'calc(50% - 12px)' }}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            precision={1}
            addonAfter="V"
          />
        </Form.Item>
        <span style={{ display: 'inline-block', width: '24px', textAlign: 'center' }}>-</span>
        <Form.Item
          name={['specs', 'mpptRange', 'max']}
          label={t('databases.equipment.inverterSpecs.maxVoltage')}
          rules={[{ required: true, message: t('databases.equipment.inverterSpecs.maxVoltageRequired') }]}
          initialValue={initialValues?.mpptRange?.max}
          style={{ display: 'inline-block', width: 'calc(50% - 12px)' }}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            precision={1}
            addonAfter="V"
          />
        </Form.Item>
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'warranty']}
            label={t('databases.equipment.inverterSpecs.warranty')}
            rules={[{ required: true, message: t('databases.equipment.inverterSpecs.warrantyRequired') }]}
            initialValue={initialValues?.warranty}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter={t('databases.equipment.inverterSpecs.years')}
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('databases.equipment.inverterSpecs.dimensions')}</Divider>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            name={['specs', 'length']}
            label={
              <span>
                {t('databases.equipment.inverterSpecs.length')}
                <Tooltip title={t('databases.equipment.inverterSpecs.lengthTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.length}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name={['specs', 'width']}
            label={
              <span>
                {t('databases.equipment.inverterSpecs.width')}
                <Tooltip title={t('databases.equipment.inverterSpecs.widthTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.width}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name={['specs', 'height']}
            label={
              <span>
                {t('databases.equipment.inverterSpecs.height')}
                <Tooltip title={t('databases.equipment.inverterSpecs.heightTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.height}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default InverterSpecsForm;
