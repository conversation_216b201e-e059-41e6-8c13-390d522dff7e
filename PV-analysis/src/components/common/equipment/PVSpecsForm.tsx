import React from 'react';
import { Form, InputNumber, Tooltip, Row, Col, Divider } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { PVSpecs } from '../../../types/database';

interface PVSpecsFormProps {
  form?: any; // 设为可选参数
  initialValues?: PVSpecs;
}

/**
 * 光伏设备规格表单组件
 */
const PVSpecsForm: React.FC<PVSpecsFormProps> = ({
  initialValues
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'power']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.power')}
                <Tooltip title={t('databases.equipment.pvSpecs.powerTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.pvSpecs.powerRequired') }]}
            initialValue={initialValues?.power}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={1}
              addonAfter="W"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['specs', 'efficiency']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.efficiency')}
                <Tooltip title={t('databases.equipment.pvSpecs.efficiencyTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.pvSpecs.efficiencyRequired') }]}
            initialValue={initialValues?.efficiency}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={100}
              precision={1}
              addonAfter="%"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'area']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.area')}
                <Tooltip title={t('databases.equipment.pvSpecs.areaTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.pvSpecs.areaRequired') }]}
            initialValue={initialValues?.area}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              addonAfter="m²"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['specs', 'warranty']}
            label={t('databases.equipment.pvSpecs.warranty')}
            rules={[{ required: true, message: t('databases.equipment.pvSpecs.warrantyRequired') }]}
            initialValue={initialValues?.warranty}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter={t('databases.equipment.pvSpecs.years')}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'degradation']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.degradation')}
                <Tooltip title={t('databases.equipment.pvSpecs.degradationTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.pvSpecs.degradationRequired') }]}
            initialValue={initialValues?.degradation}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={100}
              precision={2}
              addonAfter="%"
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('databases.equipment.pvSpecs.dimensions')}</Divider>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            name={['specs', 'length']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.length')}
                <Tooltip title={t('databases.equipment.pvSpecs.lengthTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.length}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name={['specs', 'width']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.width')}
                <Tooltip title={t('databases.equipment.pvSpecs.widthTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.width}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name={['specs', 'height']}
            label={
              <span>
                {t('databases.equipment.pvSpecs.height')}
                <Tooltip title={t('databases.equipment.pvSpecs.heightTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.height}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default PVSpecsForm;
