import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Space, Card, Tabs, Tag, Divider, InputNumber, Table, Row, Col } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';

const { Option } = Select;
const { TabPane } = Tabs;

// 用电模式类型
export interface UsagePattern {
  id: string;
  name: string;
  type: 'weekday' | 'month'; // 适用于星期几或月份
  applicableDays?: number[]; // 适用的星期几，0-6表示周一到周日
  applicableMonths?: number[]; // 适用的月份，1-12表示一月到十二月
  hourlyData: Array<{ hour: number; value: number }>; // 24小时用电量数据
}

interface UsagePatternFormProps {
  value?: UsagePattern[];
  onChange?: (patterns: UsagePattern[]) => void;
  patternType: 'weekday' | 'month'; // 模式类型：按星期几或按月份
}

/**
 * 用电模式表单组件
 * 用于添加、编辑、删除用电模式
 */
const UsagePatternForm: React.FC<UsagePatternFormProps> = ({ value = [], onChange, patternType }) => {
  const { t } = useTranslation();
  const [patterns, setPatterns] = useState<UsagePattern[]>(value);
  const [activeKey, setActiveKey] = useState<string>('');
  const [editMode, setEditMode] = useState<boolean>(false);
  const [showCompactTable, setShowCompactTable] = useState<boolean>(false);
  const [form] = Form.useForm();

  // 当外部value变化时更新内部状态
  useEffect(() => {
    const patternsWithIds = value.map(pattern => ({
      ...pattern,
      id: pattern.id || uuidv4(),
    }));
    setPatterns(patternsWithIds);
    if (patternsWithIds.length > 0 && !activeKey) {
      setActiveKey(patternsWithIds[0].id);
    }
  }, [value]);

  // 添加新的用电模式
  const handleAddPattern = () => {
    form.resetFields();

    // 创建初始24小时用电量数据
    const initialHourlyData = Array(24).fill(0).map((_, hour) => ({ hour, value: 0 }));

    const newPattern: UsagePattern = {
      id: uuidv4(),
      name: '',
      type: patternType,
      applicableDays: patternType === 'weekday' ? [] : undefined,
      applicableMonths: patternType === 'month' ? [] : undefined,
      hourlyData: initialHourlyData,
    };

    const newPatterns = [...patterns, newPattern];
    setPatterns(newPatterns);
    setActiveKey(newPattern.id);
    setEditMode(true);

    if (onChange) {
      onChange(newPatterns);
    }
  };

  // 删除用电模式
  const handleDeletePattern = (patternId: string) => {
    const newPatterns = patterns.filter(pattern => pattern.id !== patternId);
    setPatterns(newPatterns);

    if (newPatterns.length > 0) {
      setActiveKey(newPatterns[0].id);
    } else {
      setActiveKey('');
    }

    if (onChange) {
      onChange(newPatterns);
    }
  };

  // 编辑用电模式
  const handleEditPattern = (pattern: UsagePattern) => {
    form.setFieldsValue({
      name: pattern.name,
      applicableDays: pattern.applicableDays,
      applicableMonths: pattern.applicableMonths,
      hourlyData: pattern.hourlyData,
    });
    setActiveKey(pattern.id);
    setEditMode(true);
  };

  // 保存用电模式
  const handleSavePattern = async () => {
    try {
      const values = await form.validateFields();
      const updatedPatterns = patterns.map(pattern => {
        if (pattern.id === activeKey) {
          return {
            ...pattern,
            name: values.name,
            applicableDays: patternType === 'weekday' ? values.applicableDays : undefined,
            applicableMonths: patternType === 'month' ? values.applicableMonths : undefined,
            hourlyData: values.hourlyData || pattern.hourlyData,
          };
        }
        return pattern;
      });

      setPatterns(updatedPatterns);
      setEditMode(false);

      if (onChange) {
        onChange(updatedPatterns);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditMode(false);
    form.resetFields();
  };

  // 切换标签页
  const handleTabChange = (key: string) => {
    if (editMode) {
      // 如果正在编辑，提示保存
      if (window.confirm(t('electricityUsage.saveChangesPrompt'))) {
        handleSavePattern();
      } else {
        handleCancelEdit();
      }
    }

    setActiveKey(key);
    const pattern = patterns.find(p => p.id === key);
    if (pattern) {
      form.setFieldsValue({
        name: pattern.name,
        applicableDays: pattern.applicableDays,
        applicableMonths: pattern.applicableMonths,
        hourlyData: pattern.hourlyData,
      });
    }
  };

  // 处理用电量数据变更
  const handleHourlyDataChange = (hour: number, value: number | null) => {
    // 只在编辑模式下更新数据
    if (!editMode) return;

    const currentPattern = patterns.find(p => p.id === activeKey);
    if (!currentPattern) return;

    const updatedHourlyData = [...currentPattern.hourlyData];
    const hourIndex = updatedHourlyData.findIndex(item => item.hour === hour);

    if (hourIndex !== -1) {
      updatedHourlyData[hourIndex] = { hour, value: value || 0 };
    } else {
      updatedHourlyData.push({ hour, value: value || 0 });
    }

    // 更新表单值
    form.setFieldsValue({ hourlyData: updatedHourlyData });

    // 更新模式数据
    const updatedPatterns = patterns.map(pattern => {
      if (pattern.id === activeKey) {
        return {
          ...pattern,
          hourlyData: updatedHourlyData,
        };
      }
      return pattern;
    });

    setPatterns(updatedPatterns);

    // 通知父组件数据变更
    if (onChange) {
      onChange(updatedPatterns);
    }
  };

  // 获取已选择的日期或月份
  const getSelectedDaysOrMonths = () => {
    const selectedItems: number[] = [];

    patterns.forEach(pattern => {
      if (pattern.id !== activeKey) {
        if (patternType === 'weekday' && pattern.applicableDays) {
          selectedItems.push(...pattern.applicableDays);
        } else if (patternType === 'month' && pattern.applicableMonths) {
          selectedItems.push(...pattern.applicableMonths);
        }
      }
    });

    return selectedItems;
  };

  // 渲染星期几选择器
  const renderDaySelector = () => {
    const days = [
      { value: 0, label: t('electricityUsage.monday') },
      { value: 1, label: t('electricityUsage.tuesday') },
      { value: 2, label: t('electricityUsage.wednesday') },
      { value: 3, label: t('electricityUsage.thursday') },
      { value: 4, label: t('electricityUsage.friday') },
      { value: 5, label: t('electricityUsage.saturday') },
      { value: 6, label: t('electricityUsage.sunday') },
    ];

    // 获取已被其他模式选择的星期几
    const selectedDays = getSelectedDaysOrMonths();

    return (
      <Form.Item
        name="applicableDays"
        label={<span style={{ color: '#ff4d4f' }}>* {t('electricityUsage.selectDayOfWeek')}</span>}
        rules={[{ required: true, message: t('electricityUsage.daysRequired') }]}
      >
        <Select
          mode="multiple"
          placeholder={t('electricityUsage.selectDayOfWeek')}
          style={{ width: '100%' }}
        >
          {days.map(day => (
            <Option
              key={day.value}
              value={day.value}
              disabled={selectedDays.includes(day.value)}
            >
              {day.label}
            </Option>
          ))}
        </Select>
      </Form.Item>
    );
  };

  // 渲染月份选择器
  const renderMonthSelector = () => {
    const months = [
      { value: 1, label: t('common.january') },
      { value: 2, label: t('common.february') },
      { value: 3, label: t('common.march') },
      { value: 4, label: t('common.april') },
      { value: 5, label: t('common.may') },
      { value: 6, label: t('common.june') },
      { value: 7, label: t('common.july') },
      { value: 8, label: t('common.august') },
      { value: 9, label: t('common.september') },
      { value: 10, label: t('common.october') },
      { value: 11, label: t('common.november') },
      { value: 12, label: t('common.december') },
    ];

    // 获取已被其他模式选择的月份
    const selectedMonths = getSelectedDaysOrMonths();

    return (
      <Form.Item
        name="applicableMonths"
        label={<span style={{ color: '#ff4d4f' }}>* {t('electricityUsage.selectMonth')}</span>}
        rules={[{ required: true, message: t('electricityUsage.monthsRequired') }]}
      >
        <Select
          mode="multiple"
          placeholder={t('electricityUsage.selectMonth')}
          style={{ width: '100%' }}
        >
          {months.map(month => (
            <Option
              key={month.value}
              value={month.value}
              disabled={selectedMonths.includes(month.value)}
            >
              {month.label}
            </Option>
          ))}
        </Select>
      </Form.Item>
    );
  };

  // 渲染24小时用电量输入表格
  const renderHourlyDataTable = (hourlyData: Array<{ hour: number; value: number }>) => {
    // 将24小时数据分为三列
    const firstColumn = hourlyData.filter(item => item.hour < 8);
    const secondColumn = hourlyData.filter(item => item.hour >= 8 && item.hour < 16);
    const thirdColumn = hourlyData.filter(item => item.hour >= 16);

    // 表格列定义
    const columns = [
      {
        title: t('electricityUsage.hour'),
        dataIndex: 'hour',
        key: 'hour',
        width: 80,
        render: (hour: number) => `${hour}:00`,
      },
      {
        title: t('electricityUsage.value') + ' (kWh)',
        dataIndex: 'value',
        key: 'value',
        render: (value: number, record: any) => (
          editMode ? (
            <InputNumber
              min={0}
              precision={1}
              value={value}
              step={0.1}
              stringMode={true}
              onChange={(newValue) => {
                console.log("Pattern InputNumber onChange:", record.hour, newValue);
                if (newValue !== null) {
                  const numValue = parseFloat(newValue.toString());
                  if (!isNaN(numValue)) {
                    handleHourlyDataChange(record.hour, numValue);
                  }
                } else {
                  handleHourlyDataChange(record.hour, 0);
                }
              }}
              onBlur={(e) => {
                console.log("Pattern InputNumber onBlur:", record.hour, e.target.value);
                // 确保在失去焦点时也更新数据
                const numValue = parseFloat(e.target.value);
                if (!isNaN(numValue)) {
                  handleHourlyDataChange(record.hour, numValue);
                }
              }}
              onPressEnter={(e) => {
                console.log("Pattern InputNumber onPressEnter:", record.hour, e.target.value);
                const numValue = parseFloat(e.target.value);
                if (!isNaN(numValue)) {
                  handleHourlyDataChange(record.hour, numValue);
                }
              }}
              style={{ width: '100%' }}
            />
          ) : value
        ),
      },
    ];

    // 检查数据是否完备
    const isDataComplete = hourlyData.every(item => item.value !== undefined && item.value !== null);

    // 渲染紧凑表格
    if (showCompactTable) {
      return renderCompactTable(hourlyData);
    }

    return (
      <>
        <Row gutter={16}>
          <Col span={8}>
            <Table
              dataSource={firstColumn}
              columns={columns}
              pagination={false}
              size="small"
              bordered
              rowKey="hour"
            />
          </Col>
          <Col span={8}>
            <Table
              dataSource={secondColumn}
              columns={columns}
              pagination={false}
              size="small"
              bordered
              rowKey="hour"
            />
          </Col>
          <Col span={8}>
            <Table
              dataSource={thirdColumn}
              columns={columns}
              pagination={false}
              size="small"
              bordered
              rowKey="hour"
            />
          </Col>
        </Row>
        <div style={{ textAlign: 'right', marginTop: 16 }}>
          <Button
            type="primary"
            onClick={() => setShowCompactTable(true)}
            disabled={!isDataComplete}
          >
            {t('electricityUsage.completeInput')}
          </Button>
        </div>
      </>
    );
  };

  // 渲染紧凑表格
  const renderCompactTable = (hourlyData: Array<{ hour: number; value: number }>) => {
    // 将数据重新组织为表格
    const hours = Array(8).fill(0).map((_, i) => i);
    const hours2 = Array(8).fill(0).map((_, i) => i + 8);
    const hours3 = Array(8).fill(0).map((_, i) => i + 16);

    const values1 = hours.map(hour => {
      const item = hourlyData.find(d => d.hour === hour);
      return item ? item.value : 0;
    });

    const values2 = hours2.map(hour => {
      const item = hourlyData.find(d => d.hour === hour);
      return item ? item.value : 0;
    });

    const values3 = hours3.map(hour => {
      const item = hourlyData.find(d => d.hour === hour);
      return item ? item.value : 0;
    });

    return (
      <div style={{ marginTop: 16, marginBottom: 16 }}>
        <Table
          size="small"
          pagination={false}
          bordered
          columns={[
            { title: '', dataIndex: 'hour0', key: 'hour0' },
            { title: '0:00', dataIndex: 'h0', key: 'h0' },
            { title: '1:00', dataIndex: 'h1', key: 'h1' },
            { title: '2:00', dataIndex: 'h2', key: 'h2' },
            { title: '3:00', dataIndex: 'h3', key: 'h3' },
            { title: '4:00', dataIndex: 'h4', key: 'h4' },
            { title: '5:00', dataIndex: 'h5', key: 'h5' },
            { title: '6:00', dataIndex: 'h6', key: 'h6' },
            { title: '7:00', dataIndex: 'h7', key: 'h7' },
          ]}
          dataSource={[
            {
              key: 'values1',
              hour0: t('electricityUsage.value') + ' (kWh)',
              h0: values1[0], h1: values1[1], h2: values1[2], h3: values1[3],
              h4: values1[4], h5: values1[5], h6: values1[6], h7: values1[7]
            },
            {
              key: 'hours2',
              hour0: '',
              h0: '8:00', h1: '9:00', h2: '10:00', h3: '11:00',
              h4: '12:00', h5: '13:00', h6: '14:00', h7: '15:00'
            },
            {
              key: 'values2',
              hour0: t('electricityUsage.value') + ' (kWh)',
              h0: values2[0], h1: values2[1], h2: values2[2], h3: values2[3],
              h4: values2[4], h5: values2[5], h6: values2[6], h7: values2[7]
            },
            {
              key: 'hours3',
              hour0: '',
              h0: '16:00', h1: '17:00', h2: '18:00', h3: '19:00',
              h4: '20:00', h5: '21:00', h6: '22:00', h7: '23:00'
            },
            {
              key: 'values3',
              hour0: t('electricityUsage.value') + ' (kWh)',
              h0: values3[0], h1: values3[1], h2: values3[2], h3: values3[3],
              h4: values3[4], h5: values3[5], h6: values3[6], h7: values3[7]
            },
          ]}
        />
        {editMode && (
          <Button
            type="primary"
            onClick={() => setShowCompactTable(false)}
            style={{ marginTop: 16 }}
          >
            {t('electricityUsage.editData')}
          </Button>
        )}
      </div>
    );
  };

  // 渲染标签页
  const renderTabs = () => {
    return (
      <Tabs
        type="card"
        activeKey={activeKey}
        onChange={handleTabChange}
        tabBarExtraContent={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddPattern}
          >
            {patternType === 'weekday'
              ? t('electricityUsage.addDayPattern')
              : t('electricityUsage.addMonthPattern')}
          </Button>
        }
      >
        {patterns.map(pattern => (
          <TabPane
            tab={
              <span>
                {pattern.name || (patternType === 'weekday'
                  ? t('electricityUsage.newDayPattern')
                  : t('electricityUsage.newMonthPattern'))}
                {patternType === 'weekday' && pattern.applicableDays && pattern.applicableDays.length > 0 && (
                  <Tag color="blue" style={{ marginLeft: 8 }}>
                    {pattern.applicableDays.length} {t('electricityUsage.days')}
                  </Tag>
                )}
                {patternType === 'month' && pattern.applicableMonths && pattern.applicableMonths.length > 0 && (
                  <Tag color="blue" style={{ marginLeft: 8 }}>
                    {pattern.applicableMonths.length} {t('electricityUsage.months')}
                  </Tag>
                )}
              </span>
            }
            key={pattern.id}
          >
            {activeKey === pattern.id && renderPatternForm(pattern)}
          </TabPane>
        ))}
      </Tabs>
    );
  };

  // 当切换到查看模式时，设置为紧凑表格状态
  useEffect(() => {
    if (!editMode) {
      setShowCompactTable(true);
    }
  }, [editMode]);

  // 渲染模式表单
  const renderPatternForm = (pattern: UsagePattern) => {

    return (
      <div>
        {editMode ? (
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              name: pattern.name,
              applicableDays: pattern.applicableDays,
              applicableMonths: pattern.applicableMonths,
              hourlyData: pattern.hourlyData,
            }}
          >
            <Form.Item
              name="name"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityUsage.patternName')}</span>}
              rules={[{ required: true, message: t('electricityUsage.patternNameRequired') }]}
            >
              <Input placeholder={t('electricityUsage.patternNamePlaceholder')} />
            </Form.Item>

            {patternType === 'weekday' ? renderDaySelector() : renderMonthSelector()}

            <Form.Item
              name="hourlyData"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityUsage.hourlyData')}</span>}
              rules={[{ required: true, message: t('electricityUsage.dataRequired') }]}
            >
              {renderHourlyDataTable(pattern.hourlyData)}
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" icon={<SaveOutlined />} onClick={handleSavePattern}>
                  {t('common.save')}
                </Button>
                <Button onClick={handleCancelEdit}>
                  {t('common.cancel')}
                </Button>
                <Button danger icon={<DeleteOutlined />} onClick={() => handleDeletePattern(pattern.id)}>
                  {t('common.delete')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        ) : (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
              <h3>{pattern.name}</h3>
              <Button icon={<EditOutlined />} onClick={() => handleEditPattern(pattern)}>
                {t('common.edit')}
              </Button>
            </div>

            <Divider orientation="left">
              {patternType === 'weekday'
                ? t('electricityUsage.applicableDays')
                : t('electricityUsage.applicableMonths')}
            </Divider>

            <div style={{ marginBottom: 16 }}>
              {patternType === 'weekday' && pattern.applicableDays && pattern.applicableDays.length > 0 ? (
                <Space wrap>
                  {pattern.applicableDays.map(day => {
                    const dayNames = [
                      t('electricityUsage.monday'),
                      t('electricityUsage.tuesday'),
                      t('electricityUsage.wednesday'),
                      t('electricityUsage.thursday'),
                      t('electricityUsage.friday'),
                      t('electricityUsage.saturday'),
                      t('electricityUsage.sunday'),
                    ];
                    return (
                      <Tag key={day} color="blue">
                        {dayNames[day]}
                      </Tag>
                    );
                  })}
                </Space>
              ) : patternType === 'month' && pattern.applicableMonths && pattern.applicableMonths.length > 0 ? (
                <Space wrap>
                  {pattern.applicableMonths.map(month => (
                    <Tag key={month} color="blue">
                      {t(`common.month${month}`)}
                    </Tag>
                  ))}
                </Space>
              ) : (
                <span>
                  {patternType === 'weekday'
                    ? t('electricityUsage.noDaysSelected')
                    : t('electricityUsage.noMonthsSelected')}
                </span>
              )}
            </div>

            <Divider orientation="left">{t('electricityUsage.hourlyData')}</Divider>
            {renderCompactTable(pattern.hourlyData)}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="usage-pattern-form">
      {renderTabs()}
    </div>
  );
};

export default UsagePatternForm;
