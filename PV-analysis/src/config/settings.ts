/**
 * 应用程序配置文件
 * 包含全局设置和特殊项目配置
 */

// 特殊项目配置
export const special = {
  // 特殊项目设置
  projects: {
    // 分析设置
    analysis: {
      // 默认电价（如果没有找到匹配的电价规则）
      defaultPrice: 25, // 默认电价25日元/kWh
      // 默认上网电价（如果没有设置上网电价）
      defaultGridFeedInPrice: 17.5, // 默认上网电价17.5日元/kWh
      // 上网电价比例（相对于购电价格，当没有设置上网电价和默认上网电价时使用）
      gridExportPriceRatio: 0.7, // 上网电价为购电价格的70%
      // 系统默认效率（当没有设备数据时使用）
      defaultEfficiency: {
        pv: 0.15, // 光伏系统默认效率15%
        inverter: 0.95, // 逆变器默认效率95%
        storage: 0.9, // 储能系统默认效率90%
      }
    },
    // 验证设置
    validation: {
      // 必填步骤
      requiredSteps: ['basicInfo', 'irradianceData', 'electricityPrice', 'electricityUsage'],
      // 设备步骤（至少需要一种设备）
      equipmentSteps: ['pvModules', 'energyStorage', 'inverters']
    }
  }
};

// 缓存设置
export const cache = {
  // 最大缓存百分比
  maxCachePercentage: 80,
  // 缓存键前缀
  keyPrefix: {
    irradiance: 'pv_irradiance_',
    irradianceChunk: 'pv_irradiance_chunk_',
    irradianceCompressed: 'pv_irradiance_compressed_',
    project: 'pv_project_',
    projectHourly: 'pv_project_hourly_',
  },
  // 最少保留的数据项数量
  minItemsToKeep: 10,
  // 最大块大小（1MB）
  maxChunkSize: 1024 * 1024,
  // 元数据最大大小（100KB）
  maxMetadataSize: 100 * 1024,
  // 是否使用压缩存储
  useCompression: true,
  // 缓存大小（32MB）
  maxCacheSize: 32 * 1024 * 1024
};

// 默认设置
export const defaults = {
  // 语言设置
  language: 'zh',
  // 货币设置
  currency: 'JPY',
  // 时区设置
  timezone: 'Asia/Tokyo',
  // 主题设置
  theme: 'light'
};

// 导出默认配置
export default {
  special,
  cache,
  defaults
};
