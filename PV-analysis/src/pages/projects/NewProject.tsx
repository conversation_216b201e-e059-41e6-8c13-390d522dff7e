import React, { useState, useEffect, useRef } from 'react';
import { Steps, Button, message, Card, Row, Col, Space, Tooltip } from 'antd';
import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { useAppDispatch, useAppSelector } from '../../store';
import { addProject, updateProject } from '../../store/slices/projectsSlice';
import { PageHeader, NavigationConfirmDialog, SyncStatus, ErrorBoundary } from '../../components/common';
import { Project, ProjectStatus } from '../../types';
import { ProjectData } from '../../types/projectData';
import { initializeProjectData, updateElectricityUsage } from '../../services/projectDataService';
import { createProject as createProjectSync, updateProject as updateProjectSync } from '../../services/projectSyncService';
import { analyzeProject } from '../../services/projectAnalysisService';
import { special } from '../../config/settings';

// 步骤组件
import BasicInfoStep from '../../components/projects/BasicInfoStep';
import IrradianceDataStep from '../../components/projects/IrradianceDataStep';
import ElectricityPriceStep from '../../components/projects/ElectricityPriceStep';
import ElectricityUsageStep from '../../components/projects/ElectricityUsageStep';
import PVModulesStep from '../../components/projects/PVModulesStep';
import EnergyStorageStep from '../../components/projects/EnergyStorageStep';
import InvertersStep from '../../components/projects/InvertersStep';
import OtherInvestmentsStep from '../../components/projects/OtherInvestmentsStep';

// 步骤类型
type StepKey = 'basicInfo' | 'irradianceData' | 'electricityPrice' | 'electricityUsage' |
               'pvModules' | 'energyStorage' | 'inverters' | 'otherInvestments';

const NewProject: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // 组件挂载状态引用，用于防止组件卸载后更新状态
  const isMounted = useRef(true);

  // 获取当前项目（如果是编辑模式）
  const { currentProject } = useAppSelector((state) => state.projects);
  const isEditMode = !!currentProject;

  // 当前步骤
  const [currentStep, setCurrentStep] = useState<number>(0);

  // 导航确认对话框状态
  const [showNavigationDialog, setShowNavigationDialog] = useState<boolean>(false);
  const [targetRoute, setTargetRoute] = useState<string | number | null>(null);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // 组件卸载标志
  useEffect(() => {
    return () => {
      // 组件卸载时设置标志
      isMounted.current = false;
    };
  }, []);

  // 安全导航函数，确保组件卸载后不会更新状态
  const safeNavigate = (path: string) => {
    console.log('执行导航到:', path);

    // 直接使用React Router导航
    navigate(path);
  };

  // 监听全局导航尝试事件
  useEffect(() => {
    const handleNavigationAttempt = (event: any) => {
      // 获取目标路径
      const targetPath = event.detail.targetPath;
      console.log('收到导航尝试事件，目标路径:', targetPath);

      // 检查当前URL是否已经是嵌套路径（如/projects/new/settings）
      const currentPath = window.location.pathname;
      const isInNestedPath = currentPath.includes('/projects/new/') ||
                            (currentPath.includes('/projects/edit/') && currentPath.split('/').length > 4);

      console.log('当前路径:', currentPath, '是否在嵌套路径中:', isInNestedPath);

      // 如果当前在嵌套路径中，需要特殊处理
      if (isInNestedPath) {
        console.log('检测到嵌套路径，强制导航到:', targetPath);
        // 使用window.location强制导航，完全刷新页面
        window.location.href = targetPath;
        return;
      }

      // 检查是否是离开项目创建/编辑流程的导航
      // 只有当导航到项目创建/编辑流程外部时才显示确认对话框
      const isLeavingProjectCreation = typeof targetPath === 'string' &&
        !targetPath.startsWith('/projects/new') &&
        !targetPath.startsWith('/projects/edit');

      if (hasChanges && isLeavingProjectCreation) {
        // 阻止默认导航行为
        if (event.cancelable) {
          event.preventDefault();
        }

        // 设置目标路由
        setTargetRoute(targetPath);

        // 显示确认对话框
        setShowNavigationDialog(true);
        console.log('尝试离开项目创建流程，显示确认对话框');
      } else if (!hasChanges && isLeavingProjectCreation) {
        // 如果没有更改，直接导航
        console.log('无更改，直接导航到:', targetPath);
        // 使用window.location强制导航，确保页面完全刷新
        window.location.href = targetPath;
      } else {
        // 这是项目创建流程内部的导航，例如切换步骤
        console.log('项目创建流程内部导航');
        // 不需要特殊处理
      }
    };

    // 添加事件监听
    window.addEventListener('navigation-attempt', handleNavigationAttempt as EventListener);

    // 清理函数
    return () => {
      window.removeEventListener('navigation-attempt', handleNavigationAttempt as EventListener);
    };
  }, [hasChanges, navigate]);

  // 记录已访问的步骤
  const [visitedSteps, setVisitedSteps] = useState<number[]>([0]); // 初始时只有第一步被访问

  // 项目数据
  const [projectData, setProjectData] = useState<Partial<Project>>(
    currentProject || {
      id: uuidv4(),
      status: 'draft' as ProjectStatus,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      basicInfo: {
        name: '',
        location: '',
        capacity: 0,
        description: '',
      },
      pvModules: [],
      energyStorage: [],
      inverters: [],
      otherInvestments: [],
    }
  );

  // 项目详细数据（包含8760小时点的数据）
  const [detailedProjectData, setDetailedProjectData] = useState<ProjectData | null>(null);

  // 步骤数据是否有效
  const [stepsValid, setStepsValid] = useState<Record<StepKey, boolean>>({
    basicInfo: false,
    irradianceData: false,
    electricityPrice: false,
    electricityUsage: false,
    pvModules: false,
    energyStorage: false,
    inverters: false,
    otherInvestments: false,
  });

  // 步骤列表
  const steps = [
    {
      key: 'basicInfo',
      title: t('projectWizard.step1'),
      content: (
        <ErrorBoundary>
          <BasicInfoStep
            data={projectData.basicInfo}
            onChange={(data) => handleStepDataChange('basicInfo', data)}
            onValidate={(valid) => handleStepValidation('basicInfo', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'irradianceData',
      title: t('projectWizard.step2'),
      content: (
        <ErrorBoundary>
          <IrradianceDataStep
            data={projectData.irradianceDataId}
            onChange={(data) => handleStepDataChange('irradianceDataId', data)}
            onValidate={(valid) => handleStepValidation('irradianceData', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'electricityPrice',
      title: t('projectWizard.step3'),
      content: (
        <ErrorBoundary>
          <ElectricityPriceStep
            data={projectData.electricityPriceId}
            onChange={(data) => handleStepDataChange('electricityPriceId', data)}
            onValidate={(valid) => handleStepValidation('electricityPrice', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'electricityUsage',
      title: t('projectWizard.step4'),
      content: (
        <ErrorBoundary>
          <ElectricityUsageStep
            data={projectData.electricityUsage}
            onChange={(data) => handleStepDataChange('electricityUsage', data)}
            onValidate={(valid) => handleStepValidation('electricityUsage', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'pvModules',
      title: t('projectWizard.step5'),
      content: (
        <ErrorBoundary>
          <PVModulesStep
            data={projectData.pvModules}
            onChange={(data) => handleStepDataChange('pvModules', data)}
            onValidate={(valid) => handleStepValidation('pvModules', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'energyStorage',
      title: t('projectWizard.step6'),
      content: (
        <ErrorBoundary>
          <EnergyStorageStep
            data={projectData.energyStorage}
            onChange={(data) => handleStepDataChange('energyStorage', data)}
            onValidate={(valid) => handleStepValidation('energyStorage', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'inverters',
      title: t('projectWizard.step7'),
      content: (
        <ErrorBoundary>
          <InvertersStep
            data={projectData.inverters}
            onChange={(data) => handleStepDataChange('inverters', data)}
            onValidate={(valid) => handleStepValidation('inverters', valid)}
          />
        </ErrorBoundary>
      ),
    },
    {
      key: 'otherInvestments',
      title: t('projectWizard.step8'),
      content: (
        <ErrorBoundary>
          <OtherInvestmentsStep
            data={projectData.otherInvestments}
            onChange={(data) => handleStepDataChange('otherInvestments', data)}
            onValidate={(valid) => handleStepValidation('otherInvestments', valid)}
          />
        </ErrorBoundary>
      ),
    },
  ];

  // 初始化项目详细数据
  useEffect(() => {
    if (projectData) {
      // 初始化详细项目数据
      const initializedData = initializeProjectData(projectData as Project);
      setDetailedProjectData(initializedData);
      console.log('项目详细数据已初始化:', initializedData);
    }
  }, []);

  // 监听项目数据变化，检查并更新步骤验证状态（仅在组件初始化时执行一次）
  useEffect(() => {
    console.log('初始化时检查项目数据，更新步骤验证状态');

    // 检查基本信息步骤
    if (projectData.basicInfo) {
      const basicInfo = projectData.basicInfo;
      const isBasicInfoValid =
        !!basicInfo.name &&
        !!basicInfo.capacity &&
        !!basicInfo.region &&
        !!basicInfo.prefecture &&
        !!basicInfo.installationType &&
        !!basicInfo.projectType &&
        !!basicInfo.connectionType;

      console.log('基本信息验证结果:', isBasicInfoValid);

      if (isBasicInfoValid && !stepsValid.basicInfo) {
        console.log('基本信息有效，但状态未更新，现在更新状态');
        setStepsValid(prev => ({
          ...prev,
          basicInfo: true
        }));
      }
    }

    // 可以添加其他步骤的检查逻辑

    // 注意：这个useEffect只在组件挂载时执行一次，不依赖于projectData或stepsValid的变化
  }, []);

  // 处理步骤数据变更
  const handleStepDataChange = (key: string, data: any) => {
    console.log(`NewProject: 步骤数据变更 - 步骤: ${key}, 数据类型: ${Array.isArray(data) ? 'Array' : typeof data}, 数据长度: ${Array.isArray(data) ? data.length : '非数组'}`);

    // 特殊处理光伏组件数据
    if (key === 'pvModules') {
      console.log('NewProject: 处理光伏组件数据变更，新数据长度:', data.length);
      console.log('NewProject: 当前光伏组件数据:', projectData.pvModules);
    }

    // 更新项目基本数据
    setProjectData((prev) => {
      const newData = {
        ...prev,
        [key]: data,
        updatedAt: new Date().toISOString(),
      };
      console.log(`NewProject: 更新后的${key}数据长度:`, Array.isArray(newData[key]) ? newData[key].length : '非数组');
      return newData;
    });

    // 如果是电力使用数据变更，同时更新详细项目数据
    if (key === 'electricityUsage' && detailedProjectData) {
      const updatedDetailedData = updateElectricityUsage(
        detailedProjectData,
        data
      );
      setDetailedProjectData(updatedDetailedData);
      console.log('电力使用数据已更新，详细项目数据已更新');
    }

    setHasChanges(true);
  };

  // 处理步骤验证
  const handleStepValidation = (key: StepKey, valid: boolean) => {
    // 检查是否需要更新状态（只有当验证结果与当前状态不同时才更新）
    if (stepsValid[key] !== valid) {
      console.log(`步骤验证 - 步骤: ${key}, 验证结果: ${valid}`);

      // 更新步骤验证状态
      setStepsValid((prev) => {
        const newState = {
          ...prev,
          [key]: valid,
        };
        return newState;
      });
    }
  };

  // 处理下一步
  const handleNext = () => {
    const nextStep = currentStep + 1;
    setCurrentStep(nextStep);

    // 记录已访问的步骤
    if (!visitedSteps.includes(nextStep)) {
      setVisitedSteps([...visitedSteps, nextStep]);
    }
  };

  // 处理上一步
  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  // 检查所有必填步骤是否有效
  const checkAllRequiredStepsValid = () => {
    // 从配置文件获取基础必填步骤的键
    const basicRequiredStepKeys: StepKey[] = special.projects.validation.requiredSteps as StepKey[];

    // 检查基础必填步骤是否有效
    const basicStepsValid = basicRequiredStepKeys.every(key => stepsValid[key]);

    // 从配置文件获取设备步骤
    const equipmentStepKeys: StepKey[] = special.projects.validation.equipmentSteps as StepKey[];

    // 检查三类设备（光伏设备、储能设备和逆变器）是否至少有一类不为空
    const hasEquipment = (
      (projectData.pvModules && projectData.pvModules.length > 0) ||
      (projectData.energyStorage && projectData.energyStorage.length > 0) ||
      (projectData.inverters && projectData.inverters.length > 0)
    );

    console.log('基础步骤验证结果:', basicStepsValid);
    console.log('设备验证结果:', hasEquipment);
    console.log('光伏设备数量:', projectData.pvModules?.length || 0);
    console.log('储能设备数量:', projectData.energyStorage?.length || 0);
    console.log('逆变器数量:', projectData.inverters?.length || 0);

    return basicStepsValid && hasEquipment;
  };

  // 获取步骤状态
  type StepStatus = 'wait' | 'process' | 'finish' | 'error' | 'warning';

  // 确定步骤的状态
  const getStepStatus = (index: number): StepStatus => {
    const stepKey = steps[index].key as StepKey;

    // 当前步骤显示为进行中
    if (index === currentStep) {
      return 'process';
    }

    // 已访问过的步骤
    if (index < currentStep || visitedSteps.includes(index)) {
      // 从配置文件获取基础必填步骤
      const isBasicRequiredStep = special.projects.validation.requiredSteps.includes(stepKey);

      // 从配置文件获取设备步骤
      const isEquipmentStep = special.projects.validation.equipmentSteps.includes(stepKey);

      // 如果是基础必填步骤且未完成，显示警告
      if (isBasicRequiredStep && !stepsValid[stepKey]) {
        return 'warning';
      }

      // 如果是设备步骤，检查是否有设备
      if (isEquipmentStep) {
        // 检查当前设备类型是否有数据
        let hasCurrentTypeEquipment = false;

        if (stepKey === 'pvModules' && projectData.pvModules && projectData.pvModules.length > 0) {
          hasCurrentTypeEquipment = true;
        } else if (stepKey === 'energyStorage' && projectData.energyStorage && projectData.energyStorage.length > 0) {
          hasCurrentTypeEquipment = true;
        } else if (stepKey === 'inverters' && projectData.inverters && projectData.inverters.length > 0) {
          hasCurrentTypeEquipment = true;
        }

        // 检查是否有任何设备
        const hasAnyEquipment = (
          (projectData.pvModules && projectData.pvModules.length > 0) ||
          (projectData.energyStorage && projectData.energyStorage.length > 0) ||
          (projectData.inverters && projectData.inverters.length > 0)
        );

        // 如果当前设备类型没有数据，但已经访问过该步骤，显示警告
        if (!hasCurrentTypeEquipment && visitedSteps.includes(index)) {
          // 如果已经有其他类型的设备，则不需要显示警告
          if (!hasAnyEquipment) {
            return 'warning';
          }
        }
      }

      // 其他情况显示完成
      return 'finish';
    }

    // 未访问的步骤
    return 'wait';
  };

  // 获取步骤图标
  const getStepIcon = (status: StepStatus) => {
    switch (status) {
      case 'finish':
        return <CheckCircleOutlined />;
      case 'warning':
        return <Tooltip title={t('projectWizard.incompleteStepWarning')}><ExclamationCircleOutlined style={{ color: '#faad14' }} /></Tooltip>;
      case 'error':
        return <ExclamationCircleOutlined />;
      default:
        return null;
    }
  };

  // 获取步骤未完成的必填项
  const getIncompleteRequiredFields = (stepKey: StepKey): string[] => {
    const incompleteFields: string[] = [];

    // 基本信息步骤
    if (stepKey === 'basicInfo') {
      const basicInfo = projectData.basicInfo || {};
      if (!basicInfo.name) incompleteFields.push(t('projects.name'));
      if (!basicInfo.capacity) incompleteFields.push(t('projects.capacity'));
      if (!basicInfo.region) incompleteFields.push(t('projects.region'));
      if (!basicInfo.prefecture) incompleteFields.push(t('projects.prefecture'));
      if (!basicInfo.installationType) incompleteFields.push(t('projects.installationType'));
      if (!basicInfo.projectType) incompleteFields.push(t('projects.projectType'));
      if (!basicInfo.connectionType) incompleteFields.push(t('projects.connectionType'));
    }

    // 光照数据步骤
    else if (stepKey === 'irradianceData') {
      if (!projectData.irradianceDataId) incompleteFields.push(t('irradiance.datasetSelection'));
    }

    // 电价政策步骤
    else if (stepKey === 'electricityPrice') {
      if (!projectData.electricityPriceId) incompleteFields.push(t('electricityPrice.policySelection'));
    }

    // 用电数据步骤
    else if (stepKey === 'electricityUsage') {
      const electricityUsage = projectData.electricityUsage || {};
      if (!electricityUsage.data || electricityUsage.data.length === 0) {
        incompleteFields.push(t('electricityUsage.usageData'));
      }
    }

    // 设备步骤 - 至少需要一种设备
    else if (special.projects.validation.equipmentSteps.includes(stepKey)) {
      const hasAnyEquipment = (
        (projectData.pvModules && projectData.pvModules.length > 0) ||
        (projectData.energyStorage && projectData.energyStorage.length > 0) ||
        (projectData.inverters && projectData.inverters.length > 0)
      );

      if (!hasAnyEquipment) {
        incompleteFields.push(t('projectWizard.atLeastOneEquipmentRequired'));
      } else {
        // 如果当前步骤是光伏设备且没有光伏设备
        if (stepKey === 'pvModules' && (!projectData.pvModules || projectData.pvModules.length === 0)) {
          incompleteFields.push(t('pvModules.addModule'));
        }
        // 如果当前步骤是逆变器且没有逆变器
        else if (stepKey === 'inverters' && (!projectData.inverters || projectData.inverters.length === 0)) {
          incompleteFields.push(t('inverters.addInverter'));
        }
      }
    }

    return incompleteFields;
  };

  // 处理保存草稿
  const handleSaveDraft = async () => {
    try {
      console.log('🚀 开始保存草稿...');
      console.log('📊 当前项目数据:', projectData);
      console.log('📊 是否为编辑模式:', isEditMode);

      // 确保项目数据包含必要的字段
      const projectToSave: ProjectData = {
        ...projectData as ProjectData,
        name: projectData.basicInfo?.name || '未命名项目',
        location: projectData.basicInfo?.location || '',
        capacity: projectData.basicInfo?.capacity || 0,
        status: 'draft',
        updatedAt: new Date().toISOString(),
        // 确保其他必要字段有默认值
        irradianceDataId: projectData.irradianceDataId || '',
        electricityPriceId: projectData.electricityPriceId || '',
        electricityUsage: projectData.electricityUsage || { type: 'monthly', data: [] },
        pvModules: projectData.pvModules || [],
        energyStorage: projectData.energyStorage || [],
        inverters: projectData.inverters || [],
        otherInvestments: projectData.otherInvestments || []
      };

      console.log('📊 处理后的项目数据:', projectToSave);
      console.log('📊 项目ID:', projectToSave.id);
      console.log('📊 项目名称:', projectToSave.name);
      console.log('📊 项目状态:', projectToSave.status);

      // 如果有详细项目数据，将分析结果添加到项目中
      if (detailedProjectData && detailedProjectData.analysisResults) {
        console.log('📊 添加详细项目数据的分析结果');

        // 创建一个深拷贝的hourlyData数组，避免直接修改原始对象
        const hourlyDataCopy = detailedProjectData.analysisResults.hourlyData ?
          JSON.parse(JSON.stringify(detailedProjectData.analysisResults.hourlyData)) : [];

        // 使用深拷贝的数据创建新的分析结果对象
        projectToSave.analysisResults = {
          hourlyData: hourlyDataCopy.map((hourData: any) => ({
            hour: hourData.hour,
            day: hourData.day,
            month: hourData.month,
            pvGeneration: hourData.pvGeneration,
            storageCapacity: hourData.storageCapacity,
            electricityConsumption: {
              pv: 0, // 这些值将在后续计算中填充
              storage: 0,
              grid: typeof hourData.electricityConsumption === 'object'
                ? hourData.electricityConsumption.grid || 0
                : hourData.electricityConsumption || 0 // 处理不同格式的电力消耗数据
            },
            gridExport: hourData.gridExport || 0,
            storageCharge: hourData.storageCharge || 0,
            storageDischarge: hourData.storageCharge && -hourData.storageCharge < 0 ? -hourData.storageCharge : 0,
            storageBenefit: hourData.storageBenefit || 0,
            pvBenefit: hourData.pvBenefit || 0,
            gridExportIncome: hourData.gridExportIncome || 0,
            totalBenefit: hourData.totalBenefit || 0
          })),
          dailyData: [], // 这些将在后续计算中填充
          monthlyData: [],
          yearlyData: {
            pvGeneration: 0,
            electricityConsumption: {
              pv: 0,
              storage: 0,
              grid: 0
            },
            gridExport: 0,
            storageBenefit: 0,
            pvBenefit: 0,
            gridExportIncome: 0,
            totalBenefit: 0,
            roi: 0,
            paybackPeriod: 0
          }
        };
        console.log('📊 分析结果小时数据点数:', projectToSave.analysisResults.hourlyData.length);
      } else {
        console.log('📊 没有详细项目数据或分析结果');
      }

      // 检查服务器连接状态
      console.log('🔍 检查服务器连接状态...');
      let isServerAvailable = false;
      try {
        const testResponse = await fetch('http://localhost:3001/api/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // 设置较短的超时时间
          signal: AbortSignal.timeout(3000)
        });
        isServerAvailable = testResponse.ok;
        console.log(`🔍 服务器连接状态: ${isServerAvailable ? '可用' : '不可用'}`);
      } catch (connectionError) {
        console.error('🔍 服务器连接测试失败:', connectionError);
        isServerAvailable = false;
      }

      // 保存项目数据到本地和服务器
      console.log('📝 准备保存草稿:', projectToSave);
      let savedProject;

      try {
        if (isEditMode) {
          // 更新项目
          console.log('📝 更新现有项目, ID:', projectToSave.id);
          savedProject = await updateProjectSync(projectToSave.id, projectToSave);
          console.log('✅ 项目更新成功, 返回数据:', savedProject);
          console.log('📝 准备更新Redux状态');
          dispatch(updateProject(savedProject as Project));
          console.log('✅ Redux状态已更新');
        } else {
          // 创建项目
          console.log('📝 创建新项目, ID:', projectToSave.id);
          savedProject = await createProjectSync(projectToSave);
          console.log('✅ 项目创建成功, 返回数据:', savedProject);
          console.log('📝 准备更新Redux状态');
          dispatch(addProject(savedProject as Project));
          console.log('✅ Redux状态已更新');
        }

        // 验证保存是否成功
        console.log('🔍 验证项目是否成功保存到本地存储');
        const key = `pv_project_${projectToSave.id}`;
        const savedData = localStorage.getItem(key);

        if (savedData) {
          console.log('✅ 验证成功: 项目已保存到本地存储, 数据长度:', savedData.length);

          // 检查小时数据是否已保存
          if (projectToSave.analysisResults && projectToSave.analysisResults.hourlyData && projectToSave.analysisResults.hourlyData.length > 0) {
            const hourlyKey = `pv_project_hourly_${projectToSave.id}`;
            const hourlyData = localStorage.getItem(hourlyKey);

            if (hourlyData) {
              console.log('✅ 验证成功: 小时数据已保存到本地存储, 数据长度:', hourlyData.length);
            } else {
              console.warn('⚠️ 小时数据可能未保存到本地存储');
            }
          }

          console.log('📝 显示成功消息');
          message.success(isServerAvailable ? t('projects.draftSaved') : t('projects.draftSavedLocally'));
          console.log('📝 重置更改状态');
          setHasChanges(false); // 重置更改状态
          console.log('📝 准备强制导航到项目列表');
          setTimeout(() => {
            window.location.href = '/projects';
            console.log('✅ 强制导航已触发');
          }, 500);
        } else {
          console.error('❌ 验证失败: 项目未保存到本地存储');
          throw new Error('项目未成功保存到本地存储');
        }
      } catch (saveError) {
        console.error('❌ 保存操作失败:', saveError);

        // 尝试直接保存到本地存储
        console.log('⚠️ 尝试直接保存到本地存储');
        try {
          const key = `pv_project_${projectToSave.id}`;

          // 提取小时数据
          let hourlyData = null;
          if (projectToSave.analysisResults && projectToSave.analysisResults.hourlyData && projectToSave.analysisResults.hourlyData.length > 0) {
            console.log('📊 提取小时数据，数据点数:', projectToSave.analysisResults.hourlyData.length);
            hourlyData = [...projectToSave.analysisResults.hourlyData]; // 创建副本
            // 从元数据中移除小时数据
            projectToSave.analysisResults = {
              ...projectToSave.analysisResults,
              hourlyData: []
            };
          }

          // 保存元数据
          projectToSave.syncStatus = 'local-only';
          localStorage.setItem(key, JSON.stringify(projectToSave));
          console.log('✅ 项目元数据已直接保存到本地存储');

          // 如果有小时数据，压缩并保存
          if (hourlyData && hourlyData.length > 0) {
            try {
              console.log('📊 尝试压缩并保存小时数据');

              // 将小时数据分批处理，每批最多1000条记录
              const BATCH_SIZE = 1000;
              const batches = [];

              for (let i = 0; i < hourlyData.length; i += BATCH_SIZE) {
                batches.push(hourlyData.slice(i, i + BATCH_SIZE));
              }

              console.log(`将${hourlyData.length}条记录分成${batches.length}批处理`);

              // 逐批压缩并保存
              for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                const batchJson = JSON.stringify(batch);
                console.log(`批次${i+1}/${batches.length} JSON长度:`, batchJson.length, 'bytes');

                // 使用LZ-string压缩数据
                const compressedData = LZString.compressToUTF16(batchJson);
                console.log(`批次${i+1}/${batches.length} 压缩后长度:`, compressedData.length, 'bytes');

                // 保存压缩数据
                const hourlyDataKey = `pv_project_hourly_${projectToSave.id}_batch_${i}`;
                localStorage.setItem(hourlyDataKey, compressedData);
                console.log(`批次${i+1}/${batches.length} 已保存到localStorage`);
              }

              // 保存批次信息
              const batchInfoKey = `pv_project_hourly_${projectToSave.id}_info`;
              const batchInfo = {
                totalRecords: hourlyData.length,
                batchCount: batches.length,
                batchSize: BATCH_SIZE
              };
              localStorage.setItem(batchInfoKey, JSON.stringify(batchInfo));
              console.log('小时数据批次信息已保存');

            } catch (compressionError) {
              console.error('❌ 压缩和保存小时数据失败:', compressionError);
            }
          }

          // 更新Redux状态
          if (isEditMode) {
            dispatch(updateProject(projectToSave as Project));
          } else {
            dispatch(addProject(projectToSave as Project));
          }
          console.log('✅ Redux状态已更新');

          message.success(t('projects.draftSavedLocally'));
          setHasChanges(false);
          setTimeout(() => {
            window.location.href = '/projects';
            console.log('✅ 强制导航已触发');
          }, 500);
        } catch (directSaveError) {
          console.error('❌ 直接保存到本地存储也失败:', directSaveError);
          throw directSaveError;
        }
      }
    } catch (error) {
      console.error('❌ 保存草稿失败:', error);
      console.error('❌ 错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
      message.error(t('common.saveFailed'));
    }
  };

  // 处理开始分析
  const handleFinish = async () => {
    // 从配置文件获取基础必填步骤
    const basicRequiredStepKeys: StepKey[] = special.projects.validation.requiredSteps as StepKey[];
    const basicStepsValid = basicRequiredStepKeys.every(key => stepsValid[key]);

    if (!basicStepsValid) {
      message.error(t('projectWizard.someRequiredFieldsIncomplete'));
      return;
    }

    // 检查三类设备（光伏设备、储能设备和逆变器）是否至少有一类不为空
    const hasEquipment = (
      (projectData.pvModules && projectData.pvModules.length > 0) ||
      (projectData.energyStorage && projectData.energyStorage.length > 0) ||
      (projectData.inverters && projectData.inverters.length > 0)
    );

    if (!hasEquipment) {
      message.error(t('projectWizard.atLeastOneEquipmentRequired'));
      return;
    }

    try {
      // 显示加载提示
      message.loading({ content: t('common.calculating'), key: 'analyzing', duration: 0 });

      // 确保项目数据包含必要的字段
      const projectToSave: ProjectData = {
        ...projectData as ProjectData,
        name: projectData.basicInfo?.name || '未命名项目',
        location: projectData.basicInfo?.location || '',
        capacity: projectData.basicInfo?.capacity || 0,
        status: 'draft', // 先设置为草稿状态，分析完成后会更新为completed
        updatedAt: new Date().toISOString(),
        // 确保其他必要字段有默认值
        irradianceDataId: projectData.irradianceDataId || '',
        electricityPriceId: projectData.electricityPriceId || '',
        electricityUsage: projectData.electricityUsage || { type: 'monthly', data: [] },
        pvModules: projectData.pvModules || [],
        energyStorage: projectData.energyStorage || [],
        inverters: projectData.inverters || [],
        otherInvestments: projectData.otherInvestments || []
      };

      console.log('准备保存项目并开始分析:', projectToSave);
      let savedProject;

      // 先保存项目
      if (isEditMode) {
        // 更新项目
        savedProject = await updateProjectSync(projectToSave.id, projectToSave);
        dispatch(updateProject(savedProject as Project));
      } else {
        // 创建项目
        savedProject = await createProjectSync(projectToSave);
        dispatch(addProject(savedProject as Project));
      }

      // 开始分析项目
      console.log('开始分析项目:', savedProject.id);
      const analyzedProject = await analyzeProject(savedProject as ProjectData);

      // 更新Redux状态
      if (isEditMode) {
        dispatch(updateProject(analyzedProject as Project));
      } else {
        dispatch(addProject(analyzedProject as Project));
      }

      // 关闭加载提示并显示成功消息
      message.success({ content: t('projects.analysisCompleted'), key: 'analyzing', duration: 2 });

      // 重置更改状态
      setHasChanges(false);

      // 导航到项目分析结果页面
      navigate(`/projects/analysis/${analyzedProject.id}`);
    } catch (error) {
      console.error('项目分析失败:', error);
      message.error({ content: t('projects.analysisFailed'), key: 'analyzing' });
    }
  };

  // 处理返回项目列表
  const handleBackToList = () => {
    console.log('点击返回项目列表按钮，当前步骤:', currentStep);

    // 检查当前URL是否已经是嵌套路径（如/projects/new/settings）
    const currentPath = window.location.pathname;
    const isInNestedPath = currentPath.includes('/projects/new/') ||
                          (currentPath.includes('/projects/edit/') && currentPath.split('/').length > 4);

    if (isInNestedPath) {
      // 如果当前在嵌套路径中，强制导航到项目列表
      console.log('检测到嵌套路径，强制导航到项目列表');
      window.location.href = '/projects';
      return;
    }

    if (hasChanges) {
      // 这是真正离开项目创建流程的操作，需要显示确认对话框
      setTargetRoute('/projects');
      setShowNavigationDialog(true);
      console.log('尝试返回项目列表，显示确认对话框');
    } else {
      // 直接导航到项目列表
      console.log('无更改，直接返回项目列表');
      // 使用window.location直接导航，确保页面完全刷新
      window.location.href = '/projects';
    }
  };

  // 监听浏览器的前进/后退按钮
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasChanges) {
        // 显示浏览器的确认对话框
        event.preventDefault();
        event.returnValue = '';
        return '';
      }
    };

    // 添加事件监听
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasChanges]);

  // 处理导航确认对话框的保存草稿操作
  const handleSaveDraftAndNavigate = async () => {
    try {
      console.log('开始执行保存草稿并导航操作...');
      console.log('当前步骤:', currentStep);
      console.log('当前项目数据:', projectData);
      console.log('是否为编辑模式:', isEditMode);
      console.log('目标路由:', targetRoute);

      // 确保项目数据包含必要的字段
      const projectToSave: ProjectData = {
        ...projectData as ProjectData,
        name: projectData.basicInfo?.name || '未命名项目',
        location: projectData.basicInfo?.location || '',
        capacity: projectData.basicInfo?.capacity || 0,
        status: 'draft',
        updatedAt: new Date().toISOString(),
        // 确保其他必要字段有默认值
        irradianceDataId: projectData.irradianceDataId || '',
        electricityPriceId: projectData.electricityPriceId || '',
        electricityUsage: projectData.electricityUsage || { type: 'monthly', data: [] },
        pvModules: projectData.pvModules || [],
        energyStorage: projectData.energyStorage || [],
        inverters: projectData.inverters || [],
        otherInvestments: projectData.otherInvestments || []
      };

      console.log('处理后的项目数据:', projectToSave);
      console.log('项目ID:', projectToSave.id);
      console.log('项目名称:', projectToSave.name);
      console.log('项目状态:', projectToSave.status);

      // 如果有详细项目数据，将分析结果添加到项目中
      if (detailedProjectData && detailedProjectData.analysisResults) {
        console.log('添加详细项目数据的分析结果');

        // 创建一个深拷贝的hourlyData数组，避免直接修改原始对象
        const hourlyDataCopy = detailedProjectData.analysisResults.hourlyData ?
          JSON.parse(JSON.stringify(detailedProjectData.analysisResults.hourlyData)) : [];

        // 使用深拷贝的数据创建新的分析结果对象
        projectToSave.analysisResults = {
          hourlyData: hourlyDataCopy.map((hourData: any) => ({
            hour: hourData.hour,
            day: hourData.day,
            month: hourData.month,
            pvGeneration: hourData.pvGeneration || {},
            storageCapacity: hourData.storageCapacity || 0,
            electricityConsumption: typeof hourData.electricityConsumption === 'object'
              ? hourData.electricityConsumption.grid + hourData.electricityConsumption.pv + hourData.electricityConsumption.storage
              : hourData.electricityConsumption || 0,
            gridExport: hourData.gridExport || 0,
            storageCharge: hourData.storageCharge || 0
          })),
          analysisCompleted: detailedProjectData.analysisResults.analysisCompleted || false,
          analysisDate: detailedProjectData.analysisResults.analysisDate || new Date().toISOString()
        };
        console.log('分析结果小时数据点数:', projectToSave.analysisResults.hourlyData.length);
      } else {
        console.log('没有详细项目数据或分析结果');
      }

      // 保存项目数据到本地和服务器
      console.log('准备保存草稿并导航:', projectToSave);
      let savedProject;

      if (isEditMode) {
        // 更新项目
        console.log('更新现有项目, ID:', projectToSave.id);
        savedProject = await updateProjectSync(projectToSave.id, projectToSave);
        console.log('项目更新成功, 返回数据:', savedProject);
        console.log('准备更新Redux状态');
        dispatch(updateProject(savedProject as Project));
        console.log('Redux状态已更新');
      } else {
        // 创建项目
        console.log('创建新项目, ID:', projectToSave.id);
        savedProject = await createProjectSync(projectToSave);
        console.log('项目创建成功, 返回数据:', savedProject);
        console.log('准备更新Redux状态');
        dispatch(addProject(savedProject as Project));
        console.log('Redux状态已更新');
      }

      console.log('显示成功消息');
      message.success(t('projects.draftSaved'));

      // 保存当前目标路由的副本，因为在状态更新后可能会丢失
      const currentTargetRoute = targetRoute;
      console.log('保存的目标路由:', currentTargetRoute);

      // 使用setTimeout确保状态更新和UI渲染完成后再执行导航
      console.log('设置延时执行导航操作');
      setTimeout(() => {
        console.log('延时执行开始');
        // 关闭对话框
        console.log('关闭导航确认对话框');
        setShowNavigationDialog(false);

        // 重置更改状态
        console.log('重置更改状态');
        setHasChanges(false);

        // 清除目标路由
        console.log('清除目标路由状态');
        setTargetRoute(null);

        // 导航到目标路由
        if (typeof currentTargetRoute === 'string') {
          console.log('目标路由是字符串，准备导航到:', currentTargetRoute);

          // 检查当前URL是否已经是嵌套路径（如/projects/new/settings）
          const currentPath = window.location.pathname;
          const isInNestedPath = currentPath.includes('/projects/new/') ||
                                (currentPath.includes('/projects/edit/') && currentPath.split('/').length > 4);

          // 检查目标路径是否是特殊格式（如 /projects/new/settings）
          const isTargetNestedPath = currentTargetRoute.includes('/projects/new/') ||
                                    (currentTargetRoute.includes('/projects/edit/') && currentTargetRoute.split('/').length > 4);

          console.log('当前路径:', currentPath);
          console.log('是否在嵌套路径中:', isInNestedPath);
          console.log('目标是否为嵌套路径:', isTargetNestedPath);

          if (isInNestedPath || isTargetNestedPath) {
            // 如果当前在嵌套路径中或目标是嵌套路径，使用window.location强制导航
            console.log('检测到嵌套路径，使用window.location.href强制导航到:', currentTargetRoute);
            window.location.href = currentTargetRoute;
          } else {
            // 否则使用window.location导航，确保页面完全刷新
            console.log('使用window.location.href导航到:', currentTargetRoute);
            window.location.href = currentTargetRoute;
          }
        } else if (typeof currentTargetRoute === 'number') {
          console.log('目标路由是数字，切换到步骤:', currentTargetRoute);
          setCurrentStep(currentTargetRoute);
        } else {
          console.log('目标路由无效或为null:', currentTargetRoute);
        }
      }, 100); // 增加延迟，确保状态更新完成
      console.log('延时导航已设置，等待执行');
    } catch (error) {
      console.error('保存草稿失败:', error);
      console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
      message.error(t('common.saveFailed'));
    }
  };

  // 处理导航确认对话框的放弃更改操作
  const handleDiscardChanges = () => {
    console.log('执行放弃更改，当前步骤:', currentStep);

    // 保存当前目标路由的副本，因为在状态更新后可能会丢失
    const currentTargetRoute = targetRoute;
    console.log('目标路由:', currentTargetRoute);

    // 使用setTimeout确保状态更新和UI渲染完成后再执行导航
    setTimeout(() => {
      // 关闭对话框
      setShowNavigationDialog(false);

      // 清除目标路由
      setTargetRoute(null);

      // 重置更改状态
      setHasChanges(false);

      // 导航到目标路由
      if (typeof currentTargetRoute === 'string') {
        console.log('放弃更改，导航到:', currentTargetRoute);

        // 检查当前URL是否已经是嵌套路径（如/projects/new/settings）
        const currentPath = window.location.pathname;
        const isInNestedPath = currentPath.includes('/projects/new/') ||
                              (currentPath.includes('/projects/edit/') && currentPath.split('/').length > 4);

        // 检查目标路径是否是特殊格式（如 /projects/new/settings）
        const isTargetNestedPath = currentTargetRoute.includes('/projects/new/') ||
                                  (currentTargetRoute.includes('/projects/edit/') && currentTargetRoute.split('/').length > 4);

        if (isInNestedPath || isTargetNestedPath) {
          // 如果当前在嵌套路径中或目标是嵌套路径，使用window.location强制导航
          console.log('检测到嵌套路径，强制导航到:', currentTargetRoute);
          window.location.href = currentTargetRoute;
        } else {
          // 否则使用window.location导航，确保页面完全刷新
          window.location.href = currentTargetRoute;
        }
      } else if (typeof currentTargetRoute === 'number') {
        console.log('放弃更改，切换到步骤:', currentTargetRoute);
        setCurrentStep(currentTargetRoute);
      }
    }, 100); // 增加延迟，确保状态更新完成
  };

  // 处理导航确认对话框的取消操作
  const handleCancelNavigation = () => {
    // 关闭对话框
    setShowNavigationDialog(false);
    setTargetRoute(null);
  };

  return (
    <div>
      <PageHeader
        title={isEditMode ? t('projects.edit') : t('projects.new')}
        extra={
          <Button icon={<ArrowLeftOutlined />} onClick={handleBackToList}>
            {t('common.backToList')}
          </Button>
        }
      />

      <Card style={{ marginBottom: '16px' }}>
        <Steps
          current={currentStep}
          items={steps.map((step, index) => {
            // 获取步骤状态
            const status = getStepStatus(index);

            // 获取步骤图标
            const icon = getStepIcon(status);

            // 返回步骤配置
            return {
              title: step.title,
              status: status === 'warning' ? 'finish' : status, // Ant Design Steps 不支持 warning 状态，所以使用 finish 状态
              icon: status === 'warning' ? icon : undefined // 只为警告状态设置自定义图标
            };
          })}
          onChange={(current) => {
            // 允许在所有步骤之间自由切换，不需要确认对话框
            // 因为这些都是项目创建过程的一部分
            setCurrentStep(current);

            // 记录已访问的步骤
            if (!visitedSteps.includes(current)) {
              setVisitedSteps([...visitedSteps, current]);
            }
          }}
        />
      </Card>

      <Card>
        <div style={{ minHeight: '400px', padding: '20px 0' }}>
          {steps[currentStep].content}
        </div>

        <Row justify="space-between">
          <Col>
            {currentStep > 0 && (
              <Button onClick={handlePrevious}>
                {t('projectWizard.previous')}
              </Button>
            )}
          </Col>
          <Col>
            <Space>
              <Button onClick={handleSaveDraft}>
                {t('projects.saveDraft')}
              </Button>
              {currentStep < steps.length - 1 ? (
                <Button type="primary" onClick={handleNext}>
                  {t('projectWizard.next')}
                </Button>
              ) : (
                <Button
                  type="primary"
                  onClick={handleFinish}
                  disabled={!checkAllRequiredStepsValid()}
                  title={!checkAllRequiredStepsValid() ? t('projectWizard.someRequiredFieldsIncomplete') : ''}
                >
                  {t('projects.startAnalysis')}
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 导航确认对话框 */}
      <NavigationConfirmDialog
        visible={showNavigationDialog}
        onSaveDraft={handleSaveDraftAndNavigate}
        onDiscard={handleDiscardChanges}
        onCancel={handleCancelNavigation}
      />
    </div>
  );
};

export default NewProject;
