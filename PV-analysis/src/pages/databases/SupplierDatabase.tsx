import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Divider,
  message,
  Tag,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  fetchDataStart,
  fetchSuppliersSuccess,
  addSupplier,
  updateSupplier,
  deleteSupplier as deleteSupplierAction
} from '../../store/slices/databasesSlice';
import {
  PageHeader,
  EmptyState,
  LoadingSpinner,
  SyncStatus,
  ConfirmDialog,
  FilterForm,
  SupplierForm,
  RatingStars
} from '../../components/common';
import {
  getSupplierList,
  getSupplierDetail,
  createSupplier,
  updateSupplier as updateSupplierService,
  deleteSupplier as deleteSupplierService
} from '../../services/supplierService';
import { Supplier } from '../../types/database';

const SupplierDatabase: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    suppliers,
    isLoading
  } = useAppSelector(state => state.databases);

  const [currentSupplier, setCurrentSupplier] = useState<Supplier | null>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string>('');
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 筛选条件
  const [filters, setFilters] = useState({
    supplierType: '',
    region: '',
    cooperationStatus: '',
    rating: ''
  });

  // 获取供应商列表
  useEffect(() => {
    fetchSupplierList();
  }, []);

  // 获取供应商列表
  const fetchSupplierList = async () => {
    try {
      dispatch(fetchDataStart());
      const response = await getSupplierList();
      dispatch(fetchSuppliersSuccess(response.items));
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      dispatch(fetchSuppliersSuccess([]));
    }
  };

  // 处理选择供应商
  const handleSelectSupplier = async (id: string) => {
    try {
      if (currentSupplier && currentSupplier.id === id) {
        return;
      }

      const data = await getSupplierDetail(id);
      setCurrentSupplier(data);
    } catch (error) {
      console.error('获取供应商详情失败:', error);
      message.error(t('databases.suppliers.loadError'));
    }
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (id: string) => {
    setDeleteItemId(id);
    setDeleteModalVisible(true);
  };

  // 处理删除供应商
  const handleDeleteSupplier = async () => {
    try {
      await deleteSupplierService(deleteItemId);
      dispatch(deleteSupplierAction(deleteItemId));

      // 如果删除的是当前选中的供应商，清空当前供应商
      if (currentSupplier && currentSupplier.id === deleteItemId) {
        setCurrentSupplier(null);
      }

      setDeleteModalVisible(false);
      message.success(t('databases.suppliers.deleteSuccess'));
    } catch (error) {
      console.error('删除供应商失败:', error);
      message.error(t('databases.suppliers.deleteError'));
    }
  };

  // 处理创建供应商
  const handleCreateSupplier = () => {
    createForm.resetFields();
    setCreateModalVisible(true);
  };

  // 处理提交创建表单
  const handleCreateFormSubmit = async () => {
    try {
      const values = await createForm.validateFields();

      // 创建新的供应商
      const newSupplier: Partial<Supplier> = {
        name: values.name,
        contact: values.contact,
        phone: values.phone,
        email: values.email,
        address: values.address,
        products: values.products ? values.products.split(',').map((item: string) => item.trim()) : [],
        supplierType: values.supplierType,
        region: values.region,
        cooperationStatus: values.cooperationStatus,
        rating: values.rating,
      };

      // 调用创建服务
      const createdData = await createSupplier(newSupplier);

      // 更新Redux store
      dispatch(addSupplier(createdData));

      // 关闭创建表单
      setCreateModalVisible(false);
      createForm.resetFields();

      message.success(t('databases.suppliers.createSuccess'));

      // 刷新数据列表
      await fetchSupplierList();
    } catch (error) {
      message.error(t('databases.suppliers.createError'));
      console.error('创建供应商失败:', error);
    }
  };

  // 处理编辑供应商
  const handleEditSupplier = (supplier: Supplier) => {
    // 设置当前选中的供应商
    setCurrentSupplier(supplier);

    // 设置表单初始值
    editForm.setFieldsValue({
      ...supplier,
      products: supplier.products.join(', ')
    });

    // 显示编辑对话框
    setEditModalVisible(true);

    console.log('编辑供应商:', supplier);
  };

  // 处理提交编辑表单
  const handleEditFormSubmit = async () => {
    try {
      console.log('开始提交编辑表单');
      console.log('当前选中的供应商:', currentSupplier);

      const values = await editForm.validateFields();
      console.log('表单验证通过，表单值:', values);

      if (!currentSupplier) {
        console.error('没有选中的供应商');
        message.error(t('databases.suppliers.noSupplierSelected'));
        return;
      }

      // 更新供应商数据
      const updatedSupplier: Supplier = {
        ...currentSupplier,
        name: values.name,
        contact: values.contact,
        phone: values.phone,
        email: values.email,
        address: values.address,
        products: values.products ? values.products.split(',').map((item: string) => item.trim()) : [],
        supplierType: values.supplierType,
        region: values.region,
        cooperationStatus: values.cooperationStatus,
        rating: values.rating,
      };

      console.log('准备更新供应商:', updatedSupplier);

      // 调用更新服务
      const updatedData = await updateSupplierService(currentSupplier.id, updatedSupplier);
      console.log('更新服务返回数据:', updatedData);

      // 更新Redux store
      dispatch(updateSupplier(updatedData));
      console.log('已更新Redux store');

      // 更新当前选中的供应商
      setCurrentSupplier(updatedData);

      // 关闭编辑表单
      setEditModalVisible(false);
      console.log('已关闭编辑表单');

      message.success(t('databases.suppliers.updateSuccess'));

      // 刷新数据列表
      console.log('开始刷新数据列表');
      await fetchSupplierList();
      console.log('数据列表刷新完成');
    } catch (error) {
      message.error(t('databases.suppliers.updateError'));
      console.error('更新供应商失败:', error);
      // 显示详细错误信息
      if (error instanceof Error) {
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
      }
    }
  };

  // 处理筛选条件变更
  const handleFilterChange = (field: string, value: string) => {
    setFilters({
      ...filters,
      [field]: value
    });
  };

  // 重置筛选条件
  const resetFilters = () => {
    setFilters({
      supplierType: '',
      region: '',
      cooperationStatus: '',
      rating: ''
    });
  };

  // 表格列定义
  const columns = [
    {
      title: t('databases.suppliers.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('databases.suppliers.contact'),
      dataIndex: 'contact',
      key: 'contact',
    },
    {
      title: t('databases.suppliers.phone'),
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: t('databases.suppliers.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('databases.suppliers.products'),
      dataIndex: 'products',
      key: 'products',
      render: (products: string[]) => (
        <span>
          {products.map(product => (
            <Tag color="blue" key={product}>
              {product}
            </Tag>
          ))}
        </span>
      ),
    },
    {
      title: t('databases.suppliers.rating'),
      dataIndex: 'rating',
      key: 'rating',
      render: (rating: string) => (
        <RatingStars rating={rating} />
      ),
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status: 'synced' | 'local-only' | 'server-only') => (
        <SyncStatus status={status} />
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: Supplier) => (
        <Space size="small">
          <Tooltip title={t('common.edit')}>
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation(); // 阻止事件冒泡
                handleEditSupplier(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation(); // 阻止事件冒泡
                showDeleteConfirm(record.id);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 筛选选项
  const filterOptions = [
    {
      key: 'supplierType',
      label: t('databases.suppliers.supplierType'),
      type: 'select' as const,
      options: [
        { value: 'pv', label: t('databases.suppliers.pvSupplier') },
        { value: 'storage', label: t('databases.suppliers.storageSupplier') },
        { value: 'inverter', label: t('databases.suppliers.inverterSupplier') },
        { value: 'other', label: t('databases.suppliers.otherSupplier') }
      ]
    },
    {
      key: 'region',
      label: t('databases.suppliers.region'),
      type: 'select' as const,
      options: [
        { value: 'japan', label: t('databases.suppliers.japan') },
        { value: 'china', label: t('databases.suppliers.china') },
        { value: 'other', label: t('databases.suppliers.other') }
      ]
    },
    {
      key: 'cooperationStatus',
      label: t('databases.suppliers.cooperationStatus'),
      type: 'select' as const,
      options: [
        { value: 'cooperating', label: t('databases.suppliers.cooperating') },
        { value: 'negotiating', label: t('databases.suppliers.negotiating') }
      ]
    },
    {
      key: 'rating',
      label: t('databases.suppliers.rating'),
      type: 'select' as const,
      options: [
        { value: '5', label: t('databases.suppliers.rating5') },
        { value: '4', label: t('databases.suppliers.rating4') },
        { value: '3', label: t('databases.suppliers.rating3') }
      ]
    }
  ];

  // 处理搜索
  const handleSearch = () => {
    console.log('搜索条件:', filters);
    // 这里可以添加搜索逻辑
  };

  // 渲染筛选条件
  const renderFilters = () => (
    <FilterForm
      filters={filters}
      filterOptions={filterOptions}
      onFilterChange={handleFilterChange}
      onSearch={handleSearch}
      onReset={resetFilters}
    />
  );

  // 渲染供应商列表
  const renderSupplierList = () => (
    <Card
      title={t('databases.suppliers.list')}
      extra={
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateSupplier}>
          {t('databases.suppliers.new')}
        </Button>
      }
    >
      {isLoading ? (
        <LoadingSpinner />
      ) : suppliers.length > 0 ? (
        <Table
          dataSource={suppliers as any[]}
          columns={columns}
          rowKey="id"
          onRow={(record) => ({
            onClick: () => handleSelectSupplier(record.id),
            style: { cursor: 'pointer' }
          })}
        />
      ) : (
        <EmptyState
          title={t('databases.suppliers.noData')}
          description={t('databases.suppliers.createPrompt')}
          actionText={t('databases.suppliers.new')}
          onAction={handleCreateSupplier}
        />
      )}
    </Card>
  );

  // 渲染供应商统计
  const renderSupplierStatistics = () => (
    <Card title={t('databases.suppliers.statistics')}>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title={t('databases.suppliers.typeDistribution')}>
            {/* 这里将来可以添加供应商类型分布图表 */}
            <div style={{ height: '200px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              {t('databases.suppliers.typeDistributionChart')}
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('databases.suppliers.regionDistribution')}>
            {/* 这里将来可以添加供应商地区分布图表 */}
            <div style={{ height: '200px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              {t('databases.suppliers.regionDistributionChart')}
            </div>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div>
      <PageHeader title={t('databases.suppliers.title')} />

      {renderFilters()}

      <Divider />

      {renderSupplierList()}

      <Divider />

      {renderSupplierStatistics()}

      {/* 创建供应商对话框 */}
      <Modal
        title={t('databases.suppliers.new')}
        open={createModalVisible}
        onOk={handleCreateFormSubmit}
        onCancel={() => setCreateModalVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <SupplierForm form={createForm} mode="create" />
      </Modal>

      {/* 编辑供应商对话框 */}
      <Modal
        title={t('databases.suppliers.edit')}
        open={editModalVisible}
        onOk={handleEditFormSubmit}
        onCancel={() => setEditModalVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <SupplierForm form={editForm} initialValues={currentSupplier || undefined} mode="edit" />
      </Modal>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        title={t('databases.suppliers.delete')}
        content={t('databases.suppliers.deleteConfirm')}
        visible={deleteModalVisible}
        onConfirm={handleDeleteSupplier}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </div>
  );
};

export default SupplierDatabase;
