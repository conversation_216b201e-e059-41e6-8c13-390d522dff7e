import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Divider,
  message,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  fetchDataStart,
  fetchElectricityPricesSuccess,
  addElectricityPrice,
  updateElectricityPrice,
  deleteElectricityPrice as deleteElectricityPriceAction
} from '../../store/slices/databasesSlice';
import {
  PageHeader,
  EmptyState,
  SyncStatus,
  ConfirmDialog,
  PriceRuleForm,
  SeasonalPolicyForm
} from '../../components/common';
import { ElectricityPriceChart } from '../../components/charts';
import {
  getElectricityPriceList,
  getElectricityPriceDetail,
  createElectricityPrice,
  updateElectricityPrice as updateElectricityPriceService,
  deleteElectricityPrice as deleteElectricityPriceService
} from '../../services/electricityPriceService';
import { ElectricityPrice } from '../../types/database';

const { Option } = Select;

const ElectricityPriceDatabase: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    electricityPrices,
    isLoading
  } = useAppSelector(state => state.databases);

  const [currentElectricityPrice, setCurrentElectricityPrice] = useState<ElectricityPrice | null>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string>('');
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [viewMode, setViewMode] = useState<'daily' | 'monthly'>('daily');
  const [policyType, setPolicyType] = useState<'fixed' | 'seasonal'>('fixed');
  const [currentSeasonId, setCurrentSeasonId] = useState<string>('');
  const [submitting, setSubmitting] = useState<boolean>(false);

  // 获取电价政策列表
  useEffect(() => {
    fetchElectricityPriceList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 获取电价政策列表
  const fetchElectricityPriceList = async () => {
    try {
      dispatch(fetchDataStart());
      const response = await getElectricityPriceList();
      dispatch(fetchElectricityPricesSuccess(response.items));
    } catch (error) {
      console.error('获取电价政策列表失败:', error);
      dispatch(fetchElectricityPricesSuccess([]));
    }
  };

  // 处理选择电价政策
  const handleSelectElectricityPrice = async (id: string) => {
    try {
      if (currentElectricityPrice && currentElectricityPrice.id === id) {
        return;
      }

      const data = await getElectricityPriceDetail(id);
      setCurrentElectricityPrice(data);

      // 根据政策类型设置视图模式
      if (data.policyType === 'seasonal') {
        setViewMode('monthly');
        // 如果有季节政策，设置当前季节ID
        if (data.seasonalPolicies && data.seasonalPolicies.length > 0) {
          setCurrentSeasonId(data.seasonalPolicies[0].id);
        }
      } else {
        setViewMode('daily');
      }
    } catch (error) {
      console.error('获取电价政策详情失败:', error);
      message.error(t('electricityPrice.loadError'));
    }
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (id: string) => {
    setDeleteItemId(id);
    setDeleteModalVisible(true);
  };

  // 处理确认删除
  const handleConfirmDelete = async () => {
    const id = deleteItemId;
    try {
      // 如果当前选中的数据被删除，清空当前数据
      if (currentElectricityPrice && currentElectricityPrice.id === id) {
        setCurrentElectricityPrice(null);
      }

      // 更新Redux状态
      dispatch(deleteElectricityPriceAction(id));

      // 调用删除服务
      await deleteElectricityPriceService(id);

      message.success(t('electricityPrice.deleteSuccess'));

      // 刷新数据列表
      await fetchElectricityPriceList();

      // 关闭对话框
      setDeleteModalVisible(false);
    } catch (error) {
      message.error(t('electricityPrice.deleteError'));
      console.error('删除电价政策失败:', error);
    }
  };

  // 处理取消删除
  const handleCancelDelete = () => {
    setDeleteModalVisible(false);
  };

  // 处理创建电价政策
  const handleCreateElectricityPrice = () => {
    createForm.resetFields();
    setPolicyType('fixed');
    setCreateModalVisible(true);
  };

  // 处理提交创建表单
  const handleCreateFormSubmit = async () => {
    try {
      // 设置提交状态为true
      setSubmitting(true);

      console.log('开始验证创建表单字段...');
      const values = await createForm.validateFields();
      console.log('创建表单验证成功，表单数据:', values);

      // 创建新的电价政策
      const newElectricityPrice: Partial<ElectricityPrice> = {
        name: values.name,
        region: values.region,
        policyType: values.policyType,
      };

      // 根据政策类型设置规则
      if (values.policyType === 'fixed') {
        console.log('固定电价政策，规则数据:', values.rules);
        newElectricityPrice.rules = values.rules || [];
        newElectricityPrice.seasonalPolicies = [];
      } else {
        console.log('季节性电价政策，季节数据:', values.seasonalPolicies);
        newElectricityPrice.rules = [];
        newElectricityPrice.seasonalPolicies = values.seasonalPolicies || [];
      }

      console.log('准备创建电价政策，数据:', newElectricityPrice);

      // 调用创建服务
      const createdData = await createElectricityPrice(newElectricityPrice);
      console.log('电价政策创建服务调用成功');

      // 更新Redux store
      dispatch(addElectricityPrice(createdData));
      console.log('Redux store更新成功');

      // 关闭创建表单
      setCreateModalVisible(false);
      createForm.resetFields();

      message.success(t('electricityPrice.createSuccess'));

      // 刷新数据列表
      await fetchElectricityPriceList();
    } catch (error) {
      message.error(t('electricityPrice.createError'));
      console.error('创建电价政策失败:', error);
    } finally {
      // 无论成功还是失败，都将提交状态设置为false
      setSubmitting(false);
    }
  };

  // 处理政策类型变更
  const handlePolicyTypeChange = (value: 'fixed' | 'seasonal') => {
    setPolicyType(value);
  };

  // 处理编辑电价政策
  const handleEditElectricityPrice = (record: ElectricityPrice) => {
    // 设置当前电价政策
    setCurrentElectricityPrice(record);

    // 设置政策类型
    setPolicyType(record.policyType || 'fixed');

    // 设置表单值
    editForm.setFieldsValue({
      name: record.name,
      region: record.region,
      policyType: record.policyType || 'fixed',
      rules: record.rules || [],
      seasonalPolicies: record.seasonalPolicies || [],
    });

    // 打印表单值，用于调试
    console.log('编辑表单设置的值:', {
      name: record.name,
      region: record.region,
      policyType: record.policyType || 'fixed',
      rules: record.rules || [],
      seasonalPolicies: record.seasonalPolicies || [],
    });

    setEditModalVisible(true);
  };

  // 处理提交编辑表单
  const handleEditFormSubmit = async () => {
    try {
      // 设置提交状态为true
      setSubmitting(true);

      console.log('开始验证表单字段...');
      const values = await editForm.validateFields();
      console.log('表单验证成功，表单数据:', values);

      if (!currentElectricityPrice) {
        console.error('没有选中的电价政策');
        setSubmitting(false);
        return;
      }

      // 更新数据
      const updatedData: ElectricityPrice = {
        ...currentElectricityPrice,
        name: values.name,
        region: values.region,
        policyType: values.policyType,
      };

      // 根据政策类型设置规则
      if (values.policyType === 'fixed') {
        console.log('固定电价政策，规则数据:', values.rules);
        updatedData.rules = values.rules || [];
        updatedData.seasonalPolicies = [];
      } else {
        console.log('季节性电价政策，季节数据:', values.seasonalPolicies);
        updatedData.rules = [];
        updatedData.seasonalPolicies = values.seasonalPolicies || [];
      }

      console.log('准备更新电价政策，数据:', updatedData);

      // 调用更新服务
      await updateElectricityPriceService(currentElectricityPrice.id, updatedData);
      console.log('电价政策更新服务调用成功');

      // 更新Redux store
      dispatch(updateElectricityPrice(updatedData));
      console.log('Redux store更新成功');

      // 更新当前选中的数据
      setCurrentElectricityPrice(updatedData);

      // 关闭编辑表单
      setEditModalVisible(false);
      editForm.resetFields();

      message.success(t('electricityPrice.updateSuccess'));

      // 刷新数据列表
      await fetchElectricityPriceList();
    } catch (error) {
      message.error(t('electricityPrice.updateError'));
      console.error('更新电价政策失败:', error);
    } finally {
      // 无论成功还是失败，都将提交状态设置为false
      setSubmitting(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('electricityPrice.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('electricityPrice.region'),
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: t('electricityPrice.policyType'),
      dataIndex: 'policyType',
      key: 'policyType',
      render: (type: 'fixed' | 'seasonal') => (
        <Tag color={type === 'fixed' ? 'blue' : 'green'}>
          {type === 'fixed' ? t('electricityPrice.fixed') : t('electricityPrice.seasonal')}
        </Tag>
      ),
    },
    {
      title: t('electricityPrice.rulesCount'),
      key: 'rulesCount',
      render: (_: any, record: ElectricityPrice) => {
        if (record.policyType === 'fixed') {
          return record.rules?.length || 0;
        } else {
          return record.seasonalPolicies?.length || 0;
        }
      },
    },
    {
      title: t('electricityPrice.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleDateString(),
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status: 'synced' | 'local-only' | 'server-only') => (
        <SyncStatus status={status} />
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: ElectricityPrice) => (
        <Space size="small">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation(); // 阻止事件冒泡
              handleEditElectricityPrice(record);
            }}
          />
          <Button
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation(); // 阻止事件冒泡
              showDeleteConfirm(record.id);
            }}
          />
        </Space>
      ),
    },
  ];

  // 渲染数据统计
  const renderDataStatistics = () => {
    if (!currentElectricityPrice) return null;

    return (
      <div style={{ marginTop: 24 }}>
        <Divider orientation="left">{t('electricityPrice.dataStatistics')}</Divider>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
          <Card title={t('electricityPrice.name')} style={{ width: 300 }}>
            {currentElectricityPrice.name}
          </Card>
          <Card title={t('electricityPrice.region')} style={{ width: 300 }}>
            {currentElectricityPrice.region}
          </Card>
          <Card title={t('electricityPrice.policyType')} style={{ width: 300 }}>
            <Tag color={currentElectricityPrice.policyType === 'fixed' ? 'blue' : 'green'}>
              {currentElectricityPrice.policyType === 'fixed'
                ? t('electricityPrice.fixed')
                : t('electricityPrice.seasonal')}
            </Tag>
          </Card>
          {currentElectricityPrice.policyType === 'fixed' ? (
            <Card title={t('electricityPrice.rulesCount')} style={{ width: 300 }}>
              {currentElectricityPrice.rules?.length || 0}
            </Card>
          ) : (
            <Card title={t('electricityPrice.seasonsCount')} style={{ width: 300 }}>
              {currentElectricityPrice.seasonalPolicies?.length || 0}
            </Card>
          )}
        </div>
      </div>
    );
  };

  // 渲染季节选择器
  const renderSeasonSelector = () => {
    if (!currentElectricityPrice) {
      return null;
    }

    // 如果是统一政策类型
    if (currentElectricityPrice.policyType === 'fixed') {
      return (
        <Select
          value="unified"
          disabled
          style={{ width: 150, marginLeft: 16 }}
        >
          <Option value="unified">{t('electricityPrice.fixed')}</Option>
        </Select>
      );
    }

    // 如果是分季节政策类型但没有季节数据
    if (!currentElectricityPrice.seasonalPolicies || currentElectricityPrice.seasonalPolicies.length === 0) {
      return (
        <Select
          disabled
          value="none"
          style={{ width: 150, marginLeft: 16 }}
        >
          <Option value="none">{t('electricityPrice.noSeasons')}</Option>
        </Select>
      );
    }

    // 分季节政策类型且有季节数据
    return (
      <Select
        value={currentSeasonId || currentElectricityPrice.seasonalPolicies[0].id}
        onChange={setCurrentSeasonId}
        style={{ width: 150, marginLeft: 16 }}
      >
        {currentElectricityPrice.seasonalPolicies.map(season => (
          <Option key={season.id} value={season.id}>
            {season.name}
            {season.months && season.months.length > 0 &&
              ` (${season.months.join('、')}月)`}
          </Option>
        ))}
      </Select>
    );
  };

  return (
    <div>
      <PageHeader
        title={t('electricityPrice.title')}
        extra={[
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateElectricityPrice}
          >
            {t('electricityPrice.create')}
          </Button>,
        ]}
      />

      <Card title={t('electricityPrice.policyList')} style={{ marginBottom: 16 }}>
        <Table
          rowKey="id"
          columns={columns}
          dataSource={electricityPrices}
          loading={isLoading}
          pagination={{ pageSize: 10 }}
          onRow={(record) => ({
            onClick: () => handleSelectElectricityPrice(record.id),
            style: {
              cursor: 'pointer',
              background: currentElectricityPrice?.id === record.id ? '#f0f7ff' : undefined
            }
          })}
        />
      </Card>

      <Card
        title={t('electricityPrice.priceVisualization')}
        extra={
          <Space>
            {currentElectricityPrice?.policyType === 'seasonal' ? (
              <Select
                value={viewMode}
                onChange={setViewMode}
                style={{ width: 120 }}
              >
                <Option value="monthly">{t('chart.month')}</Option>
              </Select>
            ) : (
              <Select
                value="daily"
                disabled
                style={{ width: 120 }}
              >
                <Option value="daily">{t('electricityPrice.fixed')}</Option>
              </Select>
            )}
            {renderSeasonSelector()}
          </Space>
        }
      >
        {currentElectricityPrice ? (
          <>
            <ElectricityPriceChart
              data={currentElectricityPrice}
              viewMode={viewMode}
              seasonId={currentSeasonId}
            />
            {renderDataStatistics()}
          </>
        ) : (
          <EmptyState
            title={t('electricityPrice.noPolicySelected')}
            description={t('electricityPrice.selectPolicyPrompt')}
          />
        )}
      </Card>

      {/* 创建表单模态框 */}
      <Modal
        title={t('electricityPrice.createPolicy')}
        open={createModalVisible}
        onOk={handleCreateFormSubmit}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        width={800}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        centered
        destroyOnClose
        confirmLoading={submitting}
      >
        <Form
          form={createForm}
          layout="vertical"
          initialValues={{ policyType: 'fixed' }}
        >
          <Form.Item
            name="name"
            label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.name')}</span>}
            rules={[{ required: true, message: t('electricityPrice.nameRequired') }]}
          >
            <Input placeholder={t('electricityPrice.namePlaceholder', '请输入政策名称')} />
          </Form.Item>
          <Form.Item
            name="region"
            label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.region')}</span>}
            rules={[{ required: true, message: t('electricityPrice.regionRequired') }]}
          >
            <Input placeholder={t('electricityPrice.regionPlaceholder', '请输入适用地区')} />
          </Form.Item>
          <Form.Item
            name="policyType"
            label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.policyType')}</span>}
            rules={[{ required: true, message: t('electricityPrice.policyTypeRequired') }]}
          >
            <Select onChange={handlePolicyTypeChange}>
              <Option value="fixed">{t('electricityPrice.fixed')}</Option>
              <Option value="seasonal">{t('electricityPrice.seasonal')}</Option>
            </Select>
          </Form.Item>

          {policyType === 'fixed' ? (
            <Form.Item
              name="rules"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.rules')}</span>}
              rules={[{ required: true, message: t('electricityPrice.rulesRequired') }]}
            >
              <PriceRuleForm />
            </Form.Item>
          ) : (
            <Form.Item
              name="seasonalPolicies"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.seasonalPolicies')}</span>}
              rules={[{ required: true, message: t('electricityPrice.seasonalPoliciesRequired') }]}
            >
              <SeasonalPolicyForm />
            </Form.Item>
          )}
        </Form>
      </Modal>

      {/* 编辑表单模态框 */}
      <Modal
        title={t('electricityPrice.editPolicy')}
        open={editModalVisible}
        onOk={handleEditFormSubmit}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        width={800}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        centered
        destroyOnClose
        confirmLoading={submitting}
        footer={[
          <Button key="back" onClick={() => {
            setEditModalVisible(false);
            editForm.resetFields();
          }}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleEditFormSubmit}
          >
            {t('common.confirm')}
          </Button>,
        ]}
      >
        <Form
          form={editForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.name')}</span>}
            rules={[{ required: true, message: t('electricityPrice.nameRequired') }]}
          >
            <Input placeholder={t('electricityPrice.namePlaceholder', '请输入政策名称')} />
          </Form.Item>
          <Form.Item
            name="region"
            label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.region')}</span>}
            rules={[{ required: true, message: t('electricityPrice.regionRequired') }]}
          >
            <Input placeholder={t('electricityPrice.regionPlaceholder', '请输入适用地区')} />
          </Form.Item>
          <Form.Item
            name="policyType"
            label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.policyType')}</span>}
            rules={[{ required: true, message: t('electricityPrice.policyTypeRequired') }]}
          >
            <Select onChange={handlePolicyTypeChange}>
              <Option value="fixed">{t('electricityPrice.fixed')}</Option>
              <Option value="seasonal">{t('electricityPrice.seasonal')}</Option>
            </Select>
          </Form.Item>

          {policyType === 'fixed' ? (
            <Form.Item
              name="rules"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.rules')}</span>}
              rules={[{ required: true, message: t('electricityPrice.rulesRequired') }]}
            >
              <PriceRuleForm />
            </Form.Item>
          ) : (
            <Form.Item
              name="seasonalPolicies"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.seasonalPolicies')}</span>}
              rules={[{ required: true, message: t('electricityPrice.seasonalPoliciesRequired') }]}
            >
              <SeasonalPolicyForm />
            </Form.Item>
          )}
        </Form>
      </Modal>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        title={t('electricityPrice.confirmDelete')}
        content={t('electricityPrice.deleteWarning')}
        visible={deleteModalVisible}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  );
};

export default ElectricityPriceDatabase;
