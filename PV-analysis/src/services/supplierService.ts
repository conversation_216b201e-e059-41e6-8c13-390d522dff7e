/**
 * 供应商服务
 * 用于处理供应商数据的CRUD操作
 */
import { v4 as uuidv4 } from 'uuid';
import { message } from 'antd';
import { get, post, put, del } from './api';
import { Supplier } from '../types/database';
import { cacheManager } from '../utils/dataSynchronization';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 供应商列表响应类型
interface SupplierListResponse {
  items: Supplier[];
  total: number;
}

/**
 * 获取供应商列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns Promise
 */
export const getSupplierList = async (
  page: number = 1,
  pageSize: number = 10
): Promise<SupplierListResponse> => {
  try {
    console.log('开始获取供应商列表');

    // 从服务器获取数据
    const response = await get<ApiResponse<SupplierListResponse>>('/suppliers', {
      page,
      pageSize
    });

    const serverItems = response.data.data.items;
    console.log('从服务器获取到数据条数:', serverItems.length);

    // 处理同步状态
    const processedItems = serverItems.map(item => {
      // 检查本地是否有该数据
      const key = `pv_supplier_${item.id}`;
      const localData = localStorage.getItem(key);

      // 确定同步状态
      let syncStatus: 'synced' | 'local-only' | 'server-only' = 'server-only';

      if (localData) {
        syncStatus = 'synced';
        // 合并本地数据
        try {
          const parsedLocalData = JSON.parse(localData);
          return {
            ...item,
            syncStatus
          };
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }

      // 如果没有本地数据或解析失败，使用服务器数据
      return {
        ...item,
        syncStatus
      };
    });

    // 获取本地数据
    const localItems: Supplier[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_supplier_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '');
          // 检查是否已经包含在服务器数据中
          const isInServer = processedItems.some(item => item.id === data.id);
          if (!isInServer) {
            // 确保数据有正确的字段
            const completeData = {
              ...data,
              syncStatus: 'local-only'
            };
            localItems.push(completeData);
          }
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }
    });

    // 合并服务器和本地数据
    const allItems = [...processedItems, ...localItems];
    console.log('合并后的数据条数:', allItems.length);

    return {
      items: allItems,
      total: allItems.length
    };
  } catch (error) {
    console.error('获取供应商列表失败:', error);

    // 如果服务器请求失败，尝试从本地获取数据
    const localItems: Supplier[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_supplier_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '');
          // 确保数据有正确的字段
          const completeData = {
            ...data,
            syncStatus: 'local-only'
          };
          localItems.push(completeData);
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }
    });

    console.log('从本地获取到数据条数:', localItems.length);

    return {
      items: localItems,
      total: localItems.length
    };
  }
};

/**
 * 获取供应商详情
 * @param id 供应商ID
 * @returns Promise
 */
export const getSupplierDetail = async (id: string): Promise<Supplier> => {
  try {
    console.log('开始获取供应商详情, ID:', id);

    // 检查缓存
    try {
      const cachedData = cacheManager.getItem(id);
      if (cachedData) {
        console.log('从缓存获取到数据, ID:', id);
        return cachedData as Supplier;
      }
    } catch (cacheError) {
      console.error('从缓存获取数据失败:', cacheError);
    }

    // 检查本地存储
    const key = `pv_supplier_${id}`;
    const localData = localStorage.getItem(key);
    let isServerData = false;

    // 如果本地有数据，检查是否是服务器数据
    if (localData) {
      try {
        const parsedData = JSON.parse(localData);
        if (parsedData.syncStatus === 'server-only') {
          isServerData = true;
          console.log('本地数据标记为服务器数据，需要从服务器获取');
        } else {
          console.log('从本地获取到数据, ID:', id, '同步状态:', parsedData.syncStatus);

          // 添加到缓存
          try {
            cacheManager.addItem(id, parsedData);
          } catch (cacheError) {
            console.error('添加到缓存失败:', cacheError);
          }

          return parsedData;
        }
      } catch (parseError) {
        console.error('解析本地数据失败:', parseError);
        isServerData = true;
      }
    } else {
      // 如果本地没有数据，则认为是服务器数据
      isServerData = true;
      console.log('本地没有数据，认为是服务器数据');
    }

    // 如果是服务器数据或本地数据无效，尝试从服务器获取
    if (isServerData || !localData) {
      console.log('尝试从服务器获取数据, ID:', id);
      try {
        // 从服务器获取数据
        const response = await get<ApiResponse<Supplier>>(`/suppliers/${id}`);
        const serverData = response.data.data;

        // 验证服务器数据的有效性
        if (serverData) {
          console.log('从服务器获取到数据:', serverData.name);

          // 确保数据有正确的字段
          const completeData = {
            ...serverData,
            syncStatus: 'synced'
          };

          console.log('处理后的服务器数据:', completeData);

          // 保存到本地
          localStorage.setItem(key, JSON.stringify(completeData));

          // 添加到缓存
          try {
            cacheManager.addItem(id, completeData);
          } catch (cacheError) {
            console.error('添加到缓存失败:', cacheError);
          }

          return completeData;
        } else {
          throw new Error('服务器数据无效');
        }
      } catch (serverError) {
        console.error('从服务器获取数据失败:', serverError);
        throw serverError;
      }
    }

    throw new Error('无法获取供应商详情');
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    throw error;
  }
};

/**
 * 创建供应商
 * @param data 供应商数据
 * @returns Promise
 */
export const createSupplier = async (data: Partial<Supplier>): Promise<Supplier> => {
  try {
    // 生成ID
    const id = uuidv4();
    const now = new Date().toISOString();

    // 创建新的供应商
    console.log('创建供应商，输入数据:', data);

    const newSupplier: Supplier = {
      id,
      name: data.name || '',
      contact: data.contact || '',
      phone: data.phone || '',
      email: data.email || '',
      address: data.address || '',
      products: data.products || [],
      supplierType: data.supplierType || '',
      region: data.region || '',
      cooperationStatus: data.cooperationStatus || '',
      rating: data.rating || '',
      createdAt: now,
      updatedAt: now,
      syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
    };

    console.log('创建的新供应商:', newSupplier);

    // 保存到本地
    const key = `pv_supplier_${id}`;
    localStorage.setItem(key, JSON.stringify(newSupplier));

    // 尝试同步到服务器
    try {
      const response = await post<ApiResponse<Supplier>>('/suppliers', newSupplier);
      const serverData = response.data.data;

      // 更新同步状态
      newSupplier.syncStatus = 'synced';
      localStorage.setItem(key, JSON.stringify(newSupplier));

      // 添加到缓存
      try {
        cacheManager.addItem(id, newSupplier);
      } catch (cacheError) {
        console.error('添加到缓存失败:', cacheError);
      }

      message.success('供应商已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      message.warning('同步到服务器失败，数据将保存在本地');
    }

    return newSupplier;
  } catch (error) {
    console.error('创建供应商失败:', error);
    throw error;
  }
};

/**
 * 更新供应商
 * @param id 供应商ID
 * @param data 更新的数据
 * @returns Promise
 */
export const updateSupplier = async (id: string, data: Partial<Supplier>): Promise<Supplier> => {
  try {
    // 获取现有数据
    const key = `pv_supplier_${id}`;
    const localData = localStorage.getItem(key);
    let existingData: Supplier;

    if (localData) {
      existingData = JSON.parse(localData);
    } else {
      // 如果本地没有数据，尝试从服务器获取
      const response = await get<ApiResponse<Supplier>>(`/suppliers/${id}`);
      existingData = response.data.data;
    }

    console.log('更新供应商，现有数据:', existingData);
    console.log('更新供应商，新数据:', data);

    // 更新数据
    const updatedData: Supplier = {
      ...existingData,
      ...data,
      id, // 确保ID不变
      updatedAt: new Date().toISOString(),
      syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
    };

    console.log('更新后的数据:', updatedData);

    // 保存到本地
    localStorage.setItem(key, JSON.stringify(updatedData));

    // 尝试同步到服务器
    try {
      const response = await put<ApiResponse<Supplier>>(`/suppliers/${id}`, updatedData);
      const serverData = response.data.data;

      // 更新同步状态
      updatedData.syncStatus = 'synced';
      localStorage.setItem(key, JSON.stringify(updatedData));

      // 更新缓存
      try {
        cacheManager.addItem(id, updatedData);
      } catch (cacheError) {
        console.error('更新缓存失败:', cacheError);
      }

      message.success('供应商已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      message.warning('同步到服务器失败，数据将保存在本地');
    }

    return updatedData;
  } catch (error) {
    console.error('更新供应商失败:', error);
    throw error;
  }
};

/**
 * 删除供应商
 * @param id 供应商ID
 * @returns Promise
 */
export const deleteSupplier = async (id: string): Promise<boolean> => {
  try {
    console.log('开始删除供应商, ID:', id);

    // 从本地删除
    const key = `pv_supplier_${id}`;
    localStorage.removeItem(key);

    // 从缓存删除
    try {
      cacheManager.removeItem(id);
    } catch (cacheError) {
      console.error('从缓存删除失败:', cacheError);
    }

    // 尝试从服务器删除
    try {
      await del<ApiResponse<boolean>>(`/suppliers/${id}`);
      console.log('已从服务器删除供应商, ID:', id);
      message.success('供应商已成功删除');
    } catch (deleteError) {
      console.error('从服务器删除失败:', deleteError);
      message.warning('从服务器删除失败，但已从本地删除');
    }

    return true;
  } catch (error) {
    console.error('删除供应商失败:', error);
    throw error;
  }
};
