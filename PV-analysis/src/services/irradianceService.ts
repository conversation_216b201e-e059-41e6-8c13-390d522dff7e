/**
 * 光照数据服务
 * 用于处理光照数据的API请求
 */
import { get, post, put, del, uploadFile } from './api';
import { IrradianceData } from '../types/database';
import { cacheManager } from '../utils/dataSynchronization';
import { v4 as uuidv4 } from 'uuid';
import { message } from 'antd';
import * as LZString from 'lz-string';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 光照数据列表响应类型
interface IrradianceListResponse {
  items: IrradianceData[];
  total: number;
}

/**
 * 验证数据集是否有效
 * @param data 数据集
 * @param checkServer 是否检查服务器数据
 * @returns 是否有效
 */
export const isValidIrradianceData = async (data: IrradianceData, checkServer: boolean = false): Promise<boolean> => {
  try {
    // 检查本地数据是否有效
    const isLocalValid = data.data && Array.isArray(data.data) && data.data.length > 0;

    // 如果本地数据有效，直接返回true
    if (isLocalValid) {
      return true;
    }

    // 如果不需要检查服务器数据，直接返回本地验证结果
    if (!checkServer) {
      return isLocalValid;
    }

    // 尝试从服务器验证数据
    try {
      const response = await get<ApiResponse<IrradianceData>>(`/irradiance/${data.id}/validate`);
      return response.data.success && response.data.data &&
             Array.isArray(response.data.data.data) &&
             response.data.data.data.length > 0;
    } catch (serverError) {
      console.error('从服务器验证数据失败:', serverError);
      // 服务器验证失败，返回本地验证结果
      return isLocalValid;
    }
  } catch (error) {
    console.error('验证数据集有效性失败:', error);
    return false;
  }
};

/**
 * 获取光照数据列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns Promise
 */
export const getIrradianceList = async (
  page: number = 1,
  pageSize: number = 10
): Promise<IrradianceListResponse> => {
  try {
    console.log('开始获取光照数据列表');

    // 从服务器获取数据
    const response = await get<ApiResponse<IrradianceListResponse>>('/irradiance', {
      page,
      pageSize
    });

    const serverItems = response.data.data.items;
    console.log('从服务器获取到数据条数:', serverItems.length);

    // 验证服务器数据的有效性
    const validServerItems: IrradianceData[] = [];
    for (const item of serverItems) {
      try {
        // 检查服务器数据是否有详细数据
        if (item.data && Array.isArray(item.data) && item.data.length > 0) {
          validServerItems.push(item);
        } else {
          // 尝试获取详细数据
          try {
            const detailResponse = await get<ApiResponse<IrradianceData>>(`/irradiance/${item.id}`);
            const detailData = detailResponse.data.data;
            if (detailData && detailData.data && Array.isArray(detailData.data) && detailData.data.length > 0) {
              validServerItems.push(detailData);
            } else {
              console.log(`服务器数据 ${item.id} 无效，将被忽略`);
              // 从服务器删除无效数据
              try {
                await del<ApiResponse<boolean>>(`/irradiance/${item.id}`);
                console.log(`已从服务器删除无效数据 ${item.id}`);
              } catch (deleteError) {
                console.error(`删除服务器无效数据 ${item.id} 失败:`, deleteError);
              }
            }
          } catch (detailError) {
            console.error(`获取服务器数据 ${item.id} 详情失败:`, detailError);
          }
        }
      } catch (error) {
        console.error(`验证服务器数据 ${item.id} 失败:`, error);
      }
    }

    console.log('有效服务器数据条数:', validServerItems.length);

    // 从localStorage中获取所有光照数据
    const localItems: IrradianceData[] = [];
    const invalidLocalIds: string[] = [];
    const prefix = 'pv_irradiance_';

    // 遍历localStorage获取本地数据
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        // 跳过压缩数据，这些数据不应该被直接解析
        if (key.includes('compressed') || key.includes('detail')) {
          console.log(`跳过压缩数据键: ${key}`);
          continue;
        }

        try {
          const rawData = localStorage.getItem(key);
          if (!rawData) continue;

          const data = JSON.parse(rawData);
          if (data && data.id && data.name) {
            // 如果ID以server-开头，跳过（这是从服务器下载的数据）
            if (data.id.startsWith('server-')) continue;

            // 验证本地数据的有效性
            const isLocalValid = data.data && Array.isArray(data.data) && data.data.length > 0;

            if (isLocalValid) {
              // 设置为本地数据
              data.syncStatus = 'local-only';
              localItems.push(data);
            } else {
              // 记录无效的本地数据ID，稍后删除
              invalidLocalIds.push(data.id);
              console.log(`本地数据 ${data.id} 无效，将被删除`);
            }
          }
        } catch (e) {
          console.error('解析localStorage数据失败:', key, e);
          // 只删除非压缩数据的无法解析项
          if (!key.includes('compressed') && !key.includes('detail')) {
            try {
              localStorage.removeItem(key);
              console.log(`已删除无法解析的本地数据: ${key}`);
            } catch (removeError) {
              console.error(`删除无法解析的本地数据 ${key} 失败:`, removeError);
            }
          } else {
            console.log(`跳过删除压缩数据键: ${key}`);
          }
        }
      }
    }

    // 删除无效的本地数据
    for (const id of invalidLocalIds) {
      try {
        const key = `${prefix}${id}`;
        localStorage.removeItem(key);
        console.log(`已删除无效的本地数据: ${id}`);
      } catch (removeError) {
        console.error(`删除无效的本地数据 ${id} 失败:`, removeError);
      }
    }

    console.log('有效本地数据条数:', localItems.length);

    // 合并本地和服务器数据
    const allItems: IrradianceData[] = [];

    // 先添加本地数据
    allItems.push(...localItems);

    // 检查本地数据是否已经存在于服务器中
    for (const serverItem of validServerItems) {
      // 检查是否已经在本地存在
      const localItem = localItems.find(item =>
        item.id === serverItem.id || (
          item.name === serverItem.name &&
          item.location === serverItem.location &&
          item.year === serverItem.year
        )
      );

      if (localItem) {
        // 如果本地已存在，更新同步状态为已同步
        const index = allItems.findIndex(item => item.id === localItem.id);
        if (index !== -1) {
          // 检查本地存储中的同步状态
          const key = `pv_irradiance_${localItem.id}`;
          const storedData = localStorage.getItem(key);
          if (storedData) {
            try {
              const parsedData = JSON.parse(storedData);
              // 如果本地存储中已标记为已同步，则保持已同步状态
              if (parsedData.syncStatus === 'synced') {
                allItems[index].syncStatus = 'synced';
                console.log('保持已同步状态，因为本地存储中已标记为已同步, ID:', localItem.id);
              } else {
                // 如果本地和服务器都有数据，则标记为已同步
                allItems[index].syncStatus = 'synced';
                // 更新本地存储中的同步状态
                parsedData.syncStatus = 'synced';
                localStorage.setItem(key, JSON.stringify(parsedData));
                console.log('更新本地存储中的同步状态为已同步, ID:', localItem.id);
              }
            } catch (e) {
              console.error('解析localStorage数据失败:', e);
              allItems[index].syncStatus = 'synced';
            }
          } else {
            allItems[index].syncStatus = 'synced';
          }
        }
      } else {
        // 检查本地存储中是否有此数据的同步状态
        const key = `pv_irradiance_${serverItem.id}`;
        const storedData = localStorage.getItem(key);
        if (storedData) {
          try {
            const parsedData = JSON.parse(storedData);
            // 如果本地存储中已标记为已同步，则保持已同步状态
            if (parsedData.syncStatus === 'synced') {
              serverItem.syncStatus = 'synced';
              console.log('服务器数据保持已同步状态，因为本地存储中已标记为已同步, ID:', serverItem.id);
            } else {
              serverItem.syncStatus = 'server-only';
            }
          } catch (e) {
            console.error('解析localStorage数据失败:', e);
            serverItem.syncStatus = 'server-only';
          }
        } else {
          // 如果本地不存在，添加服务器数据
          serverItem.syncStatus = 'server-only';
        }
        allItems.push(serverItem);
      }
    }

    console.log('获取到的总有效数据条数:', allItems.length, '本地数据:', localItems.length, '服务器数据:', validServerItems.length);

    // 按创建时间排序，最新的在前面
    allItems.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    });

    return {
      items: allItems,
      total: allItems.length,
    };
  } catch (error) {
    console.error('获取光照数据列表失败:', error);

    // 如果服务器请求失败，尝试从本地获取数据
    console.log('服务器请求失败，尝试从本地获取数据');

    // 从localStorage中获取所有光照数据
    const localItems: IrradianceData[] = [];
    const invalidLocalIds: string[] = [];
    const prefix = 'pv_irradiance_';

    // 遍历localStorage获取本地数据
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        // 跳过压缩数据，这些数据不应该被直接解析
        if (key.includes('compressed') || key.includes('detail')) {
          console.log(`跳过压缩数据键: ${key}`);
          continue;
        }

        try {
          const rawData = localStorage.getItem(key);
          if (!rawData) continue;

          const data = JSON.parse(rawData);
          if (data && data.id && data.name) {
            // 验证本地数据的有效性
            const isLocalValid = data.data && Array.isArray(data.data) && data.data.length > 0;

            if (isLocalValid) {
              // 设置为本地数据
              data.syncStatus = 'local-only';
              localItems.push(data);
            } else {
              // 记录无效的本地数据ID，稍后删除
              invalidLocalIds.push(data.id);
              console.log(`本地数据 ${data.id} 无效，将被删除`);
            }
          }
        } catch (e) {
          console.error('解析localStorage数据失败:', key, e);
          // 只删除非压缩数据的无法解析项
          if (!key.includes('compressed') && !key.includes('detail')) {
            try {
              localStorage.removeItem(key);
              console.log(`已删除无法解析的本地数据: ${key}`);
            } catch (removeError) {
              console.error(`删除无法解析的本地数据 ${key} 失败:`, removeError);
            }
          } else {
            console.log(`跳过删除压缩数据键: ${key}`);
          }
        }
      }
    }

    // 删除无效的本地数据
    for (const id of invalidLocalIds) {
      try {
        const key = `${prefix}${id}`;
        localStorage.removeItem(key);
        console.log(`已删除无效的本地数据: ${id}`);
      } catch (removeError) {
        console.error(`删除无效的本地数据 ${id} 失败:`, removeError);
      }
    }

    // 按创建时间排序，最新的在前面
    localItems.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    });

    return {
      items: localItems,
      total: localItems.length,
    };
  }
};

/**
 * 获取光照数据详情
 * @param id 光照数据ID
 * @returns Promise
 */
export const getIrradianceDetail = async (id: string): Promise<IrradianceData> => {
  try {
    console.log('开始获取光照数据详情, ID:', id);

    // 先从缓存获取
    const cachedData = cacheManager.getItem(id);
    if (cachedData) {
      console.log('从缓存中获取到数据, ID:', id, '同步状态:', cachedData.syncStatus);

      // 验证缓存数据的有效性
      const isValid = cachedData.data && Array.isArray(cachedData.data) && cachedData.data.length > 0;

      if (isValid) {
        // 如果是有效数据，直接返回
        console.log('缓存中的数据有效，直接返回，同步状态:', cachedData.syncStatus);
        return cachedData;
      } else {
        // 如果是服务器数据且标记为已同步，但数据为空，尝试从服务器重新获取
        if (cachedData.syncStatus === 'synced' && (!cachedData.data || cachedData.data.length === 0)) {
          console.log('缓存中的数据标记为已同步但数据为空，尝试从服务器重新获取完整数据');
          // 不立即返回，继续执行后面的代码从服务器获取
        }
        // 如果是服务器数据，但解压缩失败导致标记为仅服务器，尝试从服务器重新获取
        else if (cachedData.syncStatus === 'server-only') {
          console.log('缓存中的数据标记为仅服务器，尝试从服务器重新获取完整数据');
          // 不立即返回，继续执行后面的代码从服务器获取
        } else {
          console.log('缓存数据无效，将从其他来源获取, ID:', id);
          // 从缓存中移除无效数据
          cacheManager.removeItem(id);
        }
      }
    }

    console.log('缓存中没有有效数据，尝试从localStorage获取, ID:', id);

    // 检查是否是服务器数据
    // 首先检查是否有明确的syncStatus标记为server-only
    const metaKey = `pv_irradiance_${id}`;
    const detailKey = `pv_irradiance_detail_${id}`;
    const selectedItem = localStorage.getItem(metaKey);
    let isServerData = false;
    let localData: IrradianceData | null = null;

    if (selectedItem) {
      try {
        // 解析元数据
        const metaData = JSON.parse(selectedItem);
        isServerData = metaData.syncStatus === 'server-only';
        console.log('从localStorage检查数据同步状态:', metaData.syncStatus);

        // 尝试获取压缩的详细数据
        const compressedDetailData = localStorage.getItem(detailKey);

        if (compressedDetailData) {
          try {
            console.log('发现压缩的详细数据，尝试解压...');
            // 解压详细数据
            const decompressedData = LZString.decompressFromUTF16(compressedDetailData);
            if (decompressedData) {
              const detailData = JSON.parse(decompressedData);
              console.log('成功解压详细数据，数据点数:', detailData.length);

              // 将详细数据合并回元数据
              metaData.data = detailData;
              localData = metaData;

              // 验证本地数据的有效性
              const isLocalValid = localData.data && Array.isArray(localData.data) && localData.data.length > 0;
              if (!isLocalValid) {
                console.log('解压后的数据无效, ID:', id);
                // 如果本地数据无效，尝试从服务器获取
                isServerData = true;
                localData = null;
              }
            } else {
              console.warn('详细数据解压失败，将尝试从服务器获取完整数据');
              isServerData = true;
              // 保留元数据，但标记为需要从服务器获取详细数据
              localData = metaData;
            }
          } catch (decompressionError) {
            console.error('解压详细数据失败:', decompressionError);
            console.warn('将尝试从服务器获取完整数据');
            isServerData = true;
            // 保留元数据，但标记为需要从服务器获取详细数据
            localData = metaData;
          }
        } else {
          // 没有压缩的详细数据，检查元数据中是否有详细数据
          if (metaData.data && Array.isArray(metaData.data) && metaData.data.length > 0) {
            // 元数据中有详细数据，直接使用
            localData = metaData;
          } else {
            // 元数据中没有详细数据，需要从服务器获取
            console.log('本地没有详细数据, ID:', id);
            isServerData = true;
            localData = metaData; // 保留元数据
          }
        }
      } catch (e) {
        console.error('解析localStorage数据失败:', e);
        // 删除无法解析的数据
        localStorage.removeItem(metaKey);
        localStorage.removeItem(detailKey);
        console.log('已删除无法解析的本地数据, ID:', id);
        localData = null;
        isServerData = true;
      }
    } else {
      // 如果本地没有数据，则认为是服务器数据
      isServerData = true;
      console.log('本地没有数据，认为是服务器数据');
    }

    // 如果是服务器数据或本地数据无效，尝试从服务器获取
    if (isServerData || !localData) {
      console.log('尝试从服务器获取数据, ID:', id);
      try {
        // 从服务器获取数据
        const response = await get<ApiResponse<IrradianceData>>(`/irradiance/${id}`);
        const serverData = response.data.data;

        // 验证服务器数据的有效性
        const isServerValid = serverData && serverData.data && Array.isArray(serverData.data) && serverData.data.length > 0;

        if (isServerValid) {
          console.log('从服务器获取到有效数据:', serverData.name, '数据条数:', serverData.data?.length || 0);

          // 设置同步状态
          // 如果本地有完整数据，则标记为已同步
          if (localData && localData.data && Array.isArray(localData.data) && localData.data.length > 0) {
            serverData.syncStatus = 'synced';
          } else {
            // 检查localStorage中是否有标记为已同步的数据
            const key = `pv_irradiance_${id}`;
            const storedData = localStorage.getItem(key);
            if (storedData) {
              try {
                const parsedData = JSON.parse(storedData);
                if (parsedData.syncStatus === 'synced') {
                  // 如果本地存储中已标记为已同步，则保持已同步状态
                  serverData.syncStatus = 'synced';
                  console.log('保持已同步状态，因为本地存储中已标记为已同步');
                } else {
                  serverData.syncStatus = 'server-only';
                }
              } catch (e) {
                console.error('解析localStorage数据失败:', e);
                serverData.syncStatus = 'server-only';
              }
            } else {
              serverData.syncStatus = 'server-only';
            }
          }

          // 设置同步状态为'synced'，表示已同步
          serverData.syncStatus = 'synced';
          console.log('设置服务器数据同步状态为"已同步"');

          // 添加到缓存 - 使用改进的缓存管理器
          try {
            // 直接使用缓存管理器添加数据，它会处理缓存满的情况
            cacheManager.addItem(id, serverData);
            console.log('数据已添加到缓存，同步状态:', serverData.syncStatus);

            // 显示缓存统计信息
            const stats = cacheManager.getCacheStats();
            console.log(`缓存统计: ${stats.itemCount}个项目, 使用率: ${stats.usagePercentage.toFixed(2)}%, 已用空间: ${(stats.usedSpace/1024/1024).toFixed(2)}MB`);

            // 再次检查缓存中的数据同步状态
            const cachedData = cacheManager.getItem(id);
            if (cachedData) {
              console.log('缓存中的数据同步状态:', cachedData.syncStatus);
              if (cachedData.syncStatus !== 'synced') {
                console.log('缓存中的同步状态不正确，尝试再次更新');
                cachedData.syncStatus = 'synced';
                cacheManager.addItem(id, cachedData);
              }
            }
          } catch (cacheError) {
            console.error('添加数据到缓存失败:', cacheError);
            // 即使缓存失败，我们仍然返回数据，因为已经从服务器获取到了
            message.warning('本地缓存空间不足，部分数据可能无法保存');
          }

          return serverData;
        } else {
          console.log('服务器数据无效, ID:', id);

          // 从服务器删除无效数据
          try {
            await del<ApiResponse<boolean>>(`/irradiance/${id}`);
            console.log('已从服务器删除无效数据, ID:', id);
          } catch (deleteError) {
            console.error('删除服务器无效数据失败:', deleteError);
          }

          // 如果服务器数据无效，但本地有有效数据，使用本地数据
          if (localData && localData.data && Array.isArray(localData.data) && localData.data.length > 0) {
            console.log('使用有效的本地数据, ID:', id);
            localData.syncStatus = 'local-only';

            // 添加到缓存
            try {
              cacheManager.addItem(id, localData);
              console.log('本地数据已添加到缓存');
            } catch (cacheError) {
              console.error('添加本地数据到缓存失败:', cacheError);
            }

            return localData;
          }

          // 如果服务器和本地都没有有效数据，返回空数据
          console.log('服务器和本地都没有有效数据, ID:', id);
          return createEmptyData(id);
        }
      } catch (serverError) {
        console.error('从服务器获取数据失败:', serverError);

        // 如果服务器获取失败，但本地有有效数据，使用本地数据
        if (localData && localData.data && Array.isArray(localData.data) && localData.data.length > 0) {
          console.log('服务器获取失败，使用有效的本地数据, ID:', id);
          localData.syncStatus = 'local-only';

          // 添加到缓存
          try {
            cacheManager.addItem(id, localData);
            console.log('本地数据已添加到缓存');
          } catch (cacheError) {
            console.error('添加本地数据到缓存失败:', cacheError);
          }

          return localData;
        }

        // 如果服务器获取失败且本地没有有效数据，返回空数据
        console.log('服务器获取失败且本地没有有效数据, ID:', id);
        return createEmptyData(id);
      }
    }

    // 如果本地有有效数据，使用本地数据
    if (localData && localData.data && Array.isArray(localData.data) && localData.data.length > 0) {
      console.log('使用有效的本地数据, ID:', id);

      // 添加到缓存
      try {
        cacheManager.addItem(id, localData);
        console.log('本地数据已添加到缓存');

        // 显示缓存统计信息
        const stats = cacheManager.getCacheStats();
        console.log(`缓存统计: ${stats.itemCount}个项目, 使用率: ${stats.usagePercentage.toFixed(2)}%, 已用空间: ${(stats.usedSpace/1024/1024).toFixed(2)}MB`);
      } catch (cacheError) {
        console.error('添加本地数据到缓存失败:', cacheError);
      }

      console.log('获取到数据详情, ID:', id, '同步状态:', localData.syncStatus);
      return localData;
    }

    // 如果没有找到有效数据，返回空数据
    console.log('未找到有效数据, ID:', id);
    return createEmptyData(id);
  } catch (error) {
    console.error('获取光照数据详情失败:', error);
    throw error;
  }
};



// 创建空数据对象的辅助函数
const createEmptyData = (id: string): IrradianceData => {
  return {
    id,
    name: '未找到数据',
    location: '未知',
    latitude: 0,
    longitude: 0,
    year: new Date().getFullYear(),
    dataSize: 0,
    data: [],
    syncStatus: 'local-only',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
};

/**
 * 创建光照数据
 * @param data 光照数据
 * @returns Promise
 */
export const createIrradianceData = async (data: Omit<IrradianceData, 'id' | 'createdAt' | 'updatedAt'>): Promise<IrradianceData> => {
  try {
    console.log('开始创建光照数据:', data.name, '数据条数:', data.dataSize);

    // 生成ID和时间戳
    const newData: IrradianceData = {
      ...data,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      syncStatus: 'local-only', // 初始状态为本地
    };

    console.log('生成的新数据ID:', newData.id);

    // 检查数据大小
    const dataJson = JSON.stringify(newData);
    const dataSize = dataJson.length;
    console.log('原始数据大小:', dataSize, 'bytes');

    // 提取详细数据进行压缩存储
    const detailData = newData.data;
    const metaData = { ...newData };

    // 从元数据中移除详细数据
    metaData.data = [];

    // 保存元数据到localStorage
    const metaKey = `pv_irradiance_${newData.id}`;
    const metaJson = JSON.stringify(metaData);
    console.log('元数据大小:', metaJson.length, 'bytes');
    localStorage.setItem(metaKey, metaJson);

    // 压缩并保存详细数据
    if (detailData && detailData.length > 0) {
      try {
        console.log('尝试压缩并保存详细数据...');
        // 将详细数据转换为JSON字符串
        const detailJson = JSON.stringify(detailData);
        console.log('详细数据JSON字符串长度:', detailJson.length, 'bytes');

        // 使用LZ-string压缩数据
        const compressedData = LZString.compressToUTF16(detailJson);
        console.log('压缩后数据长度:', compressedData.length, 'bytes');
        console.log('压缩率:', ((1 - compressedData.length / detailJson.length) * 100).toFixed(2), '%');

        // 保存压缩数据
        const detailKey = `pv_irradiance_detail_${newData.id}`;
        localStorage.setItem(detailKey, compressedData);
        console.log('详细数据已压缩并保存到localStorage');
      } catch (compressionError) {
        console.error('压缩和保存详细数据失败:', compressionError);
        console.error('错误详情:', JSON.stringify(compressionError, Object.getOwnPropertyNames(compressionError)));
        console.warn('详细数据将只保存在服务器上，本地访问时需要从服务器获取');
      }
    }

    // 使用缓存管理器添加完整数据（用于当前会话）
    try {
      console.log('添加完整数据到缓存');
      cacheManager.addItem(newData.id, newData);

      // 显示缓存统计信息
      const stats = cacheManager.getCacheStats();
      console.log(`缓存统计: ${stats.itemCount}个项目, 使用率: ${stats.usagePercentage.toFixed(2)}%, 已用空间: ${(stats.usedSpace/1024/1024).toFixed(2)}MB`);
    } catch (cacheError) {
      console.error('添加数据到缓存失败:', cacheError);
      message.warning('本地缓存空间不足，部分数据可能无法保存');
    }

    console.log('数据已保存到本地，准备上传到服务器:', newData.id);

    // 尝试上传到服务器
    try {
      // 调用API上传数据
      const response = await post<ApiResponse<IrradianceData>>('/irradiance', newData);
      const serverData = response.data.data;

      console.log('服务器返回数据:', serverData);

      // 更新同步状态
      newData.syncStatus = 'synced';

      // 更新缓存
      try {
        cacheManager.addItem(newData.id, newData);
        console.log('已更新同步状态到缓存');
      } catch (updateError) {
        console.error('更新同步状态到缓存失败:', updateError);
        message.warning('更新数据同步状态失败');
      }

      console.log('数据已成功上传到服务器:', newData.id);
    } catch (uploadError) {
      console.error('上传到服务器失败，数据将保持本地状态:', uploadError);
      // 保持local-only状态
      message.error('上传到服务器失败，数据将保存在本地');
    }

    return newData;
  } catch (error) {
    console.error('创建光照数据失败:', error);
    throw error;
  }
};

/**
 * 更新光照数据
 * @param id 光照数据ID
 * @param data 光照数据
 * @returns Promise
 */
export const updateIrradianceData = async (id: string, data: Partial<IrradianceData>): Promise<IrradianceData> => {
  try {
    // 先从缓存获取
    const cachedData = cacheManager.getItem(id);
    if (!cachedData) {
      throw new Error('数据不存在');
    }

    // 更新数据
    const updatedData: IrradianceData = {
      ...cachedData,
      ...data,
      updatedAt: new Date().toISOString(),
      syncStatus: 'local-only',
    };

    // 更新缓存
    try {
      cacheManager.addItem(id, updatedData);
      console.log('数据已更新到缓存');

      // 显示缓存统计信息
      const stats = cacheManager.getCacheStats();
      console.log(`缓存统计: ${stats.itemCount}个项目, 使用率: ${stats.usagePercentage.toFixed(2)}%, 已用空间: ${(stats.usedSpace/1024/1024).toFixed(2)}MB`);
    } catch (cacheError) {
      console.error('更新数据到缓存失败:', cacheError);
      message.warning('本地缓存空间不足，部分数据可能无法保存');
    }

    console.log('数据已更新到本地，准备同步到服务器:', id);

    // 尝试同步到服务器
    try {
      // 调用API更新数据
      const response = await put<ApiResponse<IrradianceData>>(`/irradiance/${id}`, updatedData);
      const serverData = response.data.data;

      console.log('服务器返回数据:', serverData);

      // 更新同步状态
      updatedData.syncStatus = 'synced';

      // 更新缓存
      try {
        cacheManager.addItem(id, updatedData);
        console.log('已更新同步状态到缓存');
      } catch (updateError) {
        console.error('更新同步状态到缓存失败:', updateError);
      }

      console.log('数据已成功同步到服务器:', id);
      message.success('数据已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      // 保持local-only状态
      message.error('同步到服务器失败，数据将保存在本地');
    }

    return updatedData;
  } catch (error) {
    console.error('更新光照数据失败:', error);
    throw error;
  }
};

/**
 * 删除光照数据
 * @param id 光照数据ID
 * @returns Promise
 */
export const deleteIrradianceData = async (id: string): Promise<boolean> => {
  try {
    console.log('服务层开始删除数据，ID:', id);

    // 从缓存删除
    console.log('正在从缓存中删除数据');
    cacheManager.removeItem(id);
    console.log('缓存数据删除完成');

    // 从localStorage删除
    const metaKey = `pv_irradiance_${id}`;
    const detailKey = `pv_irradiance_detail_${id}`;
    console.log('正在从localStorage中删除数据，元数据键名:', metaKey);
    console.log('正在从localStorage中删除数据，详细数据键名:', detailKey);

    // 删除元数据和详细数据
    localStorage.removeItem(metaKey);
    localStorage.removeItem(detailKey);

    // 检查是否删除成功
    const checkMetaItem = localStorage.getItem(metaKey);
    const checkDetailItem = localStorage.getItem(detailKey);
    console.log('检查删除后的元数据是否存在:', checkMetaItem ? '仍然存在' : '已删除');
    console.log('检查删除后的详细数据是否存在:', checkDetailItem ? '仍然存在' : '已删除');

    // 确保数据被删除
    if (checkMetaItem || checkDetailItem) {
      console.log('数据仍然存在，再次尝试删除');
      localStorage.removeItem(metaKey);
      localStorage.removeItem(detailKey);

      // 强制清除
      try {
        delete window.localStorage[metaKey];
        delete window.localStorage[detailKey];
      } catch (e) {
        console.error('使用delete操作符删除失败:', e);
      }
    }

    // 尝试从服务器删除数据
    console.log('尝试从服务器删除数据');
    try {
      // 调用API删除数据
      await del<ApiResponse<boolean>>(`/irradiance/${id}`);
      console.log('服务器数据删除成功');
      message.success('数据已成功从服务器删除');
    } catch (serverError) {
      console.error('从服务器删除数据失败:', serverError);

      // 检查错误类型，如果是404（数据不存在），则不显示错误消息
      if (serverError.response && serverError.response.status === 404) {
        console.log('服务器上不存在此数据，可能已被删除');
      } else {
        message.error('从服务器删除数据失败，但已从本地删除');
      }
      // 即使服务器删除失败，我们仍然会从本地删除数据
    }

    // 再次检查本地数据是否已删除
    const finalCheckMeta = localStorage.getItem(metaKey);
    const finalCheckDetail = localStorage.getItem(detailKey);
    if (finalCheckMeta || finalCheckDetail) {
      console.log('数据仍然存在，最后尝试删除');
      localStorage.removeItem(metaKey);
      localStorage.removeItem(detailKey);

      // 如果还是删除不掉，尝试使用其他方法
      try {
        window.localStorage.removeItem(metaKey);
        window.localStorage.removeItem(detailKey);
        delete window.localStorage[metaKey];
        delete window.localStorage[detailKey];

        // 最后的尝试：清除所有以此ID开头的键
        const keysToRemove: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const storageKey = localStorage.key(i);
          if (storageKey && storageKey.includes(id)) {
            keysToRemove.push(storageKey);
          }
        }

        for (const keyToRemove of keysToRemove) {
          localStorage.removeItem(keyToRemove);
          console.log(`删除相关键: ${keyToRemove}`);
        }
      } catch (e) {
        console.error('使用多种方法删除失败:', e);
      }
    }

    return true;
  } catch (error) {
    console.error('删除光照数据失败:', error);
    throw error;
  }
};

/**
 * 上传光照数据CSV文件
 * @param file CSV文件
 * @param onProgress 进度回调
 * @returns Promise
 */
export const uploadIrradianceCSV = async (
  file: File,
  onProgress?: (progressEvent: any) => void
): Promise<string> => {
  try {
    // 调用API上传文件
    const response = await uploadFile<ApiResponse<{ filename: string }>>('/irradiance/upload', file, onProgress);
    console.log('服务器返回数据:', response.data);

    // 返回文件名或ID
    return response.data.data.filename;
  } catch (error) {
    console.error('上传光照数据CSV文件失败:', error);
    message.error('上传文件失败');
    throw error;
  }
};
