/**
 * 项目数据同步服务
 * 用于处理项目数据的本地存储、缓存管理和服务器同步
 */

import { message } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { get, post, put, del } from './api';
import { Project, SyncStatus } from '../types/project';
import { ProjectData } from '../types/projectData';
import { cacheManager } from '../utils/dataSynchronization';
import * as LZString from 'lz-string';
import {
  compressProjectData,
  decompressProjectData,
  compressProjectHourlyDataInBatches,
  decompressProjectHourlyDataFromBatches
} from '../utils/dataCompression';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 项目列表响应类型
interface ProjectListResponse {
  items: Project[];
  total: number;
}

// 本地存储前缀
const PROJECT_STORAGE_PREFIX = 'pv_project_';

/**
 * 获取项目列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns Promise
 */
export const getProjectList = async (
  page: number = 1,
  pageSize: number = 10
): Promise<ProjectListResponse> => {
  try {
    console.log('开始获取项目列表', new Date().toISOString());

    // 尝试从服务器获取数据
    let serverItems: Project[] = [];

    try {
      console.log('尝试从服务器获取项目列表...');
      const response = await get<ApiResponse<ProjectListResponse>>('/projects', {
        page,
        pageSize
      });

      console.log('服务器响应状态:', response.status);
      console.log('服务器响应数据类型:', typeof response.data);

      if (response.data && response.data.success) {
        if (response.data.data && Array.isArray(response.data.data.items)) {
          serverItems = response.data.data.items;
          console.log('从服务器获取到项目数据条数:', serverItems.length);
        } else {
          console.error('服务器返回的数据格式不正确:', response.data);
        }
      } else {
        console.error('服务器响应不成功:', response.data);
      }
    } catch (serverError) {
      console.error('从服务器获取项目列表失败:', serverError);
      console.error('错误详情:', JSON.stringify(serverError, Object.getOwnPropertyNames(serverError)));
    }

    // 处理同步状态
    const processedItems = serverItems.map(item => {
      // 检查本地是否有该数据
      const key = `${PROJECT_STORAGE_PREFIX}${item.id}`;
      const localData = localStorage.getItem(key);

      // 确定同步状态
      let syncStatus: SyncStatus = 'server-only';

      if (localData) {
        syncStatus = 'synced';
        // 合并本地数据
        try {
          // 解析本地数据但不需要使用，因为我们只需要知道它存在
          JSON.parse(localData);
          return {
            ...item,
            syncStatus
          };
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }

      // 如果没有本地数据或解析失败，使用服务器数据
      return {
        ...item,
        syncStatus
      };
    });

    // 获取本地数据
    const localItems: Project[] = [];
    console.log('开始获取本地项目数据...');
    const allKeys = Object.keys(localStorage);
    console.log('localStorage中的键总数:', allKeys.length);

    const projectKeys = allKeys.filter(key => key.startsWith(PROJECT_STORAGE_PREFIX) && !key.includes('hourly_'));
    console.log('项目相关的键数量(排除小时数据):', projectKeys.length);

    projectKeys.forEach(key => {
      console.log(`处理本地存储项: ${key}`);
      try {
        const localDataStr = localStorage.getItem(key);
        if (!localDataStr) {
          console.warn(`本地存储项 ${key} 为空`);
          return; // 跳过这个项目
        }

        console.log(`本地存储项 ${key} 数据长度:`, localDataStr.length);
        console.log(`本地存储项 ${key} 数据前50个字符:`, localDataStr.substring(0, 50));

        // 尝试解析JSON
        try {
          // 检查JSON格式是否有效
          if (!localDataStr.trim().startsWith('{')) {
            console.error(`本地存储项 ${key} 不是有效的JSON对象格式`);
            return; // 跳过这个项目
          }

          const data = JSON.parse(localDataStr);
          console.log(`成功解析本地存储项 ${key}, 数据类型:`, typeof data);

          // 验证数据有效性
          if (!data) {
            console.warn(`本地存储项 ${key} 数据为null或undefined`);
            return; // 跳过这个项目
          }

          if (!data.id) {
            console.warn(`本地存储项 ${key} 数据缺少id字段:`, data);
            return; // 跳过这个项目
          }

          console.log(`本地存储项 ${key} 数据有效, ID: ${data.id}, 名称: ${data.name || '未命名'}`);

          // 检查是否已经包含在服务器数据中
          const isInServer = processedItems.some(item => item.id === data.id);
          console.log(`本地存储项 ${key} 是否已包含在服务器数据中: ${isInServer}`);

          if (!isInServer) {
            // 确保数据有正确的字段
            const completeData = {
              ...data,
              syncStatus: 'local-only',
              // 确保基本字段存在
              name: data.name || '未命名项目',
              location: data.location || '',
              capacity: typeof data.capacity === 'number' ? data.capacity : 0,
              status: data.status || 'draft',
              createdAt: data.createdAt || new Date().toISOString(),
              updatedAt: data.updatedAt || new Date().toISOString()
            };

            // 确保分析结果字段存在
            if (!completeData.analysisResults) {
              completeData.analysisResults = {
                hourlyData: [],
                dailyData: [],
                monthlyData: [],
                yearlyData: {
                  pvGeneration: 0,
                  storageCharge: 0,
                  storageDischarge: 0,
                  electricityConsumption: 0,
                  gridExport: 0,
                  gridImport: 0,
                  gridExportIncome: 0,
                  pvBenefit: 0,
                  storageBenefit: 0,
                  totalBenefit: 0,
                  roi: 0,
                  paybackPeriod: 0
                },
                analysisCompleted: false,
                analysisDate: ''
              };
            } else if (!completeData.analysisResults.yearlyData) {
              completeData.analysisResults.yearlyData = {
                pvGeneration: 0,
                storageCharge: 0,
                storageDischarge: 0,
                electricityConsumption: 0,
                gridExport: 0,
                gridImport: 0,
                gridExportIncome: 0,
                pvBenefit: 0,
                storageBenefit: 0,
                totalBenefit: 0,
                roi: 0,
                paybackPeriod: 0
              };
            }

            localItems.push(completeData);
            console.log(`本地存储项 ${key} 已添加到本地项目列表`);

            // 尝试修复并保存回本地存储
            try {
              localStorage.setItem(key, JSON.stringify(completeData));
              console.log(`本地存储项 ${key} 已修复并保存回本地存储`);
            } catch (saveError) {
              console.error(`保存修复后的数据失败:`, saveError);
            }
          } else {
            console.log(`本地存储项 ${key} 已存在于服务器数据中，跳过`);
          }
        } catch (parseError) {
          console.error(`解析本地存储项 ${key} JSON失败:`, parseError);
          console.error(`本地存储项 ${key} 数据内容:`, localDataStr);

          // 尝试修复损坏的JSON
          try {
            // 如果是常见的JSON错误，尝试修复
            let fixedJson = localDataStr;

            // 移除可能导致解析错误的特殊字符
            fixedJson = fixedJson.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

            // 尝试解析修复后的JSON
            const fixedData = JSON.parse(fixedJson);
            console.log(`成功修复并解析本地存储项 ${key}`);

            // 如果修复成功，保存回本地存储
            if (fixedData && fixedData.id) {
              localStorage.setItem(key, fixedJson);
              console.log(`已保存修复后的数据到本地存储`);
            } else {
              // 如果修复后的数据仍然无效，删除该项
              localStorage.removeItem(key);
              console.log(`修复后的数据仍然无效，已删除本地存储项 ${key}`);
            }
          } catch (fixError) {
            // 如果无法修复，删除损坏的数据
            localStorage.removeItem(key);
            console.log(`无法修复损坏的数据，已删除本地存储项 ${key}`);
          }
        }
      } catch (error) {
        console.error(`处理本地存储项 ${key} 失败:`, error);
        console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
      }
    });

    // 合并服务器和本地数据
    const allItems = [...processedItems, ...localItems];
    console.log('合并后的数据条数:', allItems.length);

    return {
      items: allItems,
      total: allItems.length
    };
  } catch (error) {
    console.error('获取项目列表失败:', error);

    // 如果API调用失败，尝试从本地获取数据
    const localItems: Project[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(PROJECT_STORAGE_PREFIX)) {
        try {
          const localDataStr = localStorage.getItem(key);
          if (!localDataStr) {
            console.warn(`本地存储项 ${key} 为空`);
            return; // 跳过这个项目
          }

          // 尝试解析JSON
          const data = JSON.parse(localDataStr);

          // 验证数据有效性
          if (!data || !data.id) {
            console.warn(`本地存储项 ${key} 数据无效:`, data);
            return; // 跳过这个项目
          }

          // 确保数据有正确的字段
          const completeData = {
            ...data,
            syncStatus: 'local-only'
          };
          localItems.push(completeData);
        } catch (error) {
          console.error(`解析本地数据失败 (${key}):`, error);
          // 可以考虑删除无效的数据
          // localStorage.removeItem(key);
        }
      }
    });

    console.log('从本地获取到项目数据条数:', localItems.length);

    return {
      items: localItems,
      total: localItems.length
    };
  }
};

/**
 * 获取项目详情
 * @param id 项目ID
 * @returns Promise
 */
export const getProject = async (id: string): Promise<ProjectData> => {
  try {
    console.log(`开始获取项目详情: ${id}`, new Date().toISOString());

    // 检查本地存储
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    const localData = localStorage.getItem(key);
    console.log(`本地存储项 ${key} 是否存在:`, !!localData);
    if (localData) {
      console.log(`本地存储项 ${key} 数据长度:`, localData.length);
      console.log(`本地存储项 ${key} 数据前50个字符:`, localData.substring(0, 50));
    }
    let isServerData = false;

    // 如果本地有数据，先使用本地数据
    if (localData) {
      try {
        console.log('尝试解析本地项目数据...');

        // 检查JSON格式是否有效
        if (!localData.trim().startsWith('{')) {
          console.error(`本地存储项 ${key} 不是有效的JSON对象格式`);
          isServerData = true;
          // 不能直接返回，因为这会导致类型错误
          // 继续执行，但会在后面的验证中失败
        }

        // 移除可能导致解析错误的特殊字符
        const cleanedData = localData.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

        const parsedData = JSON.parse(cleanedData);
        console.log('从本地获取到项目数据:', parsedData.name || '未命名');
        console.log('项目状态:', parsedData.status || 'unknown');
        console.log('同步状态:', parsedData.syncStatus || 'unknown');

        // 检查分析结果
        if (parsedData.analysisResults) {
          console.log('项目有分析结果');
          console.log('小时数据是否存在:', !!parsedData.analysisResults.hourlyData);
          if (parsedData.analysisResults.hourlyData) {
            console.log('小时数据点数:', parsedData.analysisResults.hourlyData.length);
          }
        } else {
          console.log('项目没有分析结果');
        }

        // 如果本地数据有效，确保字段完整
        if (parsedData) {
          // 确保数据有正确的字段
          const completeData = {
            ...parsedData,
            syncStatus: parsedData.syncStatus || 'local-only',
            // 确保基本字段存在
            name: parsedData.name || '未命名项目',
            location: parsedData.location || '',
            capacity: typeof parsedData.capacity === 'number' ? parsedData.capacity : 0,
            status: parsedData.status || 'draft',
            createdAt: parsedData.createdAt || new Date().toISOString(),
            updatedAt: parsedData.updatedAt || new Date().toISOString()
          };

          // 确保分析结果字段存在
          if (!completeData.analysisResults) {
            completeData.analysisResults = {
              hourlyData: [],
              dailyData: [],
              monthlyData: [],
              yearlyData: {
                pvGeneration: 0,
                storageCharge: 0,
                storageDischarge: 0,
                electricityConsumption: 0,
                gridExport: 0,
                gridImport: 0,
                gridExportIncome: 0,
                pvBenefit: 0,
                storageBenefit: 0,
                totalBenefit: 0,
                roi: 0,
                paybackPeriod: 0
              },
              analysisCompleted: false,
              analysisDate: ''
            };
          } else if (!completeData.analysisResults.yearlyData) {
            completeData.analysisResults.yearlyData = {
              pvGeneration: 0,
              storageCharge: 0,
              storageDischarge: 0,
              electricityConsumption: 0,
              gridExport: 0,
              gridImport: 0,
              gridExportIncome: 0,
              pvBenefit: 0,
              storageBenefit: 0,
              totalBenefit: 0,
              roi: 0,
              paybackPeriod: 0
            };
          }

          // 检查是否有分离存储的小时数据
          // 首先检查是否有批次信息
          const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
          const batchInfoStr = localStorage.getItem(batchInfoKey);
          console.log(`批次存储的小时数据信息是否存在:`, !!batchInfoStr);

          // 如果没有批次信息，检查旧格式的单文件存储
          const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}`;
          const compressedHourlyData = localStorage.getItem(hourlyDataKey);
          console.log(`旧格式分离存储的小时数据是否存在:`, !!compressedHourlyData);

          // 首先尝试从批次数据中恢复
          if (batchInfoStr && completeData.analysisResults) {
            try {
              console.log('发现批次存储的小时数据，尝试恢复...');
              const batchInfo = JSON.parse(batchInfoStr);
              console.log('批次信息:', batchInfo);

              // 恢复所有批次数据
              const allHourlyData = [];
              let success = true;

              for (let i = 0; i < batchInfo.batchCount; i++) {
                const batchKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
                const compressedBatch = localStorage.getItem(batchKey);

                if (compressedBatch) {
                  try {
                    // 解压批次数据
                    const decompressedBatch = LZString.decompressFromUTF16(compressedBatch);
                    if (decompressedBatch) {
                      const batchData = JSON.parse(decompressedBatch);
                      if (Array.isArray(batchData)) {
                        // 添加到完整数据中
                        allHourlyData.push(...batchData);
                        console.log(`批次${i+1}/${batchInfo.batchCount} 成功恢复 ${batchData.length} 条记录`);
                      } else {
                        console.warn(`批次${i+1}/${batchInfo.batchCount} 数据不是数组`);
                        success = false;
                        break;
                      }
                    } else {
                      console.warn(`批次${i+1}/${batchInfo.batchCount} 解压失败`);
                      success = false;
                      break;
                    }
                  } catch (error) {
                    console.error(`批次${i+1}/${batchInfo.batchCount} 处理失败:`, error);
                    success = false;
                    break;
                  }
                } else {
                  console.warn(`批次${i+1}/${batchInfo.batchCount} 不存在`);
                  success = false;
                  break;
                }
              }

              if (success) {
                console.log(`成功恢复所有小时数据，共 ${allHourlyData.length} 条记录`);
                // 创建新的analysisResults对象，避免修改只读属性
                completeData.analysisResults = {
                  ...completeData.analysisResults,
                  hourlyData: allHourlyData
                };

                // 不需要从服务器获取数据
                isServerData = false;
                return completeData;
              } else {
                console.warn('批次数据恢复失败，将尝试旧格式或从服务器获取');
                isServerData = true;
              }
            } catch (error) {
              console.error('恢复批次数据失败:', error);
              console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
              console.warn('将尝试旧格式或从服务器获取');
              isServerData = true;
            }
          }

          // 如果批次数据恢复失败，尝试旧格式
          else if (compressedHourlyData && completeData.analysisResults) {
            try {
              console.log('发现压缩的小时数据，尝试解压...');
              console.log('压缩数据长度:', compressedHourlyData.length);

              // 解压小时数据
              const decompressedData = LZString.decompressFromUTF16(compressedHourlyData);
              console.log('解压后数据是否存在:', !!decompressedData);

              if (decompressedData) {
                console.log('解压后数据长度:', decompressedData.length);
                console.log('解压后数据前50个字符:', decompressedData.substring(0, 50));

                try {
                  const hourlyData = JSON.parse(decompressedData);
                  console.log('成功解析小时数据，数据类型:', typeof hourlyData);
                  console.log('是否为数组:', Array.isArray(hourlyData));

                  if (Array.isArray(hourlyData)) {
                    console.log('成功解压小时数据，数据点数:', hourlyData.length);

                    // 检查第一个小时数据点
                    if (hourlyData.length > 0) {
                      const firstHourData = hourlyData[0];
                      console.log('第一个小时数据点:', JSON.stringify(firstHourData));

                      // 检查electricityConsumption字段
                      if (firstHourData.electricityConsumption) {
                        console.log('electricityConsumption类型:', typeof firstHourData.electricityConsumption);
                        if (typeof firstHourData.electricityConsumption === 'object') {
                          console.log('electricityConsumption对象:', JSON.stringify(firstHourData.electricityConsumption));
                        }
                      }
                    }

                    // 将小时数据合并回项目数据
                    // 创建新的analysisResults对象，避免修改只读属性
                    completeData.analysisResults = {
                      ...completeData.analysisResults,
                      hourlyData: hourlyData
                    };
                  } else {
                    console.warn('解析后的小时数据不是数组');
                    isServerData = true;
                  }
                } catch (parseError) {
                  console.error('解析小时数据JSON失败:', parseError);
                  console.error('错误详情:', JSON.stringify(parseError, Object.getOwnPropertyNames(parseError)));
                  console.warn('将尝试从服务器获取完整数据');
                  isServerData = true;
                }
              } else {
                console.warn('小时数据解压失败，可能需要从服务器获取完整数据');
                isServerData = true;
              }
            } catch (decompressionError) {
              console.error('解压小时数据失败:', decompressionError);
              console.error('错误详情:', JSON.stringify(decompressionError, Object.getOwnPropertyNames(decompressionError)));
              console.warn('将尝试从服务器获取完整数据');
              isServerData = true;
            }
          } else if (completeData.analysisResults && (!completeData.analysisResults.hourlyData || completeData.analysisResults.hourlyData.length === 0)) {
            console.log('本地没有小时数据，将尝试从服务器获取');
            // 如果没有小时数据，标记为需要从服务器获取
            isServerData = true;
          }

          // 如果不需要从服务器获取，直接返回完整数据
          if (!isServerData) {
            console.log('处理后的本地项目数据:', completeData.name);
            return completeData;
          }
        }
      } catch (error) {
        console.error('解析本地项目数据失败:', error);
        isServerData = true;
      }
    } else {
      // 如果本地没有数据，则认为是服务器数据
      isServerData = true;
      console.log('本地没有项目数据，认为是服务器数据');
    }

    // 如果是服务器数据或本地数据无效，尝试从服务器获取
    if (isServerData) {
      console.log('尝试从服务器获取项目数据, ID:', id);
      try {
        // 从服务器获取数据
        console.log('发送GET请求到服务器...');
        const response = await get<ApiResponse<ProjectData>>(`/projects/${id}`);
        console.log('服务器响应状态:', response.status);
        console.log('服务器响应数据类型:', typeof response.data);

        const serverData = response.data.data;
        console.log('服务器返回的数据是否存在:', !!serverData);

        // 验证服务器数据的有效性
        if (serverData) {
          console.log('从服务器获取到项目数据:', serverData.name);
          console.log('项目状态:', serverData.status);

          // 检查分析结果
          if (serverData.analysisResults) {
            console.log('项目有分析结果');
            console.log('小时数据是否存在:', !!serverData.analysisResults.hourlyData);

            if (serverData.analysisResults.hourlyData) {
              console.log('小时数据点数:', serverData.analysisResults.hourlyData.length);

              // 检查第一个小时数据点
              if (serverData.analysisResults.hourlyData.length > 0) {
                const firstHourData = serverData.analysisResults.hourlyData[0];
                console.log('第一个小时数据点:', JSON.stringify(firstHourData));

                // 检查electricityConsumption字段
                if (firstHourData.electricityConsumption !== undefined) {
                  console.log('electricityConsumption类型:', typeof firstHourData.electricityConsumption);
                  console.log('electricityConsumption值:', firstHourData.electricityConsumption);
                  if (typeof firstHourData.electricityConsumption === 'object') {
                    console.log('electricityConsumption对象:', JSON.stringify(firstHourData.electricityConsumption));
                  }
                }
              }
            }
          } else {
            console.log('项目没有分析结果');
            // 如果服务器数据没有分析结果，尝试从服务器获取分析结果
            try {
              console.log('尝试从服务器获取分析结果...');
              const resultsResponse = await get<ApiResponse<any>>(`/projects/${id}/results`);
              if (resultsResponse.data.success && resultsResponse.data.data) {
                console.log('成功获取分析结果');
                serverData.analysisResults = resultsResponse.data.data;
                console.log('小时数据点数:', serverData.analysisResults.hourlyData?.length || 0);
              }
            } catch (resultsError) {
              console.error('获取分析结果失败:', resultsError);
            }
          }

          // 确保数据有正确的字段
          const completeData = {
            ...serverData,
            syncStatus: 'synced' as SyncStatus
          };

          console.log('处理后的服务器项目数据:', completeData.name);

          // 保存到本地
          try {
            console.log('保存服务器数据到本地存储...');
            localStorage.setItem(key, JSON.stringify(completeData));
            console.log('服务器数据已保存到本地存储');

            // 如果有小时数据，尝试分离存储
            if (completeData.analysisResults && completeData.analysisResults.hourlyData && completeData.analysisResults.hourlyData.length > 0) {
              try {
                console.log('尝试压缩并分离存储小时数据...');
                const hourlyData = completeData.analysisResults.hourlyData;
                const hourlyDataJson = JSON.stringify(hourlyData);
                console.log('小时数据JSON字符串长度:', hourlyDataJson.length);

                // 使用LZ-string压缩数据
                const compressedData = LZString.compressToUTF16(hourlyDataJson);
                console.log('压缩后数据长度:', compressedData.length);

                // 保存压缩数据
                const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}`;
                localStorage.setItem(hourlyDataKey, compressedData);
                console.log('小时数据已压缩并保存到localStorage');

                // 从元数据中移除小时数据
                const metaProject = { ...completeData };
                if (metaProject.analysisResults) {
                  metaProject.analysisResults.hourlyData = [];
                  localStorage.setItem(key, JSON.stringify(metaProject));
                  console.log('项目元数据已更新（移除小时数据）');
                }
              } catch (compressionError) {
                console.error('压缩和保存小时数据失败:', compressionError);
                console.error('错误详情:', JSON.stringify(compressionError, Object.getOwnPropertyNames(compressionError)));
              }
            }
          } catch (saveError) {
            console.error('保存服务器数据到本地存储失败:', saveError);
            console.error('错误详情:', JSON.stringify(saveError, Object.getOwnPropertyNames(saveError)));
          }

          // 添加到缓存
          try {
            console.log('添加项目数据到缓存...');
            // 使用压缩函数处理项目数据
            console.log('使用压缩函数处理项目数据');
            const projectForCache = {
              ...completeData,
              dataType: '项目数据' // 使用统一的类型标记
            };
            cacheManager.addItem(id, projectForCache as any);
            console.log('项目数据已添加到缓存');
          } catch (cacheError) {
            console.error('添加到缓存失败:', cacheError);
            console.error('错误详情:', JSON.stringify(cacheError, Object.getOwnPropertyNames(cacheError)));
          }

          return completeData;
        } else {
          console.error('服务器项目数据无效');
          throw new Error('服务器项目数据无效');
        }
      } catch (serverError) {
        console.error('从服务器获取项目数据失败:', serverError);
        console.error('错误详情:', JSON.stringify(serverError, Object.getOwnPropertyNames(serverError)));
        throw serverError;
      }
    }

    throw new Error('无法获取项目详情');
  } catch (error) {
    console.error('获取项目详情失败:', error);
    throw error;
  }
};

/**
 * 创建项目
 * @param project 项目数据
 * @returns Promise
 */
export const createProject = async (project: Partial<ProjectData>): Promise<ProjectData> => {
  try {
    console.log('开始创建项目...');
    console.log('传入的项目数据:', project);
    console.log('项目ID是否存在:', !!project.id);

    // 生成ID和时间戳
    const id = project.id || uuidv4();
    const now = new Date().toISOString();
    console.log('使用的项目ID:', id);
    console.log('创建时间戳:', now);

    // 创建新的项目
    const newProject: ProjectData = {
      ...project as ProjectData,
      id,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'local-only',
    };

    console.log('创建的新项目对象:', newProject);
    console.log('项目名称:', newProject.name);
    console.log('项目状态:', newProject.status);
    console.log('同步状态:', newProject.syncStatus);

    // 保存到本地，但分离详细数据和元数据
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    console.log('本地存储键名:', key);

    try {
      // 创建元数据对象（不包含详细的小时数据）
      const metaProject = { ...newProject };

      // 提取并保存分析结果的小时数据
      let hourlyData = null;
      if (metaProject.analysisResults && metaProject.analysisResults.hourlyData && metaProject.analysisResults.hourlyData.length > 0) {
        console.log('提取小时数据，数据点数:', metaProject.analysisResults.hourlyData.length);
        hourlyData = [...metaProject.analysisResults.hourlyData]; // 创建副本
        // 从元数据中移除小时数据
        metaProject.analysisResults = {
          ...metaProject.analysisResults,
          hourlyData: []
        };
      }

      // 保存元数据到localStorage
      const metaJson = JSON.stringify(metaProject);
      console.log('元数据JSON字符串长度:', metaJson.length);
      localStorage.setItem(key, metaJson);
      console.log('项目元数据已保存到localStorage');

      // 如果有小时数据，尝试压缩并保存
      if (hourlyData && hourlyData.length > 0) {
        try {
          console.log('尝试压缩并保存小时数据...');

          // 将小时数据分批处理，每批最多1000条记录
          const BATCH_SIZE = 1000;
          const batches = [];

          for (let i = 0; i < hourlyData.length; i += BATCH_SIZE) {
            batches.push(hourlyData.slice(i, i + BATCH_SIZE));
          }

          console.log(`将${hourlyData.length}条记录分成${batches.length}批处理`);

          // 逐批压缩并保存
          for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            const batchJson = JSON.stringify(batch);
            console.log(`批次${i+1}/${batches.length} JSON长度:`, batchJson.length, 'bytes');

            // 使用LZ-string压缩数据
            const compressedData = LZString.compressToUTF16(batchJson);
            console.log(`批次${i+1}/${batches.length} 压缩后长度:`, compressedData.length, 'bytes');

            // 保存压缩数据
            const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
            localStorage.setItem(hourlyDataKey, compressedData);
            console.log(`批次${i+1}/${batches.length} 已保存到localStorage`);
          }

          // 保存批次信息
          const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
          const batchInfo = {
            totalRecords: hourlyData.length,
            batchCount: batches.length,
            batchSize: BATCH_SIZE
          };
          localStorage.setItem(batchInfoKey, JSON.stringify(batchInfo));
          console.log('小时数据批次信息已保存');

        } catch (compressionError) {
          console.error('压缩和保存小时数据失败:', compressionError);
          console.warn('小时数据将只保存在服务器上，本地访问时需要从服务器获取');
        }
      }

      // 验证保存是否成功
      const savedData = localStorage.getItem(key);
      if (savedData) {
        console.log('验证: 项目元数据成功保存到localStorage, 数据长度:', savedData.length);
      } else {
        console.error('验证: 项目元数据未能保存到localStorage');
      }
    } catch (localStorageError) {
      console.error('保存到localStorage失败:', localStorageError);
      console.error('错误详情:', JSON.stringify(localStorageError, Object.getOwnPropertyNames(localStorageError)));
      throw new Error('保存到本地存储失败: ' + (localStorageError as Error).message);
    }

    // 尝试同步到服务器
    try {
      // 检查网络连接
      let isServerAvailable = false;
      try {
        console.log('🔍 检查服务器连接状态...');
        const testResponse = await fetch('http://localhost:3000/api/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // 设置较短的超时时间
          signal: AbortSignal.timeout(3000)
        });
        isServerAvailable = testResponse.ok;
        console.log(`🔍 服务器连接状态: ${isServerAvailable ? '可用' : '不可用'}`);
      } catch (connectionError) {
        console.error('🔍 服务器连接测试失败:', connectionError);
        isServerAvailable = false;
      }

      // 如果服务器不可用，直接返回本地项目
      if (!isServerAvailable) {
        console.log('⚠️ 服务器不可用，项目将保存在本地');
        message.warning('服务器不可用，项目将保存在本地');
        return newProject;
      }

      console.log('尝试同步项目到服务器...');
      // 确保发送到服务器的数据包含完整的小时数据
      const projectToSend = { ...newProject };

      // 如果有分离存储的小时数据，恢复到项目对象中
      if (projectToSend.analysisResults && (!projectToSend.analysisResults.hourlyData || projectToSend.analysisResults.hourlyData.length === 0)) {
        // 检查是否有批次信息
        const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
        const batchInfoStr = localStorage.getItem(batchInfoKey);

        if (batchInfoStr) {
          try {
            console.log('发现批次存储的小时数据，尝试恢复...');
            const batchInfo = JSON.parse(batchInfoStr);
            console.log('批次信息:', batchInfo);

            // 恢复所有批次数据
            const allHourlyData = [];
            let success = true;

            for (let i = 0; i < batchInfo.batchCount; i++) {
              const batchKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
              const compressedBatch = localStorage.getItem(batchKey);

              if (compressedBatch) {
                try {
                  // 解压批次数据
                  const decompressedBatch = LZString.decompressFromUTF16(compressedBatch);
                  if (decompressedBatch) {
                    const batchData = JSON.parse(decompressedBatch);
                    if (Array.isArray(batchData)) {
                      // 添加到完整数据中
                      allHourlyData.push(...batchData);
                      console.log(`批次${i+1}/${batchInfo.batchCount} 成功恢复 ${batchData.length} 条记录`);
                    } else {
                      console.warn(`批次${i+1}/${batchInfo.batchCount} 数据不是数组`);
                      success = false;
                      break;
                    }
                  } else {
                    console.warn(`批次${i+1}/${batchInfo.batchCount} 解压失败`);
                    success = false;
                    break;
                  }
                } catch (error) {
                  console.error(`批次${i+1}/${batchInfo.batchCount} 处理失败:`, error);
                  success = false;
                  break;
                }
              } else {
                console.warn(`批次${i+1}/${batchInfo.batchCount} 不存在`);
                success = false;
                break;
              }
            }

            if (success) {
              console.log(`成功恢复所有小时数据，共 ${allHourlyData.length} 条记录`);
              // 创建新的analysisResults对象，避免修改只读属性
              projectToSend.analysisResults = {
                ...projectToSend.analysisResults,
                hourlyData: allHourlyData
              };
            }
          } catch (error) {
            console.error('恢复批次数据失败:', error);
          }
        } else {
          // 尝试旧的单文件存储方式
          const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}`;
          const compressedHourlyData = localStorage.getItem(hourlyDataKey);

          if (compressedHourlyData) {
            try {
              console.log('发现旧格式压缩的小时数据，尝试解压...');

              // 解压小时数据
              const decompressedData = LZString.decompressFromUTF16(compressedHourlyData);

              if (decompressedData) {
                try {
                  const hourlyData = JSON.parse(decompressedData);

                  if (Array.isArray(hourlyData)) {
                    console.log('成功解压小时数据，数据点数:', hourlyData.length);

                    // 创建新的analysisResults对象，避免修改只读属性
                    projectToSend.analysisResults = {
                      ...projectToSend.analysisResults,
                      hourlyData: hourlyData
                    };
                  } else {
                    console.warn('解析后的小时数据不是数组');
                  }
                } catch (parseError) {
                  console.error('解析小时数据JSON失败:', parseError);
                }
              } else {
                console.warn('小时数据解压失败');
              }
            } catch (error) {
              console.error('解压小时数据失败:', error);
            }
          }
        }
      }

      const response = await post<ApiResponse<ProjectData>>('/projects', projectToSend);
      console.log('服务器响应:', response.data);
      const serverData = response.data.data;
      console.log('服务器返回的数据:', serverData);

      // 更新同步状态
      console.log('更新项目同步状态为synced');
      newProject.syncStatus = 'synced';

      try {
        localStorage.setItem(key, JSON.stringify(newProject));
        console.log('更新后的项目已保存到localStorage');
      } catch (updateError) {
        console.error('更新同步状态到localStorage失败:', updateError);
      }

      // 添加到缓存
      try {
        console.log('尝试将项目添加到缓存...');
        // 使用压缩函数处理项目数据
        console.log('使用压缩函数处理项目数据');
        const projectForCache = {
          ...newProject,
          dataType: '项目数据' // 使用统一的类型标记
        };
        cacheManager.addItem(id, projectForCache as any);
        console.log('项目已添加到缓存');
      } catch (cacheError) {
        console.error('添加到缓存失败:', cacheError);
        console.error('缓存错误详情:', JSON.stringify(cacheError, Object.getOwnPropertyNames(cacheError)));
      }

      message.success('项目已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，项目将保持本地状态:', uploadError);
      console.error('上传错误详情:', JSON.stringify(uploadError, Object.getOwnPropertyNames(uploadError)));

      // 确保在服务器同步失败时也能保存到本地
      try {
        // 再次确认本地存储是否成功
        const savedData = localStorage.getItem(key);
        if (!savedData) {
          console.log('⚠️ 本地存储可能失败，尝试再次保存');
          localStorage.setItem(key, JSON.stringify(newProject));
        }
        console.log('✅ 确认项目已保存到本地存储');
      } catch (localSaveError) {
        console.error('❌ 再次保存到本地存储失败:', localSaveError);
      }

      message.warning('同步到服务器失败，项目将保存在本地');
    }

    console.log('项目创建完成，返回新项目数据');
    return newProject;
  } catch (error) {
    console.error('创建项目失败:', error);
    console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    throw error;
  }
};

/**
 * 更新项目
 * @param id 项目ID
 * @param project 更新的数据
 * @returns Promise
 */
export const updateProject = async (id: string, project: Partial<ProjectData>): Promise<ProjectData> => {
  try {
    console.log('开始更新项目...');
    console.log('项目ID:', id);
    console.log('传入的更新数据:', project);

    // 获取现有数据
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    console.log('本地存储键名:', key);

    const localData = localStorage.getItem(key);
    console.log('本地是否存在该项目数据:', !!localData);

    let existingData: ProjectData;

    if (localData) {
      try {
        console.log('解析本地项目数据...');
        existingData = JSON.parse(localData);
        console.log('本地项目数据解析成功:', existingData.name);
        console.log('本地项目同步状态:', existingData.syncStatus);
      } catch (parseError) {
        console.error('解析本地项目数据失败:', parseError);
        console.error('本地数据内容:', localData);
        throw new Error('解析本地项目数据失败: ' + (parseError as Error).message);
      }
    } else {
      // 如果本地没有数据，尝试从服务器获取
      console.log('本地没有数据，尝试从服务器获取...');
      try {
        const response = await get<ApiResponse<ProjectData>>(`/projects/${id}`);
        console.log('服务器响应:', response.data);
        existingData = response.data.data;
        console.log('从服务器获取到项目数据:', existingData.name);
      } catch (serverError) {
        console.error('从服务器获取项目数据失败:', serverError);
        console.error('错误详情:', JSON.stringify(serverError, Object.getOwnPropertyNames(serverError)));

        // 如果服务器返回404错误，使用传入的项目数据作为基础数据
        if (serverError.response && serverError.response.status === 404) {
          console.log('服务器上不存在该项目，使用传入的项目数据作为基础数据');
          existingData = {
            ...project as ProjectData,
            id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            syncStatus: 'local-only'
          };
        } else {
          throw new Error('无法获取项目数据: ' + (serverError as Error).message);
        }
      }
    }

    // 更新数据
    console.log('合并现有数据和更新数据...');
    const updatedData: ProjectData = {
      ...existingData,
      ...project,
      id, // 确保ID不变
      updatedAt: new Date().toISOString(),
      syncStatus: 'local-only',
    };
    console.log('更新后的项目数据:', updatedData.name);
    console.log('更新后的同步状态:', updatedData.syncStatus);

    // 保存到本地，但分离详细数据和元数据
    console.log('保存更新后的项目数据到localStorage...');
    try {
      // 创建元数据对象（不包含详细的小时数据）
      const metaProject = { ...updatedData };

      // 提取并保存分析结果的小时数据
      let hourlyData = null;
      if (metaProject.analysisResults && metaProject.analysisResults.hourlyData && metaProject.analysisResults.hourlyData.length > 0) {
        console.log('提取小时数据，数据点数:', metaProject.analysisResults.hourlyData.length);
        hourlyData = [...metaProject.analysisResults.hourlyData]; // 创建副本
        // 从元数据中移除小时数据
        metaProject.analysisResults = {
          ...metaProject.analysisResults,
          hourlyData: []
        };
      }

      // 保存元数据到localStorage
      const metaJson = JSON.stringify(metaProject);
      console.log('元数据JSON字符串长度:', metaJson.length);
      localStorage.setItem(key, metaJson);
      console.log('项目元数据已保存到localStorage');

      // 如果有小时数据，尝试压缩并保存
      if (hourlyData && hourlyData.length > 0) {
        try {
          console.log('尝试压缩并保存小时数据...');

          // 将小时数据分批处理，每批最多1000条记录
          const BATCH_SIZE = 1000;
          const batches = [];

          for (let i = 0; i < hourlyData.length; i += BATCH_SIZE) {
            batches.push(hourlyData.slice(i, i + BATCH_SIZE));
          }

          console.log(`将${hourlyData.length}条记录分成${batches.length}批处理`);

          // 逐批压缩并保存
          for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            const batchJson = JSON.stringify(batch);
            console.log(`批次${i+1}/${batches.length} JSON长度:`, batchJson.length, 'bytes');

            // 使用LZ-string压缩数据
            const compressedData = LZString.compressToUTF16(batchJson);
            console.log(`批次${i+1}/${batches.length} 压缩后长度:`, compressedData.length, 'bytes');

            // 保存压缩数据
            const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
            localStorage.setItem(hourlyDataKey, compressedData);
            console.log(`批次${i+1}/${batches.length} 已保存到localStorage`);
          }

          // 保存批次信息
          const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
          const batchInfo = {
            totalRecords: hourlyData.length,
            batchCount: batches.length,
            batchSize: BATCH_SIZE
          };
          localStorage.setItem(batchInfoKey, JSON.stringify(batchInfo));
          console.log('小时数据批次信息已保存');

        } catch (compressionError) {
          console.error('压缩和保存小时数据失败:', compressionError);
          console.warn('小时数据将只保存在服务器上，本地访问时需要从服务器获取');
        }
      }

      // 验证保存是否成功
      const savedData = localStorage.getItem(key);
      if (savedData) {
        console.log('验证: 项目元数据成功保存到localStorage, 数据长度:', savedData.length);
      } else {
        console.error('验证: 项目元数据未能保存到localStorage');
      }
    } catch (localStorageError) {
      console.error('保存到localStorage失败:', localStorageError);
      console.error('错误详情:', JSON.stringify(localStorageError, Object.getOwnPropertyNames(localStorageError)));
      throw new Error('保存到本地存储失败: ' + (localStorageError as Error).message);
    }

    // 尝试同步到服务器
    try {
      console.log('尝试将项目更新同步到服务器...');
      console.log('更新数据:', updatedData);

      // 检查网络连接
      let isServerAvailable = false;
      try {
        console.log('🔍 检查服务器连接状态...');
        const testResponse = await fetch('http://localhost:3000/api/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // 设置较短的超时时间
          signal: AbortSignal.timeout(3000)
        });
        isServerAvailable = testResponse.ok;
        console.log(`🔍 服务器连接状态: ${isServerAvailable ? '可用' : '不可用'}`);
      } catch (connectionError) {
        console.error('🔍 服务器连接测试失败:', connectionError);
        isServerAvailable = false;
      }

      // 如果服务器不可用，直接保存到本地
      if (!isServerAvailable) {
        console.log('⚠️ 服务器不可用，仅保存到本地');
        updatedData.syncStatus = 'local-only';

        // 保存到本地存储，但分离详细数据和元数据
        try {
          // 创建元数据对象（不包含详细的小时数据）
          const metaProject = { ...updatedData };

          // 如果有小时数据，从元数据中移除
          if (metaProject.analysisResults && metaProject.analysisResults.hourlyData && metaProject.analysisResults.hourlyData.length > 0) {
            const hourlyData = [...metaProject.analysisResults.hourlyData]; // 创建副本
            // 从元数据中移除小时数据
            metaProject.analysisResults = {
              ...metaProject.analysisResults,
              hourlyData: []
            };

            // 尝试压缩并保存小时数据
            try {
              console.log('尝试压缩并保存小时数据...');

              // 将小时数据分批处理，每批最多1000条记录
              const BATCH_SIZE = 1000;
              const batches = [];

              for (let i = 0; i < hourlyData.length; i += BATCH_SIZE) {
                batches.push(hourlyData.slice(i, i + BATCH_SIZE));
              }

              console.log(`将${hourlyData.length}条记录分成${batches.length}批处理`);

              // 逐批压缩并保存
              for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                const batchJson = JSON.stringify(batch);
                console.log(`批次${i+1}/${batches.length} JSON长度:`, batchJson.length, 'bytes');

                // 使用LZ-string压缩数据
                const compressedData = LZString.compressToUTF16(batchJson);
                console.log(`批次${i+1}/${batches.length} 压缩后长度:`, compressedData.length, 'bytes');

                // 保存压缩数据
                const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
                localStorage.setItem(hourlyDataKey, compressedData);
                console.log(`批次${i+1}/${batches.length} 已保存到localStorage`);
              }

              // 保存批次信息
              const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
              const batchInfo = {
                totalRecords: hourlyData.length,
                batchCount: batches.length,
                batchSize: BATCH_SIZE
              };
              localStorage.setItem(batchInfoKey, JSON.stringify(batchInfo));
              console.log('小时数据批次信息已保存');
            } catch (compressionError) {
              console.error('压缩和保存小时数据失败:', compressionError);
              console.warn('小时数据将只保存在服务器上，本地访问时需要从服务器获取');
            }
          }

          // 保存元数据到localStorage
          localStorage.setItem(key, JSON.stringify(metaProject));
          console.log('✅ 项目元数据已保存到本地存储');

          // 更新缓存
          try {
            // 使用压缩函数处理项目数据
            console.log('使用压缩函数处理项目数据');
            const projectForCache = {
              ...metaProject,
              dataType: '项目数据' // 使用统一的类型标记
            };
            cacheManager.addItem(id, projectForCache as any);
            console.log('✅ 项目已添加到缓存');
          } catch (cacheError) {
            console.error('❌ 添加到缓存失败:', cacheError);
          }

          message.warning('服务器不可用，项目已保存到本地');
          return updatedData;
        } catch (localSaveError) {
          console.error('❌ 保存到本地存储失败:', localSaveError);
          throw new Error('保存到本地存储失败: ' + (localSaveError as Error).message);
        }
      }

      try {
        // 先尝试PUT更新
        console.log('尝试PUT更新项目:', id);

        // 确保发送到服务器的数据包含完整的小时数据
        const projectToSend = { ...updatedData };

        // 如果有分离存储的小时数据，恢复到项目对象中
        if (projectToSend.analysisResults && (!projectToSend.analysisResults.hourlyData || projectToSend.analysisResults.hourlyData.length === 0)) {
          // 检查是否有批次信息
          const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
          const batchInfoStr = localStorage.getItem(batchInfoKey);

          if (batchInfoStr) {
            try {
              console.log('发现批次存储的小时数据，尝试恢复...');
              const batchInfo = JSON.parse(batchInfoStr);
              console.log('批次信息:', batchInfo);

              // 恢复所有批次数据
              const allHourlyData = [];
              let success = true;

              for (let i = 0; i < batchInfo.batchCount; i++) {
                const batchKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
                const compressedBatch = localStorage.getItem(batchKey);

                if (compressedBatch) {
                  try {
                    // 解压批次数据
                    const decompressedBatch = LZString.decompressFromUTF16(compressedBatch);
                    if (decompressedBatch) {
                      const batchData = JSON.parse(decompressedBatch);
                      if (Array.isArray(batchData)) {
                        // 添加到完整数据中
                        allHourlyData.push(...batchData);
                        console.log(`批次${i+1}/${batchInfo.batchCount} 成功恢复 ${batchData.length} 条记录`);
                      } else {
                        console.warn(`批次${i+1}/${batchInfo.batchCount} 数据不是数组`);
                        success = false;
                        break;
                      }
                    } else {
                      console.warn(`批次${i+1}/${batchInfo.batchCount} 解压失败`);
                      success = false;
                      break;
                    }
                  } catch (error) {
                    console.error(`批次${i+1}/${batchInfo.batchCount} 处理失败:`, error);
                    success = false;
                    break;
                  }
                } else {
                  console.warn(`批次${i+1}/${batchInfo.batchCount} 不存在`);
                  success = false;
                  break;
                }
              }

              if (success) {
                console.log(`成功恢复所有小时数据，共 ${allHourlyData.length} 条记录`);
                // 创建新的analysisResults对象，避免修改只读属性
                projectToSend.analysisResults = {
                  ...projectToSend.analysisResults,
                  hourlyData: allHourlyData
                };
              }
            } catch (error) {
              console.error('恢复批次数据失败:', error);
            }
          } else {
            // 尝试旧的单文件存储方式
            const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}`;
            const compressedHourlyData = localStorage.getItem(hourlyDataKey);

            if (compressedHourlyData) {
              try {
                console.log('发现旧格式压缩的小时数据，尝试解压...');

                // 解压小时数据
                const decompressedData = LZString.decompressFromUTF16(compressedHourlyData);

                if (decompressedData) {
                  try {
                    const hourlyData = JSON.parse(decompressedData);

                    if (Array.isArray(hourlyData)) {
                      console.log('成功解压小时数据，数据点数:', hourlyData.length);

                      // 创建新的analysisResults对象，避免修改只读属性
                      projectToSend.analysisResults = {
                        ...projectToSend.analysisResults,
                        hourlyData: hourlyData
                      };
                    } else {
                      console.warn('解析后的小时数据不是数组');
                    }
                  } catch (parseError) {
                    console.error('解析小时数据JSON失败:', parseError);
                  }
                } else {
                  console.warn('小时数据解压失败');
                }
              } catch (error) {
                console.error('解压小时数据失败:', error);
              }
            }
          }
        }

        const response = await put<ApiResponse<ProjectData>>(`/projects/${id}`, projectToSend);
        console.log('服务器响应状态:', response.status);
        console.log('服务器响应数据:', response.data);

        if (response.data.success) {
          console.log('服务器返回的数据:', response.data.data);

          // 更新同步状态
          console.log('更新项目同步状态为synced');
          updatedData.syncStatus = 'synced';

          try {
            localStorage.setItem(key, JSON.stringify(updatedData));
            console.log('更新后的项目已保存到localStorage');
          } catch (updateError) {
            console.error('更新同步状态到localStorage失败:', updateError);
          }

          // 更新缓存
          try {
            console.log('尝试更新项目缓存...');
            // 使用压缩函数处理项目数据
            console.log('使用压缩函数处理项目数据');
            const projectForCache = {
              ...updatedData,
              dataType: '项目数据' // 使用统一的类型标记
            };
            cacheManager.addItem(id, projectForCache as any);
            console.log('项目缓存已更新');
          } catch (cacheError) {
            console.error('更新缓存失败:', cacheError);
            console.error('缓存错误详情:', JSON.stringify(cacheError, Object.getOwnPropertyNames(cacheError)));
          }

          message.success('项目已成功同步到服务器');
        } else {
          console.error('服务器响应成功但数据标记为失败:', response.data.message);
          throw new Error(response.data.message || '同步到服务器失败');
        }
      } catch (putError: any) {
        // 如果PUT返回404，说明服务器上不存在该项目，尝试POST创建
        console.error('PUT更新失败:', putError);
        console.error('PUT错误详情:', JSON.stringify(putError, Object.getOwnPropertyNames(putError)));

        if (putError.response && putError.response.status === 404) {
          console.log('服务器上不存在该项目，尝试POST创建:', id);
          try {
            // 确保发送到服务器的数据包含完整的小时数据
            const projectToSend = { ...updatedData };

            // 如果有分离存储的小时数据，恢复到项目对象中
            if (projectToSend.analysisResults && (!projectToSend.analysisResults.hourlyData || projectToSend.analysisResults.hourlyData.length === 0)) {
              // 检查是否有批次信息
              const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;
              const batchInfoStr = localStorage.getItem(batchInfoKey);

              if (batchInfoStr) {
                try {
                  console.log('发现批次存储的小时数据，尝试恢复...');
                  const batchInfo = JSON.parse(batchInfoStr);
                  console.log('批次信息:', batchInfo);

                  // 恢复所有批次数据
                  const allHourlyData = [];
                  let success = true;

                  for (let i = 0; i < batchInfo.batchCount; i++) {
                    const batchKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
                    const compressedBatch = localStorage.getItem(batchKey);

                    if (compressedBatch) {
                      try {
                        // 解压批次数据
                        const decompressedBatch = LZString.decompressFromUTF16(compressedBatch);
                        if (decompressedBatch) {
                          const batchData = JSON.parse(decompressedBatch);
                          if (Array.isArray(batchData)) {
                            // 添加到完整数据中
                            allHourlyData.push(...batchData);
                            console.log(`批次${i+1}/${batchInfo.batchCount} 成功恢复 ${batchData.length} 条记录`);
                          } else {
                            console.warn(`批次${i+1}/${batchInfo.batchCount} 数据不是数组`);
                            success = false;
                            break;
                          }
                        } else {
                          console.warn(`批次${i+1}/${batchInfo.batchCount} 解压失败`);
                          success = false;
                          break;
                        }
                      } catch (error) {
                        console.error(`批次${i+1}/${batchInfo.batchCount} 处理失败:`, error);
                        success = false;
                        break;
                      }
                    } else {
                      console.warn(`批次${i+1}/${batchInfo.batchCount} 不存在`);
                      success = false;
                      break;
                    }
                  }

                  if (success) {
                    console.log(`成功恢复所有小时数据，共 ${allHourlyData.length} 条记录`);
                    // 创建新的analysisResults对象，避免修改只读属性
                    projectToSend.analysisResults = {
                      ...projectToSend.analysisResults,
                      hourlyData: allHourlyData
                    };
                  }
                } catch (error) {
                  console.error('恢复批次数据失败:', error);
                }
              } else {
                // 尝试旧的单文件存储方式
                const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}`;
                const compressedHourlyData = localStorage.getItem(hourlyDataKey);

                if (compressedHourlyData) {
                  try {
                    console.log('发现旧格式压缩的小时数据，尝试解压...');

                    // 解压小时数据
                    const decompressedData = LZString.decompressFromUTF16(compressedHourlyData);

                    if (decompressedData) {
                      try {
                        const hourlyData = JSON.parse(decompressedData);

                        if (Array.isArray(hourlyData)) {
                          console.log('成功解压小时数据，数据点数:', hourlyData.length);

                          // 创建新的analysisResults对象，避免修改只读属性
                          projectToSend.analysisResults = {
                            ...projectToSend.analysisResults,
                            hourlyData: hourlyData
                          };
                        } else {
                          console.warn('解析后的小时数据不是数组');
                        }
                      } catch (parseError) {
                        console.error('解析小时数据JSON失败:', parseError);
                      }
                    } else {
                      console.warn('小时数据解压失败');
                    }
                  } catch (error) {
                    console.error('解压小时数据失败:', error);
                  }
                }
              }
            }

            const createResponse = await post<ApiResponse<ProjectData>>('/projects', projectToSend);
            console.log('创建项目响应:', createResponse.data);

            if (createResponse.data.success) {
              console.log('服务器返回的数据:', createResponse.data.data);

              // 更新同步状态
              console.log('更新项目同步状态为synced');
              updatedData.syncStatus = 'synced';

              try {
                localStorage.setItem(key, JSON.stringify(updatedData));
                console.log('更新后的项目已保存到localStorage');
              } catch (updateError) {
                console.error('更新同步状态到localStorage失败:', updateError);
              }

              // 更新缓存
              try {
                console.log('尝试更新项目缓存...');
                cacheManager.addItem(id, updatedData as any);
                console.log('项目缓存已更新');
              } catch (cacheError) {
                console.error('更新缓存失败:', cacheError);
                console.error('缓存错误详情:', JSON.stringify(cacheError, Object.getOwnPropertyNames(cacheError)));
              }

              message.success('项目已成功同步到服务器');
            } else {
              console.error('创建项目响应成功但数据标记为失败:', createResponse.data.message);
              throw new Error(createResponse.data.message || '创建项目失败');
            }
          } catch (postError) {
            console.error('POST创建项目失败:', postError);
            console.error('POST错误详情:', JSON.stringify(postError, Object.getOwnPropertyNames(postError)));

            // 如果服务器请求失败，确保数据保存到本地
            console.log('⚠️ 服务器请求失败，保存到本地');
            updatedData.syncStatus = 'local-only';

            try {
              // 创建元数据对象（不包含详细的小时数据）
              const metaProject = { ...updatedData };

              // 如果有小时数据，从元数据中移除
              if (metaProject.analysisResults && metaProject.analysisResults.hourlyData && metaProject.analysisResults.hourlyData.length > 0) {
                // 从元数据中移除小时数据
                metaProject.analysisResults = {
                  ...metaProject.analysisResults,
                  hourlyData: []
                };
              }

              // 保存元数据到localStorage
              localStorage.setItem(key, JSON.stringify(metaProject));
              console.log('✅ 项目元数据已保存到本地存储');
              message.warning('服务器同步失败，项目已保存到本地');
            } catch (localSaveError) {
              console.error('❌ 保存到本地存储失败:', localSaveError);
              throw new Error('保存到本地存储失败: ' + (localSaveError as Error).message);
            }
          }
        } else {
          // 其他错误，保存到本地
          console.error('非404错误，保存到本地:', putError);
          updatedData.syncStatus = 'local-only';

          try {
            // 创建元数据对象（不包含详细的小时数据）
            const metaProject = { ...updatedData };

            // 如果有小时数据，从元数据中移除
            if (metaProject.analysisResults && metaProject.analysisResults.hourlyData && metaProject.analysisResults.hourlyData.length > 0) {
              // 从元数据中移除小时数据
              metaProject.analysisResults = {
                ...metaProject.analysisResults,
                hourlyData: []
              };
            }

            // 保存元数据到localStorage
            localStorage.setItem(key, JSON.stringify(metaProject));
            console.log('✅ 项目元数据已保存到本地存储');
            message.warning('服务器同步失败，项目已保存到本地');
          } catch (localSaveError) {
            console.error('❌ 保存到本地存储失败:', localSaveError);
            throw new Error('保存到本地存储失败: ' + (localSaveError as Error).message);
          }
        }
      }
    } catch (uploadError) {
      console.error('同步到服务器失败，项目将保持本地状态:', uploadError);
      console.error('上传错误详情:', JSON.stringify(uploadError, Object.getOwnPropertyNames(uploadError)));

      // 确保在服务器同步失败时也能保存到本地
      try {
        updatedData.syncStatus = 'local-only';

        // 创建元数据对象（不包含详细的小时数据）
        const metaProject = { ...updatedData };

        // 如果有小时数据，从元数据中移除
        if (metaProject.analysisResults && metaProject.analysisResults.hourlyData && metaProject.analysisResults.hourlyData.length > 0) {
          // 从元数据中移除小时数据
          metaProject.analysisResults = {
            ...metaProject.analysisResults,
            hourlyData: []
          };
        }

        // 保存元数据到localStorage
        localStorage.setItem(key, JSON.stringify(metaProject));
        console.log('✅ 项目元数据已保存到本地存储');
        message.warning('同步到服务器失败，项目已保存到本地');
      } catch (localSaveError) {
        console.error('❌ 保存到本地存储失败:', localSaveError);
        throw new Error('保存到本地存储失败: ' + (localSaveError as Error).message);
      }
    }

    console.log('项目更新完成，返回更新后的项目数据');
    return updatedData;
  } catch (error) {
    console.error('更新项目失败:', error);
    console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
    throw error;
  }
};

/**
 * 删除项目
 * @param id 项目ID
 * @returns Promise
 */
export const deleteProject = async (id: string): Promise<boolean> => {
  try {
    console.log(`开始删除项目: ${id}`);

    // 删除本地数据
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    const hourlyDataKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}`;
    const batchInfoKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_info`;

    // 删除元数据和小时数据
    localStorage.removeItem(key);
    localStorage.removeItem(hourlyDataKey);
    localStorage.removeItem(batchInfoKey);

    // 检查是否有批次数据
    try {
      const batchInfoStr = localStorage.getItem(batchInfoKey);
      if (batchInfoStr) {
        const batchInfo = JSON.parse(batchInfoStr);
        // 删除所有批次数据
        for (let i = 0; i < batchInfo.batchCount; i++) {
          const batchKey = `${PROJECT_STORAGE_PREFIX}hourly_${id}_batch_${i}`;
          localStorage.removeItem(batchKey);
        }
      }
    } catch (error) {
      console.error('删除批次数据失败:', error);
    }

    console.log('已删除本地项目数据和小时数据');

    // 从缓存中删除
    try {
      cacheManager.removeItem(id);
    } catch (cacheError) {
      console.error('从缓存中删除项目失败:', cacheError);
    }

    // 尝试从服务器删除
    try {
      const response = await del<ApiResponse<boolean>>(`/projects/${id}`);
      if (response.data.success) {
        message.success('项目已成功从服务器删除');
      } else {
        console.error('从服务器删除项目失败:', response.data.message);
        message.warning('从服务器删除项目失败，但已从本地删除');
      }
    } catch (serverError) {
      console.error('从服务器删除项目失败:', serverError);
      message.warning('从服务器删除项目失败，但已从本地删除');
    }

    return true;
  } catch (error) {
    console.error('删除项目失败:', error);
    throw error;
  }
};

/**
 * 同步项目到服务器
 * @param id 项目ID
 * @returns Promise
 */
export const syncProjectToServer = async (id: string): Promise<ProjectData> => {
  try {
    console.log(`开始同步项目到服务器: ${id}`);

    // 获取本地数据
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    const localData = localStorage.getItem(key);

    if (!localData) {
      throw new Error('本地没有项目数据，无法同步');
    }

    let projectData: ProjectData;
    try {
      projectData = JSON.parse(localData);
      console.log('成功解析本地项目数据:', projectData.name);
    } catch (parseError) {
      console.error('解析本地项目数据失败:', parseError);
      throw new Error('解析本地项目数据失败，无法同步');
    }

    // 检查同步状态
    if (projectData.syncStatus === 'synced') {
      console.log('项目已经同步，无需再次同步');
      return projectData;
    }

    // 检查服务器连接
    let isServerAvailable = false;
    try {
      console.log('🔍 检查服务器连接状态...');
      const testResponse = await fetch('http://localhost:3000/api/health', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        // 设置较短的超时时间
        signal: AbortSignal.timeout(3000)
      });
      isServerAvailable = testResponse.ok;
      console.log(`🔍 服务器连接状态: ${isServerAvailable ? '可用' : '不可用'}`);
    } catch (connectionError) {
      console.error('🔍 服务器连接测试失败:', connectionError);
      isServerAvailable = false;
    }

    // 如果服务器不可用，直接返回本地项目
    if (!isServerAvailable) {
      console.log('⚠️ 服务器不可用，项目将保持本地状态');
      message.warning('服务器不可用，无法同步项目');
      return projectData;
    }

    // 尝试同步到服务器
    try {
      console.log('尝试将项目同步到服务器...');

      // 直接使用POST创建项目，如果服务器上已存在会返回错误
      try {
        console.log('尝试POST创建项目:', id);
        const createResponse = await post<ApiResponse<ProjectData>>('/projects', projectData);
        console.log('POST创建项目响应:', createResponse.data);

        if (createResponse.data.success) {
          // 服务器响应成功，更新同步状态
          projectData.syncStatus = 'synced';
          localStorage.setItem(key, JSON.stringify(projectData));

          // 更新缓存
          try {
            cacheManager.addItem(id, projectData as any);
          } catch (cacheError) {
            console.error('更新缓存失败:', cacheError);
          }

          message.success('项目已成功同步到服务器');
          return projectData;
        }
      } catch (postError: any) {
        // 如果POST返回409 Conflict，说明服务器上已存在该项目，尝试PUT更新
        if (postError.response && postError.response.status === 409) {
          console.log('服务器上已存在该项目，尝试PUT更新:', id);
          try {
            const updateResponse = await put<ApiResponse<ProjectData>>(`/projects/${id}`, projectData);
            console.log('PUT更新项目响应:', updateResponse.data);

            if (updateResponse.data.success) {
              // 服务器响应成功，更新同步状态
              projectData.syncStatus = 'synced';
              localStorage.setItem(key, JSON.stringify(projectData));

              // 更新缓存
              try {
                cacheManager.addItem(id, projectData as any);
              } catch (cacheError) {
                console.error('更新缓存失败:', cacheError);
              }

              message.success('项目已成功同步到服务器');
              return projectData;
            }
          } catch (putError) {
            console.error('PUT更新项目失败:', putError);
            message.error('同步到服务器失败');
            return projectData;
          }
        } else {
          console.error('POST创建项目失败:', postError);
          message.error('同步到服务器失败');
          return projectData;
        }
      }

      return projectData;
    } catch (uploadError) {
      console.error('同步到服务器失败:', uploadError);
      message.error('同步到服务器失败');
      return projectData;
    }
  } catch (error) {
    console.error('同步项目失败:', error);
    message.error('同步项目失败');
    throw error;
  }
};

/**
 * 开始项目分析
 * @param id 项目ID
 * @returns Promise
 */
export const startProjectAnalysis = async (id: string): Promise<boolean> => {
  try {
    console.log(`开始项目分析: ${id}`);

    // 获取本地数据
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    const localData = localStorage.getItem(key);

    if (!localData) {
      throw new Error('本地没有项目数据，无法开始分析');
    }

    const projectData: ProjectData = JSON.parse(localData);

    // 更新项目状态
    projectData.status = 'analyzing';
    projectData.updatedAt = new Date().toISOString();
    localStorage.setItem(key, JSON.stringify(projectData));

    // 尝试在服务器上开始分析
    try {
      const response = await post<ApiResponse<boolean>>(`/projects/${id}/analyze`);

      if (response.data.success) {
        message.success('项目分析已开始');
        return true;
      } else {
        throw new Error(response.data.message || '开始项目分析失败');
      }
    } catch (serverError) {
      console.error('在服务器上开始项目分析失败:', serverError);
      message.warning('在服务器上开始项目分析失败，将在本地进行分析');

      // 这里可以添加本地分析逻辑，或者提示用户服务器分析失败

      return false;
    }
  } catch (error) {
    console.error('开始项目分析失败:', error);
    message.error('开始项目分析失败');
    return false;
  }
};

/**
 * 获取项目分析结果
 * @param id 项目ID
 * @returns Promise
 */
export const getProjectAnalysisResults = async (id: string): Promise<any> => {
  try {
    console.log(`获取项目分析结果: ${id}`);

    // 获取本地数据
    const key = `${PROJECT_STORAGE_PREFIX}${id}`;
    const localData = localStorage.getItem(key);

    if (localData) {
      const projectData: ProjectData = JSON.parse(localData);

      // 如果本地有分析结果，直接返回
      if (projectData.analysisResults && projectData.analysisResults.analysisCompleted) {
        console.log('从本地获取到项目分析结果');
        return projectData.analysisResults;
      }
    }

    // 尝试从服务器获取分析结果
    try {
      const response = await get<ApiResponse<any>>(`/projects/${id}/results`);

      if (response.data.success) {
        const analysisResults = response.data.data;

        // 如果有本地数据，更新本地数据
        if (localData) {
          const projectData: ProjectData = JSON.parse(localData);
          projectData.analysisResults = analysisResults;
          projectData.status = 'completed';
          projectData.updatedAt = new Date().toISOString();
          localStorage.setItem(key, JSON.stringify(projectData));
        }

        return analysisResults;
      } else {
        throw new Error(response.data.message || '获取项目分析结果失败');
      }
    } catch (serverError) {
      console.error('从服务器获取项目分析结果失败:', serverError);

      // 如果服务器获取失败，但本地有数据，返回本地数据
      if (localData) {
        const projectData: ProjectData = JSON.parse(localData);
        if (projectData.analysisResults) {
          message.warning('从服务器获取分析结果失败，使用本地数据');
          return projectData.analysisResults;
        }
      }

      throw serverError;
    }
  } catch (error) {
    console.error('获取项目分析结果失败:', error);
    throw error;
  }
};
