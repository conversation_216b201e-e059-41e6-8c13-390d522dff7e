/**
 * 项目数据服务
 * 用于处理项目数据的计算和分析
 */

import { v4 as uuidv4 } from 'uuid';
import { Project, ElectricityUsage } from '../types/project';
import { ProjectData, ProjectHourlyData, createEmptyHourlyDataArray } from '../types/projectData';
import { applyElectricityUsageToHourlyData } from '../utils/electricityUsageCalculator';

/**
 * 初始化项目数据
 * @param project 项目基本信息
 * @returns 初始化的项目数据
 */
export const initializeProjectData = (project: Project): ProjectData => {
  // 创建项目数据对象
  const projectData: ProjectData = {
    // 基本信息
    id: project.id || uuidv4(),
    name: project.name || '',
    location: project.location || '',
    capacity: project.capacity || 0,
    status: project.status || 'draft',
    createdAt: project.createdAt || new Date().toISOString(),
    updatedAt: project.updatedAt || new Date().toISOString(),

    // 详细信息
    basicInfo: project.basicInfo || {
      name: '',
      location: '',
      capacity: 0,
    },

    // 输入数据
    irradianceDataId: project.irradianceDataId || '',
    electricityPriceId: project.electricityPriceId || '',
    electricityUsage: project.electricityUsage || { type: 'sameEveryday', data: [] },
    pvModules: project.pvModules || [],
    energyStorage: project.energyStorage || [],
    inverters: project.inverters || [],
    otherInvestments: project.otherInvestments || [],

    // 分析结果
    analysisResults: project.analysisResults ? project.analysisResults : {
      hourlyData: createEmptyHourlyDataArray(),
      dailyData: [],
      monthlyData: [],
      yearlyData: {
        pvGeneration: 0,
        storageCharge: 0,
        storageDischarge: 0,
        electricityConsumption: 0,
        gridExport: 0,
        gridImport: 0,
        gridExportIncome: 0,
        pvBenefit: 0,
        storageBenefit: 0,
        totalBenefit: 0,
        roi: 0,
        paybackPeriod: 0
      },
      analysisCompleted: false,
      analysisDate: ''
    }
  };

  // 确保项目有完整的8760条小时数据
  if (!projectData.analysisResults || !projectData.analysisResults.hourlyData || projectData.analysisResults.hourlyData.length !== 8760) {
    console.log('创建完整的8760条小时数据');
    if (!projectData.analysisResults) {
      projectData.analysisResults = {
        hourlyData: createEmptyHourlyDataArray(),
        dailyData: [],
        monthlyData: [],
        yearlyData: {
          pvGeneration: 0,
          storageCharge: 0,
          storageDischarge: 0,
          electricityConsumption: 0,
          gridExport: 0,
          gridImport: 0,
          gridExportIncome: 0,
          pvBenefit: 0,
          storageBenefit: 0,
          totalBenefit: 0,
          roi: 0,
          paybackPeriod: 0
        },
        analysisCompleted: false,
        analysisDate: ''
      };
    } else {
      // 防止直接修改只读对象
      try {
        // 检查hourlyData是否为undefined
        if (!projectData.analysisResults.hourlyData) {
          console.log('hourlyData为undefined，创建新的数组');
          projectData.analysisResults.hourlyData = createEmptyHourlyDataArray();
        } else {
          console.log('hourlyData长度不为8760，创建新的数组');
          // 创建一个新的分析结果对象，避免直接修改只读对象
          projectData.analysisResults = {
            ...projectData.analysisResults,
            hourlyData: createEmptyHourlyDataArray()
          };
        }
      } catch (error) {
        console.error('设置hourlyData时出错:', error);
        // 如果出错，创建一个全新的分析结果对象
        projectData.analysisResults = {
          hourlyData: createEmptyHourlyDataArray(),
          dailyData: projectData.analysisResults.dailyData || [],
          monthlyData: projectData.analysisResults.monthlyData || [],
          yearlyData: projectData.analysisResults.yearlyData || {
            pvGeneration: 0,
            storageCharge: 0,
            storageDischarge: 0,
            electricityConsumption: 0,
            gridExport: 0,
            gridImport: 0,
            gridExportIncome: 0,
            pvBenefit: 0,
            storageBenefit: 0,
            totalBenefit: 0,
            roi: 0,
            paybackPeriod: 0
          },
          analysisCompleted: projectData.analysisResults.analysisCompleted || false,
          analysisDate: projectData.analysisResults.analysisDate || ''
        };
      }
    }
  }

  // 如果项目有电力使用数据，计算8760个小时的用电量
  if (project.electricityUsage && project.electricityUsage.data && project.electricityUsage.data.length > 0) {
    console.log('项目有电力使用数据，计算8760个小时的用电量');
    updateElectricityUsage(projectData, project.electricityUsage);
  } else {
    console.log('项目没有电力使用数据，使用全0数组');
    // 即使没有电力使用数据，也确保hourlyData中的electricityConsumption字段为0
    if (projectData.analysisResults && projectData.analysisResults.hourlyData) {
      try {
        // 使用try-catch块来捕获可能的只读对象错误
        const newHourlyData = projectData.analysisResults.hourlyData.map(hourData => ({
          ...hourData,
          electricityConsumption: 0
        }));
        projectData.analysisResults.hourlyData = newHourlyData;
      } catch (error) {
        console.error('设置电力使用数据为0时出错:', error);
        // 如果出错，创建一个新的分析结果对象
        projectData.analysisResults = {
          ...projectData.analysisResults,
          hourlyData: createEmptyHourlyDataArray()
        };
      }
    }
  }

  return projectData;
};

/**
 * 更新项目的电力使用数据
 * @param projectData 项目数据
 * @param electricityUsage 电力使用数据
 * @returns 更新后的项目数据
 */
export const updateElectricityUsage = (
  projectData: ProjectData,
  electricityUsage: ElectricityUsage
): ProjectData => {
  console.log('更新项目电力使用数据:', electricityUsage.type);

  // 更新项目的电力使用数据
  projectData.electricityUsage = electricityUsage;

  // 确保分析结果存在
  if (!projectData.analysisResults) {
    console.log('创建新的分析结果对象');
    projectData.analysisResults = {
      hourlyData: createEmptyHourlyDataArray(),
      dailyData: [],
      monthlyData: [],
      yearlyData: {
        pvGeneration: 0,
        storageCharge: 0,
        storageDischarge: 0,
        electricityConsumption: 0,
        gridExport: 0,
        gridImport: 0,
        gridExportIncome: 0,
        pvBenefit: 0,
        storageBenefit: 0,
        totalBenefit: 0,
        roi: 0,
        paybackPeriod: 0
      },
      analysisCompleted: false,
      analysisDate: ''
    };
  }

  // 确保hourlyData存在且长度为8760
  if (!projectData.analysisResults.hourlyData || projectData.analysisResults.hourlyData.length !== 8760) {
    console.log('创建新的8760条小时数据');
    projectData.analysisResults.hourlyData = createEmptyHourlyDataArray();
  }

  // 应用电力使用数据到小时数据中
  console.log('应用电力使用数据到小时数据中');
  try {
    // 使用try-catch块来捕获可能的只读对象错误
    projectData.analysisResults.hourlyData = applyElectricityUsageToHourlyData(
      projectData.analysisResults.hourlyData,
      electricityUsage
    );
  } catch (error) {
    console.error('应用电力使用数据时出错:', error);
    // 如果出错，创建一个新的分析结果对象
    projectData.analysisResults = {
      ...projectData.analysisResults,
      hourlyData: applyElectricityUsageToHourlyData(
        createEmptyHourlyDataArray(),
        electricityUsage
      )
    };
  }

  // 更新项目的更新时间
  projectData.updatedAt = new Date().toISOString();

  return projectData;
};

/**
 * 计算项目的日数据
 * @param hourlyData 小时数据数组
 * @returns 日数据数组
 */
export const calculateDailyData = (hourlyData: ProjectHourlyData[]) => {
  // 每月的天数（非闰年）
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  const dailyData = [];

  let hourIndex = 0;
  for (let month = 1; month <= 12; month++) {
    for (let day = 1; day <= daysInMonth[month - 1]; day++) {
      // 初始化当天数据
      const dayData = {
        day,
        month,
        pvGeneration: 0,
        storageCharge: 0,
        storageDischarge: 0,
        electricityConsumption: 0,
        gridExport: 0,
        gridImport: 0,
        gridExportIncome: 0,
        pvBenefit: 0,
        storageBenefit: 0,
        totalBenefit: 0
      };

      // 累加当天24小时的数据
      for (let h = 0; h < 24; h++) {
        const hourData = hourlyData[hourIndex];

        dayData.pvGeneration += hourData.pvGeneration;
        dayData.electricityConsumption += hourData.electricityConsumption;
        dayData.gridExport += hourData.gridExport;
        dayData.gridImport += hourData.gridImport;
        dayData.gridExportIncome += hourData.gridExportIncome;
        dayData.pvBenefit += hourData.pvBenefit;
        dayData.storageBenefit += hourData.storageBenefit;
        dayData.totalBenefit += hourData.totalBenefit;

        // 计算储能充放电
        if (hourData.storageCharge > 0) {
          dayData.storageCharge += hourData.storageCharge;
        } else if (hourData.storageCharge < 0) {
          dayData.storageDischarge -= hourData.storageCharge; // 转为正数
        }

        hourIndex++;
      }

      dailyData.push(dayData);
    }
  }

  return dailyData;
};

/**
 * 计算项目的月数据
 * @param dailyData 日数据数组
 * @returns 月数据数组
 */
export const calculateMonthlyData = (dailyData: any[]) => {
  const monthlyData = [];

  // 每月的天数（非闰年）
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

  let dayIndex = 0;
  for (let month = 1; month <= 12; month++) {
    // 初始化当月数据
    const monthData = {
      month,
      pvGeneration: 0,
      storageCharge: 0,
      storageDischarge: 0,
      electricityConsumption: 0,
      gridExport: 0,
      gridImport: 0,
      gridExportIncome: 0,
      pvBenefit: 0,
      storageBenefit: 0,
      totalBenefit: 0
    };

    // 累加当月每天的数据
    for (let d = 0; d < daysInMonth[month - 1]; d++) {
      const dayData = dailyData[dayIndex];

      monthData.pvGeneration += dayData.pvGeneration;
      monthData.storageCharge += dayData.storageCharge;
      monthData.storageDischarge += dayData.storageDischarge;
      monthData.electricityConsumption += dayData.electricityConsumption;
      monthData.gridExport += dayData.gridExport;
      monthData.gridImport += dayData.gridImport;
      monthData.gridExportIncome += dayData.gridExportIncome;
      monthData.pvBenefit += dayData.pvBenefit;
      monthData.storageBenefit += dayData.storageBenefit;
      monthData.totalBenefit += dayData.totalBenefit;

      dayIndex++;
    }

    monthlyData.push(monthData);
  }

  return monthlyData;
};

/**
 * 计算项目的年数据
 * @param monthlyData 月数据数组
 * @returns 年数据
 */
export const calculateYearlyData = (monthlyData: any[]) => {
  // 初始化年数据
  const yearData = {
    pvGeneration: 0,
    storageCharge: 0,
    storageDischarge: 0,
    electricityConsumption: 0,
    gridExport: 0,
    gridImport: 0,
    gridExportIncome: 0,
    pvBenefit: 0,
    storageBenefit: 0,
    totalBenefit: 0,
    roi: 0,
    paybackPeriod: 0
  };

  // 累加全年12个月的数据
  monthlyData.forEach(monthData => {
    yearData.pvGeneration += monthData.pvGeneration;
    yearData.storageCharge += monthData.storageCharge;
    yearData.storageDischarge += monthData.storageDischarge;
    yearData.electricityConsumption += monthData.electricityConsumption;
    yearData.gridExport += monthData.gridExport;
    yearData.gridImport += monthData.gridImport;
    yearData.gridExportIncome += monthData.gridExportIncome;
    yearData.pvBenefit += monthData.pvBenefit;
    yearData.storageBenefit += monthData.storageBenefit;
    yearData.totalBenefit += monthData.totalBenefit;
  });

  return yearData;
};
