/**
 * 系统设置路由
 */
const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const multer = require('multer');

// 数据存储路径
const dataDir = path.join(__dirname, '../../data/settings');
const settingsPath = path.join(dataDir, 'settings.json');

// 上传目录
const uploadsDir = path.join(__dirname, '../../uploads');

// 确保数据目录存在
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 确保上传目录存在
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // 保留原始文件名但添加时间戳
    const uniqueFilename = `${Date.now()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 只允许图片
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

// 配置上传
const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 限制文件大小为5MB
  }
});

// 获取系统设置
router.get('/', (req, res) => {
  try {
    // 如果设置文件不存在，创建默认设置
    if (!fs.existsSync(settingsPath)) {
      const defaultSettings = {
        theme: 'light',
        language: 'zh-CN',
        cacheSize: 32, // MB
        logo: null,
        companyName: '',
        updatedAt: new Date().toISOString()
      };
      
      fs.writeFileSync(settingsPath, JSON.stringify(defaultSettings, null, 2));
      return res.json({ success: true, data: defaultSettings });
    }

    const settingsData = fs.readFileSync(settingsPath, 'utf8');
    const settings = JSON.parse(settingsData);

    res.json({ success: true, data: settings });
  } catch (err) {
    console.error('获取系统设置时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 更新系统设置
router.put('/', (req, res) => {
  try {
    const { theme, language, cacheSize, companyName } = req.body;
    
    // 读取现有设置或创建默认设置
    let settings = {};
    if (fs.existsSync(settingsPath)) {
      const settingsData = fs.readFileSync(settingsPath, 'utf8');
      settings = JSON.parse(settingsData);
    } else {
      settings = {
        theme: 'light',
        language: 'zh-CN',
        cacheSize: 32,
        logo: null,
        companyName: '',
        updatedAt: new Date().toISOString()
      };
    }

    // 更新设置
    const updatedSettings = {
      ...settings,
      theme: theme !== undefined ? theme : settings.theme,
      language: language !== undefined ? language : settings.language,
      cacheSize: cacheSize !== undefined ? cacheSize : settings.cacheSize,
      companyName: companyName !== undefined ? companyName : settings.companyName,
      updatedAt: new Date().toISOString()
    };

    fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2));

    res.json({ success: true, data: updatedSettings });
  } catch (err) {
    console.error('更新系统设置时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 上传Logo
router.post('/logo', upload.single('logo'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: '没有文件被上传' });
    }

    // 读取现有设置或创建默认设置
    let settings = {};
    if (fs.existsSync(settingsPath)) {
      const settingsData = fs.readFileSync(settingsPath, 'utf8');
      settings = JSON.parse(settingsData);
    } else {
      settings = {
        theme: 'light',
        language: 'zh-CN',
        cacheSize: 32,
        logo: null,
        companyName: '',
        updatedAt: new Date().toISOString()
      };
    }

    // 如果之前有Logo，删除旧文件
    if (settings.logo && settings.logo.filename) {
      const oldLogoPath = path.join(uploadsDir, settings.logo.filename);
      if (fs.existsSync(oldLogoPath)) {
        fs.unlinkSync(oldLogoPath);
      }
    }

    // 更新Logo信息
    settings.logo = {
      originalName: req.file.originalname,
      filename: req.file.filename,
      path: `/uploads/${req.file.filename}`,
      mimetype: req.file.mimetype,
      size: req.file.size,
      uploadedAt: new Date().toISOString()
    };
    
    settings.updatedAt = new Date().toISOString();

    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));

    res.json({ success: true, data: settings });
  } catch (err) {
    console.error('上传Logo时出错:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

module.exports = router;
