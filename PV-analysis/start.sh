#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认管理员账号密码
DEFAULT_ADMIN_USERNAME="admin"
DEFAULT_ADMIN_PASSWORD="admin"

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "光伏+储能项目经济性分析系统启动脚本"
    echo ""
    echo "用法: ./start.sh [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                显示帮助信息"
    echo "  -u, --username USERNAME   设置管理员用户名 (默认: admin)"
    echo "  -p, --password PASSWORD   设置管理员密码 (默认: admin)"
    echo "  --port PORT               设置应用端口 (默认: 5173)"
    echo ""
    echo "示例:"
    echo "  ./start.sh                使用默认设置启动应用"
    echo "  ./start.sh -u root -p 123456  使用自定义管理员账号启动应用"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--username)
                ADMIN_USERNAME="$2"
                shift 2
                ;;
            -p|--password)
                ADMIN_PASSWORD="$2"
                shift 2
                ;;
            --port)
                APP_PORT="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 设置默认值（如果未指定）
    ADMIN_USERNAME=${ADMIN_USERNAME:-$DEFAULT_ADMIN_USERNAME}
    ADMIN_PASSWORD=${ADMIN_PASSWORD:-$DEFAULT_ADMIN_PASSWORD}
    APP_PORT=${APP_PORT:-5173}
}

# 创建或更新管理员账号
setup_admin_account() {
    print_info "设置管理员账号: $ADMIN_USERNAME"

    # 这里我们将管理员账号信息写入临时环境变量文件
    # 实际项目中，您可能需要将这些信息写入数据库或配置文件
    cat > .env.local << EOF
VITE_ADMIN_USERNAME=$ADMIN_USERNAME
VITE_ADMIN_PASSWORD=$ADMIN_PASSWORD
EOF

    # 确保环境变量文件存在于正确的位置
    if [ ! -f ".env.local" ]; then
        print_error "无法创建环境变量文件"
        exit 1
    fi

    print_success "管理员账号设置完成"
}

# 启动应用
start_app() {
    print_info "正在启动应用，端口: $APP_PORT..."

    # 检查是否有正在运行的实例
    if command -v lsof >/dev/null 2>&1; then
        if lsof -i :$APP_PORT > /dev/null 2>&1; then
            print_warning "端口 $APP_PORT 已被占用，尝试停止现有实例..."
            kill $(lsof -t -i:$APP_PORT) 2>/dev/null || true
            sleep 2
        fi
    else
        print_warning "未找到lsof命令，无法检查端口占用情况"
    fi

    # 设置环境变量并启动应用
    export VITE_ADMIN_USERNAME=$ADMIN_USERNAME
    export VITE_ADMIN_PASSWORD=$ADMIN_PASSWORD

    print_info "启动应用中，请稍候..."
    print_info "超级管理员账号: $ADMIN_USERNAME"
    print_info "超级管理员密码: $ADMIN_PASSWORD"

    # 启动应用
    npm run dev -- --port $APP_PORT &
    APP_PID=$!

    # 保存进程ID
    echo $APP_PID > .app.pid

    # 等待应用启动
    sleep 3

    # 检查应用是否成功启动
    if ps -p $APP_PID > /dev/null; then
        print_success "应用已成功启动！"
        print_info "访问地址: http://localhost:$APP_PORT"
        print_info "管理员账号: $ADMIN_USERNAME"
        print_info "管理员密码: $ADMIN_PASSWORD"
    else
        print_error "应用启动失败，请检查日志"
        exit 1
    fi
}

# 检查登录组件是否支持超级管理员账号
check_login_component() {
    print_info "检查登录组件是否支持超级管理员账号..."

    # 检查Login.tsx文件是否存在
    if [ ! -f "src/pages/auth/Login.tsx" ]; then
        print_error "登录组件文件不存在: src/pages/auth/Login.tsx"
        return 1
    fi

    # 检查是否已经支持环境变量中的管理员账号
    if grep -q "import.meta.env.VITE_ADMIN_USERNAME" src/pages/auth/Login.tsx; then
        print_success "登录组件已支持超级管理员账号"
        return 0
    fi

    print_warning "登录组件不支持超级管理员账号，请确保已按照说明修改Login.tsx文件"
    return 0
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"

    # 显示欢迎信息
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统启动脚本"
    echo "=================================================="
    echo ""

    # 设置管理员账号
    setup_admin_account

    # 检查登录组件
    check_login_component

    # 启动应用
    start_app

    echo ""
    echo "=================================================="
    echo "  应用已成功启动"
    echo "  按 Ctrl+C 停止应用"
    echo "=================================================="
    echo "  超级管理员账号: $ADMIN_USERNAME"
    echo "  超级管理员密码: $ADMIN_PASSWORD"
    echo "=================================================="
}

# 执行主函数
main "$@"
