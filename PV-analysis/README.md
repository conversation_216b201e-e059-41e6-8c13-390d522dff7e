# 光伏+储能项目经济性分析系统

一个用于分析光伏+储能项目经济可行性的跨平台Web应用程序。

## 项目概述

本系统是一个基于React的跨平台网页应用，用于分析光伏+储能项目的经济可行性。系统包括项目库、数据库和系统设置等功能模块，支持多语言和主题切换。

### 主要功能

- **项目管理**：创建、编辑、删除和分析光伏+储能项目
- **数据库管理**：管理光照数据、电价政策、供应商和设备信息
- **分析功能**：分析项目的经济性，包括投资回报率、收益率等指标
- **报告生成**：生成项目分析报告，支持PDF和长图片导出
- **多语言支持**：支持中文、英文和日文
- **主题切换**：支持浅色和深色主题

## 技术栈

### 前端

- **React**：用于构建用户界面
- **Vite**：现代前端构建工具
- **Redux Toolkit**：状态管理
- **Ant Design**：UI组件库
- **Echarts**：数据可视化
- **i18next**：国际化支持
- **TypeScript**：静态类型检查

### 后端

- **Node.js**：JavaScript运行时
- **Express**：Web应用框架
- **Multer**：文件上传处理

## 安装与部署

### 系统要求

- **Node.js**：18.0.0或更高版本
- **npm**：8.0.0或更高版本

### 快速部署

#### macOS

```bash
cd deploy
chmod +x deploy_macos.sh
./deploy_macos.sh
```

#### Windows

```cmd
cd deploy
deploy_windows.bat
```

### 手动部署

1. 克隆仓库
   ```bash
   git clone https://github.com/wsd07/PV-analysis.git
   cd PV-analysis
   ```

2. 安装依赖
   ```bash
   # 安装前端依赖
   npm install

   # 安装服务器依赖
   cd server
   npm install
   cd ..
   ```

3. 构建前端应用
   ```bash
   npm run build
   ```

4. 启动服务器
   ```bash
   cd server
   npm run dev
   ```

5. 启动前端应用（开发模式）
   ```bash
   npm run dev
   ```

## 项目结构

```
src/
├── assets/            # 静态资源
├── components/        # 共享组件
│   ├── common/        # 通用组件
│   ├── layout/        # 布局组件
│   ├── charts/        # 图表组件
│   └── forms/         # 表单组件
├── config/            # 配置文件
├── hooks/             # 自定义钩子
├── locales/           # 国际化文件
├── pages/             # 页面组件
├── services/          # API服务
├── store/             # Redux状态管理
├── styles/            # 全局样式
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
├── App.tsx            # 应用入口组件
└── main.tsx           # 应用入口文件

server/
├── data/              # 数据存储目录
├── src/               # 服务器源代码
├── uploads/           # 上传文件目录
└── package.json       # 服务器依赖配置

deploy/                # 部署脚本和文档
```

## 使用说明

1. 启动应用后，使用默认管理员账号登录：
   - 用户名：admin
   - 密码：admin

2. 在项目库中创建新项目，填写基本信息、光照数据、电价政策等

3. 完成信息填写后，点击"开始分析"按钮进行项目分析

4. 在分析结果页面查看详细的经济性分析数据和图表

5. 可以导出分析报告为PDF或长图片格式

## 许可证

[MIT](LICENSE)

## 联系方式

如有任何问题或建议，请联系项目维护者。
