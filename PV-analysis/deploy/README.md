# 光伏+储能项目经济性分析系统部署指南

本文档提供了光伏+储能项目经济性分析系统的完整部署指南，适用于macOS和Windows 11系统。

## 系统要求

### macOS
- macOS 10.15 (Catalina) 或更高版本
- 至少4GB内存
- 至少2GB可用磁盘空间
- Node.js 18.0.0 或更高版本

### Windows 11
- Windows 11 (21H2) 或更高版本
- 至少4GB内存
- 至少2GB可用磁盘空间
- Node.js 18.0.0 或更高版本

## 快速部署

### macOS

1. 打开终端
2. 进入项目根目录
3. 执行以下命令：

```bash
cd deploy
chmod +x deploy_macos.sh
./deploy_macos.sh
```

### Windows 11

1. 打开文件资源管理器，进入项目根目录
2. 双击 `deploy` 文件夹
3. 双击 `deploy_windows.bat` 文件

## 部署选项

部署脚本支持以下选项：

### macOS

```bash
./deploy_macos.sh [选项]
```

选项：
- `-h, --help`：显示帮助信息
- `-u, --username USERNAME`：设置管理员用户名（默认：admin）
- `-p, --password PASSWORD`：设置管理员密码（默认：admin）
- `--port PORT`：设置应用端口（默认：5173）
- `--server-port PORT`：设置服务器端口（默认：3001）
- `--build-only`：仅构建应用，不启动
- `--start-only`：仅启动应用，不构建

### Windows

```cmd
deploy_windows.bat [选项]
```

选项：
- `-h`：显示帮助信息
- `-u USERNAME`：设置管理员用户名（默认：admin）
- `-p PASSWORD`：设置管理员密码（默认：admin）
- `-port PORT`：设置应用端口（默认：5173）
- `-server-port PORT`：设置服务器端口（默认：3001）
- `-build-only`：仅构建应用，不启动
- `-start-only`：仅启动应用，不构建

## 详细部署步骤

### 1. 安装Node.js

如果您的系统尚未安装Node.js，部署脚本会自动下载并安装适合您系统的Node.js版本。

### 2. 安装依赖

部署脚本会自动安装项目所需的所有依赖，包括前端和服务器端依赖。

### 3. 构建应用

部署脚本会自动构建应用，生成可部署的静态文件。

### 4. 启动应用

部署脚本会自动启动应用，包括前端和服务器端。

## 访问应用

部署完成后，您可以通过以下地址访问应用：

- 前端应用：http://localhost:5173（或您指定的端口）
- 服务器端：http://localhost:3001（或您指定的端口）

默认管理员账号：
- 用户名：admin
- 密码：admin

## 常见问题

### 端口被占用

如果端口被占用，部署脚本会尝试停止占用端口的进程。如果无法停止，您可以通过以下方式指定其他端口：

```bash
# macOS
./deploy_macos.sh --port 8080 --server-port 3002

# Windows
deploy_windows.bat -port 8080 -server-port 3002
```

### 无法启动应用

如果应用无法启动，请检查以下几点：

1. 确保Node.js版本正确（18.0.0或更高）
2. 确保所有依赖已正确安装
3. 检查端口是否被占用
4. 检查日志文件（`deploy/logs`目录）

### 数据存储

应用数据存储在以下位置：

- 项目数据：`server/data/projects`
- 光照数据：`server/data/irradiance`
- 电价政策：`server/data/electricity-prices`
- 供应商数据：`server/data/suppliers`
- 设备数据：`server/data/equipment`

## 更新应用

要更新应用，请执行以下步骤：

1. 获取最新代码
2. 运行部署脚本

```bash
# macOS
./deploy_macos.sh

# Windows
deploy_windows.bat
```

## 卸载应用

要卸载应用，请执行以下步骤：

1. 停止应用（按Ctrl+C）
2. 删除项目文件夹

## 技术支持

如有任何问题，请联系技术支持团队。
