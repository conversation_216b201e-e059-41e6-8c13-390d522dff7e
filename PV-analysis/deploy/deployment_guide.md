# 光伏+储能项目经济性分析系统部署指南

## 目录

1. [系统概述](#系统概述)
2. [系统要求](#系统要求)
3. [部署方式](#部署方式)
4. [详细部署步骤](#详细部署步骤)
5. [配置说明](#配置说明)
6. [常见问题](#常见问题)
7. [维护与更新](#维护与更新)

## 系统概述

光伏+储能项目经济性分析系统是一个基于React的跨平台网页应用，用于分析光伏+储能项目的经济可行性。系统包括项目库、数据库和系统设置等功能模块，支持多语言和主题切换。

系统架构分为前端和服务器端两部分：
- 前端：基于React、Vite、Ant Design等技术栈开发
- 服务器端：基于Node.js、Express等技术栈开发，提供数据存储和API服务

## 系统要求

### 硬件要求

- 处理器：双核处理器，1.8GHz或更高
- 内存：至少4GB RAM
- 磁盘空间：至少2GB可用空间

### 软件要求

#### macOS
- macOS 10.15 (Catalina) 或更高版本
- Node.js 18.0.0 或更高版本
- npm 8.0.0 或更高版本

#### Windows
- Windows 11 或更高版本
- Node.js 18.0.0 或更高版本
- npm 8.0.0 或更高版本

## 部署方式

系统提供以下几种部署方式：

1. **快速部署**：使用提供的部署脚本自动完成所有步骤
2. **手动部署**：按照详细步骤手动部署系统
3. **安装包部署**：使用预构建的安装包进行部署

## 详细部署步骤

### 方式一：快速部署

#### macOS

1. 打开终端
2. 进入项目根目录
3. 执行以下命令：

```bash
cd deploy
chmod +x deploy_macos.sh
./deploy_macos.sh
```

#### Windows

1. 打开命令提示符
2. 进入项目根目录
3. 执行以下命令：

```cmd
cd deploy
deploy_windows.bat
```

### 方式二：手动部署

#### 步骤1：安装Node.js

确保您的系统已安装Node.js 18.0.0或更高版本。

#### 步骤2：克隆或下载项目代码

```bash
git clone <项目仓库URL>
cd <项目目录>
```

#### 步骤3：安装依赖

```bash
# 安装前端依赖
npm install

# 安装服务器依赖
cd server
npm install
cd ..
```

#### 步骤4：构建前端应用

```bash
npm run build
```

#### 步骤5：配置环境变量

创建`.env.local`文件，设置管理员账号：

```
VITE_ADMIN_USERNAME=admin
VITE_ADMIN_PASSWORD=admin
```

#### 步骤6：启动服务器

```bash
cd server
npm run dev
```

#### 步骤7：启动前端应用

打开新的终端窗口，执行：

```bash
npm run preview
```

### 方式三：安装包部署

#### macOS

1. 下载macOS安装包（PVAnalyzer-macOS.dmg）
2. 双击安装包，将应用拖到Applications文件夹
3. 双击应用图标启动

#### Windows

1. 下载Windows安装包（PVAnalyzer-Windows.zip）
2. 解压安装包
3. 双击`install.bat`进行安装
4. 安装完成后，双击桌面上的快捷方式启动应用

## 配置说明

### 端口配置

默认端口配置：
- 前端应用：5173
- 服务器：3001

如需修改端口，可以通过以下方式：

#### 使用部署脚本时

```bash
# macOS
./deploy_macos.sh --port 8080 --server-port 3002

# Windows
deploy_windows.bat -port 8080 -server-port 3002
```

#### 手动配置

前端端口：
```bash
npm run dev -- --port <端口号>
# 或
npm run preview -- --port <端口号>
```

服务器端口：
```bash
cd server
PORT=<端口号> npm run dev
```

### 管理员账号配置

默认管理员账号：
- 用户名：admin
- 密码：admin

如需修改，可以通过以下方式：

#### 使用部署脚本时

```bash
# macOS
./deploy_macos.sh -u <用户名> -p <密码>

# Windows
deploy_windows.bat -u <用户名> -p <密码>
```

#### 手动配置

修改`.env.local`文件：

```
VITE_ADMIN_USERNAME=<用户名>
VITE_ADMIN_PASSWORD=<密码>
```

## 常见问题

### 1. 端口被占用

**问题**：启动应用时提示端口被占用。

**解决方案**：
- 使用不同的端口：
  ```bash
  # macOS
  ./deploy_macos.sh --port 8080 --server-port 3002
  
  # Windows
  deploy_windows.bat -port 8080 -server-port 3002
  ```
- 或者手动终止占用端口的进程：
  ```bash
  # macOS
  lsof -i :<端口号>
  kill <进程ID>
  
  # Windows
  netstat -ano | findstr :<端口号>
  taskkill /F /PID <进程ID>
  ```

### 2. Node.js版本过低

**问题**：提示Node.js版本过低。

**解决方案**：
- 升级Node.js到18.0.0或更高版本：
  ```bash
  # 使用nvm（推荐）
  nvm install 18
  nvm use 18
  
  # 或直接从官网下载安装
  # https://nodejs.org/
  ```

### 3. 无法连接到服务器

**问题**：前端应用无法连接到服务器。

**解决方案**：
- 确保服务器正在运行
- 检查API配置是否正确（src/services/api.ts中的baseURL）
- 检查防火墙设置是否允许端口通信

### 4. 数据无法保存

**问题**：项目数据无法保存或加载。

**解决方案**：
- 确保服务器目录下的data目录存在且有写入权限
- 检查服务器日志（deploy/logs/server.log）查看详细错误信息

## 维护与更新

### 日常维护

1. **备份数据**：定期备份`server/data`目录，保存重要数据
2. **清理日志**：定期清理`deploy/logs`目录下的日志文件
3. **检查磁盘空间**：确保系统有足够的磁盘空间

### 系统更新

当有新版本发布时，可以通过以下步骤更新系统：

1. 备份当前数据（`server/data`目录）
2. 获取最新代码
3. 运行部署脚本重新部署系统
4. 恢复数据备份（如有必要）

```bash
# 备份数据
cp -r server/data /backup/data

# 更新代码
git pull

# 重新部署
cd deploy
./deploy_macos.sh  # 或 deploy_windows.bat

# 恢复数据（如有必要）
cp -r /backup/data server/
```

### 故障排除

如遇系统故障，请检查以下日志文件：

- 前端应用日志：`deploy/logs/app.log`
- 服务器日志：`deploy/logs/server.log`
- 部署日志：`deploy/logs/deploy_*.log`

如需进一步帮助，请联系技术支持团队。
