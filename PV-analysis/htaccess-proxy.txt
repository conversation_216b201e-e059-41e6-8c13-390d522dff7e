# .htaccess文件，用于代理API请求到Node.js服务器
# 将此文件内容添加到 ~/domains/pv-analysis.top/public_html/.htaccess 文件中

# 启用重写引擎
RewriteEngine On

# 代理API请求到Node.js服务器
RewriteRule ^api/(.*)$ http://localhost:3001/api/$1 [P,L]

# 设置代理头信息
<IfModule mod_headers.c>
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Port "443"
</IfModule>

# 处理前端路由
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L,QSA]
