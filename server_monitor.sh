#!/bin/bash

# 服务器监控和自动重启脚本
# 光伏+储能项目经济性分析系统

set -e

# 配置变量
PROJECT_DIR="~/domains/pv-analysis.top/public_html"
SERVER_DIR="$PROJECT_DIR/server"
API_PORT="3001"
HEALTH_URL="http://localhost:$API_PORT/api/health"
LOG_FILE="$SERVER_DIR/monitor.log"
MAX_RESTART_ATTEMPTS=3
RESTART_DELAY=10

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 检查服务器是否运行
check_server() {
    if curl -s --max-time 5 "$HEALTH_URL" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 检查进程是否运行
check_process() {
    if [ -f "$SERVER_DIR/.server.pid" ]; then
        local pid=$(cat "$SERVER_DIR/.server.pid")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 启动服务器
start_server() {
    print_info "启动服务器..."
    
    cd "$SERVER_DIR"
    
    # 加载NVM
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    nvm use node > /dev/null 2>&1
    
    # 停止现有进程
    if [ -f ".server.pid" ]; then
        local old_pid=$(cat .server.pid)
        if ps -p "$old_pid" > /dev/null 2>&1; then
            print_info "停止现有进程 PID: $old_pid"
            kill "$old_pid" 2>/dev/null || true
            sleep 2
            if ps -p "$old_pid" > /dev/null 2>&1; then
                kill -9 "$old_pid" 2>/dev/null || true
            fi
        fi
        rm -f .server.pid
    fi
    
    # 清理端口占用
    pkill -f "node.*src/index.js" 2>/dev/null || true
    sleep 2
    
    # 尝试使用PM2启动
    if command -v pm2 > /dev/null 2>&1; then
        print_info "使用PM2启动服务器..."
        pm2 delete pv-server 2>/dev/null || true
        pm2 start src/index.js --name pv-server --log server.log --error server.error.log
        pm2 save
    else
        print_info "使用nohup启动服务器..."
        nohup npm start > server.log 2>&1 &
        local server_pid=$!
        echo "$server_pid" > .server.pid
        print_info "服务器已启动，PID: $server_pid"
    fi
    
    # 等待服务器启动
    sleep 5
    
    # 验证启动
    local attempts=0
    while [ $attempts -lt 10 ]; do
        if check_server; then
            print_success "服务器启动成功"
            return 0
        fi
        attempts=$((attempts + 1))
        print_info "等待服务器启动... ($attempts/10)"
        sleep 2
    done
    
    print_error "服务器启动失败"
    return 1
}

# 重启服务器
restart_server() {
    local attempt=$1
    print_warning "尝试重启服务器 (第 $attempt 次)"
    
    if start_server; then
        print_success "服务器重启成功"
        return 0
    else
        print_error "服务器重启失败"
        return 1
    fi
}

# 主监控循环
monitor_server() {
    print_info "开始监控服务器..."
    
    local restart_count=0
    
    while true; do
        if check_server; then
            print_info "服务器运行正常"
            restart_count=0
        else
            print_warning "服务器健康检查失败"
            
            # 检查进程是否还在运行
            if check_process; then
                print_warning "进程存在但健康检查失败，可能是服务异常"
            else
                print_error "服务器进程已停止"
            fi
            
            # 尝试重启
            if [ $restart_count -lt $MAX_RESTART_ATTEMPTS ]; then
                restart_count=$((restart_count + 1))
                
                if restart_server $restart_count; then
                    restart_count=0
                else
                    print_error "重启失败，等待 $RESTART_DELAY 秒后重试"
                    sleep $RESTART_DELAY
                fi
            else
                print_error "达到最大重启次数 ($MAX_RESTART_ATTEMPTS)，停止自动重启"
                print_error "请手动检查服务器问题"
                exit 1
            fi
        fi
        
        # 等待下次检查
        sleep 30
    done
}

# 主函数
main() {
    case "${1:-monitor}" in
        "start")
            start_server
            ;;
        "restart")
            restart_server 1
            ;;
        "monitor")
            monitor_server
            ;;
        "check")
            if check_server; then
                print_success "服务器运行正常"
                exit 0
            else
                print_error "服务器异常"
                exit 1
            fi
            ;;
        *)
            echo "用法: $0 {start|restart|monitor|check}"
            echo "  start   - 启动服务器"
            echo "  restart - 重启服务器"
            echo "  monitor - 监控服务器并自动重启"
            echo "  check   - 检查服务器状态"
            exit 1
            ;;
    esac
}

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# 执行主函数
main "$@"
