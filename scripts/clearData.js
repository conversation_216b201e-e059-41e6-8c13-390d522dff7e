/**
 * 清除数据脚本
 * 用于清除系统中的虚拟数据
 * 
 * 使用方法：
 * node scripts/clearData.js [all|projects|irradiance]
 */

// 清除localStorage中的项目数据
const clearProjectsLocalStorage = () => {
  try {
    // 清除所有以pv_storage_开头的项目数据
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_storage_')) {
        localStorage.removeItem(key);
      }
    });
    console.log('项目数据已从localStorage中清除');
  } catch (error) {
    console.error('清除项目数据失败:', error);
  }
};

// 清除localStorage中的光照数据
const clearIrradianceLocalStorage = () => {
  try {
    // 清除所有以pv_irradiance_开头的光照数据
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_irradiance_')) {
        localStorage.removeItem(key);
      }
    });
    console.log('光照数据已从localStorage中清除');
  } catch (error) {
    console.error('清除光照数据失败:', error);
  }
};

// 清除所有虚拟数据
const clearAllData = () => {
  clearProjectsLocalStorage();
  clearIrradianceLocalStorage();
  console.log('所有虚拟数据已清除');
};

// 清除项目库数据
const clearProjectsData = () => {
  clearProjectsLocalStorage();
  console.log('项目库数据已清除');
};

// 清除光照数据库数据
const clearIrradianceData = () => {
  clearIrradianceLocalStorage();
  console.log('光照数据库数据已清除');
};

// 主函数
const main = () => {
  // 获取命令行参数
  const args = process.argv.slice(2);
  const command = args[0] || 'all';

  // 创建一个模拟的localStorage对象
  global.localStorage = {
    _data: {},
    setItem: function(id, val) {
      this._data[id] = String(val);
    },
    getItem: function(id) {
      return this._data[id] || null;
    },
    removeItem: function(id) {
      delete this._data[id];
    },
    clear: function() {
      this._data = {};
    }
  };

  // 执行命令
  switch (command) {
    case 'all':
      clearAllData();
      break;
    case 'projects':
      clearProjectsData();
      break;
    case 'irradiance':
      clearIrradianceData();
      break;
    default:
      console.error('未知命令:', command);
      console.log('使用方法: node scripts/clearData.js [all|projects|irradiance]');
      process.exit(1);
  }

  console.log('数据清除完成');
};

// 执行主函数
main();
