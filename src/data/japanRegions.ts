/**
 * 日本行政区划数据
 * 按照地方（区域）和县府两级组织
 */

// 县府类型
export interface Prefecture {
  value: string;
  label: string;
  number?: number; // 地图上的编号
  districts?: District[]; // 保留区市町村数据以便兼容
}

// 区市町村类型
export interface District {
  value: string;
  label: string;
}

// 地方（区域）类型
export interface Region {
  value: string;
  label: string;
  color: string; // 地图上的颜色
  prefectures: Prefecture[];
}

// 日本行政区划数据
export const japanRegions: Region[] = [
  {
    value: "hokkaido",
    label: "北海道地方",
    color: "#FF9999", // 红色
    prefectures: [
      { value: "hokkaido", label: "北海道", number: 1 }
    ]
  },
  {
    value: "tohoku",
    label: "东北地方",
    color: "#FFFF99", // 黄色
    prefectures: [
      { value: "aomori", label: "青森县", number: 2 },
      { value: "iwate", label: "岩手县", number: 3 },
      { value: "miyagi", label: "宫城县", number: 4 },
      { value: "akita", label: "秋田县", number: 5 },
      { value: "yamagata", label: "山形县", number: 6 },
      { value: "fukushima", label: "福岛县", number: 7 }
    ]
  },
  {
    value: "kanto",
    label: "关东地方",
    color: "#99FF99", // 绿色
    prefectures: [
      { value: "ibaraki", label: "茨城县", number: 8 },
      { value: "tochigi", label: "栃木县", number: 9 },
      { value: "gunma", label: "群马县", number: 10 },
      { value: "saitama", label: "埼玉县", number: 11 },
      { value: "chiba", label: "千叶县", number: 12 },
      { value: "tokyo", label: "东京都", number: 13 },
      { value: "kanagawa", label: "神奈川县", number: 14 }
    ]
  },
  {
    value: "chubu",
    label: "中部地方",
    color: "#99FFFF", // 青色
    prefectures: [
      { value: "niigata", label: "新潟县", number: 15 },
      { value: "toyama", label: "富山县", number: 16 },
      { value: "ishikawa", label: "石川县", number: 17 },
      { value: "fukui", label: "福井县", number: 18 },
      { value: "yamanashi", label: "山梨县", number: 19 },
      { value: "nagano", label: "长野县", number: 20 },
      { value: "gifu", label: "岐阜县", number: 21 },
      { value: "shizuoka", label: "静冈县", number: 22 },
      { value: "aichi", label: "爱知县", number: 23 }
    ]
  },
  {
    value: "kinki",
    label: "近畿地方",
    color: "#9999FF", // 紫色
    prefectures: [
      { value: "mie", label: "三重县", number: 24 },
      { value: "shiga", label: "滋贺县", number: 25 },
      { value: "kyoto", label: "京都府", number: 26 },
      { value: "osaka", label: "大阪府", number: 27 },
      { value: "hyogo", label: "兵库县", number: 28 },
      { value: "nara", label: "奈良县", number: 29 },
      { value: "wakayama", label: "和歌山县", number: 30 }
    ]
  },
  {
    value: "chugoku",
    label: "中国地方",
    color: "#FF9966", // 橙色
    prefectures: [
      { value: "tottori", label: "鸟取县", number: 31 },
      { value: "shimane", label: "岛根县", number: 32 },
      { value: "okayama", label: "冈山县", number: 33 },
      { value: "hiroshima", label: "广岛县", number: 34 },
      { value: "yamaguchi", label: "山口县", number: 35 }
    ]
  },
  {
    value: "shikoku",
    label: "四国地方",
    color: "#FF99FF", // 粉色
    prefectures: [
      { value: "tokushima", label: "德岛县", number: 36 },
      { value: "kagawa", label: "香川县", number: 37 },
      { value: "ehime", label: "爱媛县", number: 38 },
      { value: "kochi", label: "高知县", number: 39 }
    ]
  },
  {
    value: "kyushu",
    label: "九州及冲绳地方",
    color: "#CCCCCC", // 灰色
    prefectures: [
      { value: "fukuoka", label: "福冈县", number: 40 },
      { value: "saga", label: "佐贺县", number: 41 },
      { value: "nagasaki", label: "长崎县", number: 42 },
      { value: "kumamoto", label: "熊本县", number: 43 },
      { value: "oita", label: "大分县", number: 44 },
      { value: "miyazaki", label: "宫崎县", number: 45 },
      { value: "kagoshima", label: "鹿儿岛县", number: 46 },
      { value: "okinawa", label: "冲绳县", number: 47 }
    ]
  }
];

// 为了兼容旧代码，保留japanPrefectures数组
export const japanPrefectures: Prefecture[] = japanRegions.flatMap(region =>
  region.prefectures.map(prefecture => ({
    ...prefecture,
    districts: [] // 暂时保留空的districts数组以兼容旧代码
  }))
);

// 获取所有地方（区域）
export const getAllRegions = (): { value: string; label: string }[] => {
  return japanRegions.map(region => ({
    value: region.value,
    label: region.label
  }));
};

// 获取指定地方的县府
export const getPrefecturesByRegion = (regionValue: string): Prefecture[] => {
  const region = japanRegions.find(r => r.value === regionValue);
  return region ? region.prefectures : [];
};

// 获取所有县府
export const getAllPrefectures = (): Prefecture[] => {
  return japanRegions.flatMap(region => region.prefectures);
};

// 根据县府值获取所属地方
export const getRegionByPrefecture = (prefectureValue: string): Region | undefined => {
  return japanRegions.find(region =>
    region.prefectures.some(prefecture => prefecture.value === prefectureValue)
  );
};

// 根据县府值获取县府对象
export const getPrefectureByValue = (prefectureValue: string): Prefecture | undefined => {
  for (const region of japanRegions) {
    const prefecture = region.prefectures.find(p => p.value === prefectureValue);
    if (prefecture) {
      return prefecture;
    }
  }
  return undefined;
};

// 根据县府标签获取县府值
export const getPrefectureValueByLabel = (prefectureLabel: string): string => {
  for (const region of japanRegions) {
    const prefecture = region.prefectures.find(p => p.label === prefectureLabel);
    if (prefecture) {
      return prefecture.value;
    }
  }
  return '';
};

// 根据县府值获取县府标签
export const getPrefectureLabelByValue = (prefectureValue: string): string => {
  const prefecture = getPrefectureByValue(prefectureValue);
  return prefecture ? prefecture.label : '';
};

// 以下函数保留以兼容旧代码
export const getDistricts = (prefectureValue: string): District[] => {
  return [];
};

export const getDistrictValueByLabel = (prefectureValue: string, districtLabel: string): string => {
  return '';
};

export const getDistrictLabelByValue = (prefectureValue: string, districtValue: string): string => {
  return '';
};
