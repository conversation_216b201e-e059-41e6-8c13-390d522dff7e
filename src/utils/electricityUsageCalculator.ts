/**
 * 电力使用数据计算工具
 * 用于将用户输入的电力使用模式转换为8760个小时点的数据
 */

import { ElectricityUsage } from '../types/project';
import { ProjectHourlyData } from '../types/projectData';

/**
 * 计算全年8760个小时的用电量数据
 * @param electricityUsage 用户输入的电力使用数据
 * @returns 8760个小时点的用电量数据
 */
export const calculateHourlyElectricityUsage = (electricityUsage: ElectricityUsage): number[] => {
  // 初始化8760个小时的数组
  const hourlyUsage: number[] = new Array(8760).fill(0);

  // 如果没有数据，返回空数组
  if (!electricityUsage || !electricityUsage.data || electricityUsage.data.length === 0) {
    console.warn('电力使用数据为空，返回全0数组');
    return hourlyUsage;
  }

  // 每月的天数（非闰年）
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

  // 根据不同的电力使用模式计算
  switch (electricityUsage.type) {
    case 'sameEveryday': // 每天相同模式
      return calculateSameEverydayUsage(electricityUsage.data, daysInMonth);

    case 'sameEveryWeek': // 按星期几模式
    case 'patternByDay': // 按星期几模式（新命名）
      return calculateWeeklyPatternUsage(electricityUsage.data, daysInMonth);

    case 'monthlyDifferent': // 按月份模式
    case 'patternByMonth': // 按月份模式（新命名）
      return calculateMonthlyPatternUsage(electricityUsage.data, daysInMonth);

    case 'dailyDifferent': // 每天不同模式
      return calculateDailyDifferentUsage(electricityUsage.data);

    default:
      console.warn(`未知的电力使用模式: ${electricityUsage.type}，返回全0数组`);
      return hourlyUsage;
  }
};

/**
 * 计算每天相同模式的用电量
 * @param data 24小时用电数据
 * @param daysInMonth 每月天数数组
 * @returns 8760个小时点的用电量数据
 */
const calculateSameEverydayUsage = (data: any[], daysInMonth: number[]): number[] => {
  // 初始化8760个小时的数组
  const hourlyUsage: number[] = new Array(8760).fill(0);

  // 提取24小时的用电模式
  const hourlyPattern: number[] = new Array(24).fill(0);
  data.forEach(item => {
    if (typeof item.hour === 'number' && typeof item.value === 'number') {
      hourlyPattern[item.hour] = item.value;
    }
  });

  // 应用到全年每一天
  let hourIndex = 0;
  for (let month = 0; month < 12; month++) {
    for (let day = 0; day < daysInMonth[month]; day++) {
      for (let hour = 0; hour < 24; hour++) {
        hourlyUsage[hourIndex] = hourlyPattern[hour];
        hourIndex++;
      }
    }
  }

  return hourlyUsage;
};

/**
 * 计算按星期几模式的用电量
 * @param data 按星期几的用电数据
 * @param daysInMonth 每月天数数组
 * @returns 8760个小时点的用电量数据
 */
const calculateWeeklyPatternUsage = (data: any[], daysInMonth: number[]): number[] => {
  // 初始化8760个小时的数组
  const hourlyUsage: number[] = new Array(8760).fill(0);

  // 提取每周7天，每天24小时的用电模式
  const weeklyPattern: number[][] = Array(7).fill(0).map(() => Array(24).fill(0));

  data.forEach(item => {
    if (typeof item.day === 'number' && typeof item.hour === 'number' && typeof item.value === 'number') {
      const dayOfWeek = item.day % 7; // 确保day在0-6范围内
      weeklyPattern[dayOfWeek][item.hour] = item.value;
    }
  });

  // 计算2023年1月1日是星期几（2023-01-01是星期日，即0）
  let currentDayOfWeek = 0;

  // 应用到全年每一天
  let hourIndex = 0;
  for (let month = 0; month < 12; month++) {
    for (let day = 0; day < daysInMonth[month]; day++) {
      for (let hour = 0; hour < 24; hour++) {
        hourlyUsage[hourIndex] = weeklyPattern[currentDayOfWeek][hour];
        hourIndex++;
      }
      // 更新星期几
      currentDayOfWeek = (currentDayOfWeek + 1) % 7;
    }
  }

  return hourlyUsage;
};

/**
 * 计算按月份模式的用电量
 * @param data 按月份的用电数据
 * @param daysInMonth 每月天数数组
 * @returns 8760个小时点的用电量数据
 */
const calculateMonthlyPatternUsage = (data: any[], daysInMonth: number[]): number[] => {
  // 初始化8760个小时的数组
  const hourlyUsage: number[] = new Array(8760).fill(0);

  // 提取每月24小时的用电模式
  const monthlyPattern: number[][] = Array(12).fill(0).map(() => Array(24).fill(0));

  data.forEach(item => {
    if (typeof item.month === 'number' && typeof item.hour === 'number' && typeof item.value === 'number') {
      const monthIndex = (item.month - 1) % 12; // 确保month在0-11范围内
      monthlyPattern[monthIndex][item.hour] = item.value;
    }
  });

  // 应用到全年每一天
  let hourIndex = 0;
  for (let month = 0; month < 12; month++) {
    for (let day = 0; day < daysInMonth[month]; day++) {
      for (let hour = 0; hour < 24; hour++) {
        hourlyUsage[hourIndex] = monthlyPattern[month][hour];
        hourIndex++;
      }
    }
  }

  return hourlyUsage;
};

/**
 * 计算每天不同模式的用电量
 * @param data 365天的用电数据
 * @returns 8760个小时点的用电量数据
 */
const calculateDailyDifferentUsage = (data: any[]): number[] => {
  // 初始化8760个小时的数组
  const hourlyUsage: number[] = new Array(8760).fill(0);

  // 直接使用用户提供的每小时数据
  data.forEach(item => {
    if (typeof item.day === 'number' && typeof item.hour === 'number' && typeof item.value === 'number') {
      // 计算年中的小时索引
      const dayOfYear = item.day - 1; // 转为0-364
      if (dayOfYear >= 0 && dayOfYear < 365) {
        const hourIndex = dayOfYear * 24 + item.hour;
        if (hourIndex >= 0 && hourIndex < 8760) {
          hourlyUsage[hourIndex] = item.value;
        }
      }
    }
  });

  return hourlyUsage;
};

/**
 * 将计算得到的用电量数据应用到项目小时数据中
 * @param hourlyData 项目小时数据数组
 * @param electricityUsage 用户输入的电力使用数据
 * @returns 更新后的项目小时数据数组
 */
export const applyElectricityUsageToHourlyData = (
  hourlyData: ProjectHourlyData[],
  electricityUsage: ElectricityUsage
): ProjectHourlyData[] => {
  // 计算8760个小时的用电量
  const hourlyUsageValues = calculateHourlyElectricityUsage(electricityUsage);

  // 确保hourlyData长度正确
  if (hourlyData.length !== 8760) {
    console.error(`项目小时数据长度不正确: ${hourlyData.length}，应为8760`);
    return hourlyData;
  }

  // 应用用电量数据
  for (let i = 0; i < 8760; i++) {
    // 检查electricityConsumption的类型
    if (typeof hourlyData[i].electricityConsumption === 'number') {
      // 如果是数字，直接赋值
      hourlyData[i].electricityConsumption = hourlyUsageValues[i];
    } else {
      // 如果是对象，更新grid字段
      if (hourlyData[i].electricityConsumption && typeof hourlyData[i].electricityConsumption === 'object') {
        // 确保对象存在
        const consumption = hourlyData[i].electricityConsumption as {
          pv: number;
          storage: number;
          grid: number;
        };

        // 更新总用电量，暂时全部分配给grid
        consumption.grid = hourlyUsageValues[i];
        consumption.pv = 0;
        consumption.storage = 0;
      } else {
        // 如果electricityConsumption不存在或不是预期的类型，创建新对象
        hourlyData[i].electricityConsumption = {
          pv: 0,
          storage: 0,
          grid: hourlyUsageValues[i]
        };
      }
    }
  }

  console.log('应用电力使用数据完成，第一个小时点的用电量:',
    typeof hourlyData[0].electricityConsumption === 'number'
      ? hourlyData[0].electricityConsumption
      : JSON.stringify(hourlyData[0].electricityConsumption)
  );

  return hourlyData;
};
