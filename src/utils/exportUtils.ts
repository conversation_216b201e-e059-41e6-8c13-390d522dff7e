/**
 * 导出工具函数
 * 用于将数据导出为CSV等格式
 */
import { ProjectData, ProjectHourlyData } from '../types/projectData';

/**
 * 将项目小时数据导出为CSV格式
 * @param project 项目数据
 * @returns CSV格式的字符串
 */
export const exportProjectHourlyDataToCSV = (project: ProjectData): string => {
  console.log('开始导出项目小时数据为CSV:', project.name);

  if (!project.analysisResults?.hourlyData || project.analysisResults.hourlyData.length === 0) {
    console.error('项目没有小时数据，无法导出');
    throw new Error('项目没有小时数据，无法导出');
  }

  // 获取项目基本信息
  const basicInfo = `项目的基本信息, ${project.basicInfo.name}, ${project.basicInfo.region} ${project.basicInfo.prefecture}, ${project.basicInfo.installationType}-${project.basicInfo.projectType}\n\n`;

  // 获取所有光伏组件ID
  const pvModuleIds = project.pvModules.map(module => module.id);

  // 创建CSV标题行
  let headers = ['m', 'dm', 'h'];
  
  // 添加每个光伏组件的发电量列
  pvModuleIds.forEach((id, index) => {
    headers.push(`pvGeneration${index + 1}`);
  });
  
  // 添加其他列
  headers = headers.concat([
    'storageCharge',
    'storageCapacity',
    'electricityConsumption',
    'gridExport',
    'gridImport',
    'price',
    'gridFeedInPrice'
  ]);

  // 创建CSV内容
  let csvContent = headers.join(',') + '\n';

  // 添加数据行
  project.analysisResults.hourlyData.forEach(hour => {
    const row = [
      hour.month,
      hour.day,
      hour.hour
    ];

    // 添加每个光伏组件的发电量
    pvModuleIds.forEach(id => {
      row.push(hour.pvGeneration[id] || 0);
    });

    // 添加其他数据
    row.push(
      hour.storageCharge || 0,
      hour.storageCapacity || 0,
      hour.electricityConsumption || 0,
      hour.gridExport || 0,
      hour.gridImport || 0,
      0, // 电价（这里需要根据实际情况获取）
      0  // 上网电价（这里需要根据实际情况获取）
    );

    csvContent += row.join(',') + '\n';
  });

  return basicInfo + csvContent;
};

/**
 * 下载CSV文件
 * @param csvContent CSV内容
 * @param fileName 文件名
 */
export const downloadCSV = (csvContent: string, fileName: string): void => {
  // 创建Blob对象
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // 创建下载链接
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  
  // 添加到文档并触发点击
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * 导出项目小时数据为CSV文件
 * @param project 项目数据
 */
export const exportProjectHourlyData = (project: ProjectData): void => {
  try {
    // 生成CSV内容
    const csvContent = exportProjectHourlyDataToCSV(project);
    
    // 生成文件名
    const fileName = `${project.name || project.id}_hourly_data_${new Date().toISOString().split('T')[0]}.csv`;
    
    // 下载文件
    downloadCSV(csvContent, fileName);
    
    console.log('项目小时数据导出成功');
  } catch (error) {
    console.error('导出项目小时数据失败:', error);
    throw error;
  }
};
