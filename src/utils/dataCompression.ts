/**
 * 数据压缩工具
 * 用于压缩和解压缩各种数据，减少存储空间占用
 */
import { IrradianceData, IrradianceHourlyData } from '../types/database';
import { ProjectData, ProjectHourlyData } from '../types/projectData';
import * as LZString from 'lz-string';

// 压缩配置
const COMPRESSION_CONFIG = {
  // 批次大小 - 用于分批处理大量数据
  BATCH_SIZE: 1000,         // 每批处理的数据点数量

  // 压缩方法
  COMPRESSION_METHOD: {
    LZ_STRING: 'lz-string', // 使用LZ-string压缩
  }
};

/**
 * 通用数据压缩函数
 * 使用LZ-string压缩任意数据
 * @param data 要压缩的数据
 * @param dataName 数据名称（用于日志）
 * @returns 压缩后的字符串
 */
export const compressData = <T>(data: T, dataName: string = '数据'): string => {
  console.log(`开始压缩${dataName}`);

  try {
    // 将数据转换为JSON字符串
    const jsonString = JSON.stringify(data);
    console.log(`${dataName} JSON字符串长度: ${jsonString.length} 字节`);

    // 使用LZ-string压缩数据
    const compressedString = LZString.compressToUTF16(jsonString);
    console.log(`${dataName} 压缩后长度: ${compressedString.length} 字节`);
    console.log(`${dataName} 压缩率: ${(compressedString.length / jsonString.length * 100).toFixed(2)}%`);

    return compressedString;
  } catch (error) {
    console.error(`压缩${dataName}失败:`, error);
    throw new Error(`压缩${dataName}失败: ${error.message}`);
  }
};

/**
 * 通用数据解压缩函数
 * 使用LZ-string解压缩数据
 * @param compressedString 压缩后的字符串
 * @param dataName 数据名称（用于日志）
 * @returns 解压缩后的数据
 */
export const decompressData = <T>(compressedString: string, dataName: string = '数据'): T => {
  console.log(`开始解压缩${dataName}, 压缩数据长度: ${compressedString.length} 字节`);

  try {
    // 使用LZ-string解压缩数据
    const decompressedString = LZString.decompressFromUTF16(compressedString);

    if (!decompressedString) {
      console.error(`解压缩${dataName}失败: 解压结果为空`);
      throw new Error(`解压缩${dataName}失败: 解压结果为空`);
    }

    console.log(`${dataName} 解压后长度: ${decompressedString.length} 字节`);

    // 解析JSON
    const data = JSON.parse(decompressedString) as T;
    console.log(`${dataName} 解压缩完成`);

    return data;
  } catch (error) {
    console.error(`解压缩${dataName}失败:`, error);
    throw new Error(`解压缩${dataName}失败: ${error.message}`);
  }
};

/**
 * 压缩光照数据
 * 使用通用压缩方法处理光照数据
 * @param data 光照数据
 * @returns 压缩后的数据对象，包含元数据和压缩后的小时数据
 */
export const compressIrradianceData = (data: IrradianceData): {
  metadata: Omit<IrradianceData, 'data'>,
  compressedData: string
} => {
  console.log('开始压缩光照数据:', data.name);

  // 检查数据有效性
  if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
    console.error('无效的光照数据，无法压缩');
    throw new Error('无效的光照数据，无法压缩');
  }

  // 创建元数据对象（不包含小时数据）
  const metadata = { ...data };
  delete (metadata as any).data;

  // 压缩小时数据
  const compressedData = compressData(data.data, '光照小时数据');

  return {
    metadata,
    compressedData
  };
};

/**
 * 解压缩光照数据
 * 使用通用解压缩方法处理光照数据
 * @param metadata 光照数据元数据
 * @param compressedData 压缩后的小时数据
 * @returns 完整的光照数据
 */
export const decompressIrradianceData = (
  metadata: Omit<IrradianceData, 'data'>,
  compressedData: string
): IrradianceData => {
  console.log('开始解压缩光照数据:', metadata.name);

  try {
    // 解压缩小时数据
    const hourlyData = decompressData<IrradianceHourlyData[]>(compressedData, '光照小时数据');
    console.log(`成功解析光照小时数据，数据点数: ${hourlyData.length}`);

    // 合并元数据和解压缩的数据
    const result: IrradianceData = {
      ...metadata,
      data: hourlyData
    };

    return result;
  } catch (error) {
    console.error('解压缩光照数据失败:', error);

    // 返回带有空数据的元数据
    return {
      ...metadata,
      data: []
    };
  }
};

// 注意：旧的二进制转Base64和Base64转二进制函数已被移除，
// 因为我们现在使用LZString直接压缩为字符串，不再需要二进制中间步骤

/**
 * 压缩项目数据
 * 使用通用压缩方法处理项目数据
 * @param data 项目数据
 * @returns 压缩后的数据对象，包含元数据和压缩后的小时数据
 */
export const compressProjectData = (data: ProjectData): {
  metadata: Omit<ProjectData, 'analysisResults'> & { analysisResults: Omit<any, 'hourlyData'> },
  compressedHourlyData: string | null
} => {
  console.log('开始压缩项目数据:', data.name);

  // 创建元数据对象（不包含小时数据）
  const metadata = { ...data };
  let compressedHourlyData: string | null = null;

  // 检查是否有小时数据
  if (data.analysisResults && data.analysisResults.hourlyData && data.analysisResults.hourlyData.length > 0) {
    try {
      console.log(`项目小时数据点数: ${data.analysisResults.hourlyData.length}`);

      // 使用通用压缩方法压缩小时数据
      compressedHourlyData = compressData(data.analysisResults.hourlyData, '项目小时数据');

      // 从元数据中移除小时数据
      if (metadata.analysisResults) {
        metadata.analysisResults = {
          ...metadata.analysisResults,
          hourlyData: [] // 清空小时数据
        };
      }
    } catch (error) {
      console.error('压缩项目小时数据失败:', error);
      compressedHourlyData = null;
    }
  } else {
    console.log('项目没有小时数据，无需压缩');
  }

  return {
    metadata,
    compressedHourlyData
  };
};

/**
 * 解压缩项目数据
 * @param metadata 项目元数据
 * @param compressedHourlyData 压缩后的小时数据
 * @returns 完整的项目数据
 */
export const decompressProjectData = (
  metadata: Omit<ProjectData, 'analysisResults'> & { analysisResults: Omit<any, 'hourlyData'> },
  compressedHourlyData: string | null
): ProjectData => {
  console.log('开始解压缩项目数据:', metadata.name);

  // 创建结果对象
  const result: ProjectData = { ...metadata };

  // 如果有压缩的小时数据，尝试解压
  if (compressedHourlyData) {
    try {
      // 使用通用解压缩方法解压小时数据
      const hourlyData = decompressData<ProjectHourlyData[]>(compressedHourlyData, '项目小时数据');
      console.log(`成功解析项目小时数据，数据点数: ${hourlyData.length}`);

      // 将小时数据添加到结果中
      if (result.analysisResults) {
        result.analysisResults = {
          ...result.analysisResults,
          hourlyData
        };
      }
    } catch (error) {
      console.error('解压缩项目小时数据失败:', error);
      if (result.analysisResults) {
        result.analysisResults.hourlyData = [];
      }
    }
  } else {
    console.log('没有压缩的小时数据，返回原始元数据');
    if (result.analysisResults) {
      result.analysisResults.hourlyData = [];
    }
  }

  return result;
};

/**
 * 批量压缩项目小时数据
 * 将小时数据分批压缩，每批最多1000条记录
 * @param hourlyData 项目小时数据
 * @returns 批次信息和压缩后的批次数据
 */
export const compressProjectHourlyDataInBatches = (hourlyData: ProjectHourlyData[]): {
  batchInfo: { totalRecords: number; batchCount: number; batchSize: number };
  compressedBatches: string[];
} => {
  console.log(`开始批量压缩项目小时数据，数据点数: ${hourlyData.length}`);

  // 批次大小
  const BATCH_SIZE = COMPRESSION_CONFIG.BATCH_SIZE;

  // 将小时数据分批
  const batches: ProjectHourlyData[][] = [];
  for (let i = 0; i < hourlyData.length; i += BATCH_SIZE) {
    batches.push(hourlyData.slice(i, i + BATCH_SIZE));
  }

  console.log(`将${hourlyData.length}条记录分成${batches.length}批处理`);

  // 压缩每个批次
  const compressedBatches: string[] = [];

  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    // 使用通用压缩方法压缩批次数据
    const compressedData = compressData(batch, `项目小时数据批次${i+1}/${batches.length}`);
    compressedBatches.push(compressedData);
  }

  // 创建批次信息
  const batchInfo = {
    totalRecords: hourlyData.length,
    batchCount: batches.length,
    batchSize: BATCH_SIZE
  };

  return {
    batchInfo,
    compressedBatches
  };
};

/**
 * 解压缩批量项目小时数据
 * @param batchInfo 批次信息
 * @param compressedBatches 压缩后的批次数据
 * @returns 完整的项目小时数据
 */
export const decompressProjectHourlyDataFromBatches = (
  batchInfo: { totalRecords: number; batchCount: number; batchSize: number },
  compressedBatches: string[]
): ProjectHourlyData[] => {
  console.log(`开始解压缩批量项目小时数据，批次数: ${batchInfo.batchCount}`);

  // 检查批次数量是否匹配
  if (compressedBatches.length !== batchInfo.batchCount) {
    console.error(`批次数量不匹配，期望${batchInfo.batchCount}个批次，实际${compressedBatches.length}个批次`);
    throw new Error(`批次数量不匹配，期望${batchInfo.batchCount}个批次，实际${compressedBatches.length}个批次`);
  }

  // 解压每个批次
  const allHourlyData: ProjectHourlyData[] = [];

  for (let i = 0; i < compressedBatches.length; i++) {
    const compressedBatch = compressedBatches[i];

    try {
      // 使用通用解压缩方法解压批次数据
      const batchData = decompressData<ProjectHourlyData[]>(compressedBatch, `项目小时数据批次${i+1}/${batchInfo.batchCount}`);
      console.log(`批次${i+1}/${batchInfo.batchCount} 成功解析，数据点数: ${batchData.length}`);

      // 添加到结果中
      allHourlyData.push(...batchData);
    } catch (error) {
      console.error(`批次${i+1}/${batchInfo.batchCount} 解压失败:`, error);
      throw new Error(`批次${i+1}/${batchInfo.batchCount} 解压失败: ${error.message}`);
    }
  }

  console.log(`所有批次解压完成，总数据点数: ${allHourlyData.length}`);

  return allHourlyData;
};