import dayjs from 'dayjs';
import { CurrencyType } from '../types';

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD'): string => {
  return dayjs(date).format(format);
};

/**
 * 格式化时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的时间字符串
 */
export const formatTime = (date: string | Date, format: string = 'HH:mm:ss'): string => {
  return dayjs(date).format(format);
};

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: string | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  return dayjs(date).format(format);
};

/**
 * 格式化货币
 * @param value 金额
 * @param currency 货币类型
 * @returns 格式化后的货币字符串
 */
export const formatCurrency = (value: number, currency: CurrencyType = 'JPY'): string => {
  const formatter = new Intl.NumberFormat(getCurrencyLocale(currency), {
    style: 'currency',
    currency,
    minimumFractionDigits: getCurrencyDecimals(currency),
    maximumFractionDigits: getCurrencyDecimals(currency),
  });
  return formatter.format(value);
};

/**
 * 获取货币对应的区域设置
 * @param currency 货币类型
 * @returns 区域设置
 */
const getCurrencyLocale = (currency: CurrencyType): string => {
  switch (currency) {
    case 'JPY':
      return 'ja-JP';
    case 'CNY':
      return 'zh-CN';
    case 'USD':
      return 'en-US';
    default:
      return 'ja-JP';
  }
};

/**
 * 获取货币小数位数
 * @param currency 货币类型
 * @returns 小数位数
 */
const getCurrencyDecimals = (currency: CurrencyType): number => {
  switch (currency) {
    case 'JPY':
      return 0;
    case 'CNY':
    case 'USD':
      return 2;
    default:
      return 0;
  }
};

/**
 * 格式化电力单位
 * @param value 电力值
 * @param unit 单位
 * @returns 格式化后的电力字符串
 */
export const formatPower = (value: number, unit: 'W' | 'kW' | 'MW' = 'kW'): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return `${formatter.format(value)} ${unit}`;
};

/**
 * 格式化能量单位
 * @param value 能量值
 * @param unit 单位
 * @returns 格式化后的能量字符串
 */
export const formatEnergy = (value: number, unit: 'Wh' | 'kWh' | 'MWh' = 'kWh'): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return `${formatter.format(value)} ${unit}`;
};

/**
 * 格式化百分比
 * @param value 百分比值
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export const formatPercentage = (value: number, decimals: number = 2): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
  return formatter.format(value / 100);
};

/**
 * 格式化数字
 * @param value 数字值
 * @param decimals 小数位数
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (value: number, decimals: number = 2): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
  return formatter.format(value);
};
