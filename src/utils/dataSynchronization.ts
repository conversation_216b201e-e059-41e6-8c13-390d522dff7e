/**
 * 数据同步工具
 * 用于管理前端和服务器之间的数据同步
 */
import { IrradianceData } from '../types/database';
import { ProjectData } from '../types/projectData';
import { message } from 'antd';
import {
  compressIrradianceData,
  decompressIrradianceData,
  compressProjectData,
  decompressProjectData
} from './dataCompression';

// 缓存配置
const CACHE_CONFIG = {
  MAX_CACHE_PERCENTAGE: 80, // 最大缓存百分比
  CACHE_KEY_PREFIX: 'pv_irradiance_', // 缓存键前缀
  CHUNK_KEY_PREFIX: 'pv_irradiance_chunk_', // 分块数据键前缀
  COMPRESSED_KEY_PREFIX: 'pv_irradiance_compressed_', // 压缩数据键前缀
  PROJECT_KEY_PREFIX: 'pv_project_', // 项目数据键前缀
  PROJECT_HOURLY_KEY_PREFIX: 'pv_project_hourly_', // 项目小时数据键前缀
  MIN_ITEMS_TO_KEEP: 10, // 最少保留的数据项数量
  MAX_CHUNK_SIZE: 1024 * 1024, // 每个块的最大大小（1MB）
  MAX_METADATA_SIZE: 100 * 1024, // 元数据最大大小（100KB）
  USE_COMPRESSION: true, // 是否使用压缩存储 - 确保启用压缩功能
};

// 缓存管理器
class CacheManager {
  private static instance: CacheManager;
  private cachedItems: Map<string, { timestamp: number; id: string }> = new Map();
  private maxCacheSize: number = 0;

  private constructor() {
    // 初始化缓存管理器
    this.updateMaxCacheSize();
    window.addEventListener('storage', this.handleStorageChange);
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * 更新最大缓存大小
   */
  private updateMaxCacheSize(): void {
    try {
      // 设置缓存空间为32MB
      const estimatedAvailableSpace = 32 * 1024 * 1024; // 32MB in bytes
      this.maxCacheSize = Math.floor(estimatedAvailableSpace * (CACHE_CONFIG.MAX_CACHE_PERCENTAGE / 100));
      console.log(`缓存管理器: 设置最大缓存大小为 ${(this.maxCacheSize / 1024 / 1024).toFixed(1)} MB`);
    } catch (error) {
      console.error('Failed to update max cache size:', error);
      this.maxCacheSize = 16 * 1024 * 1024; // 默认16MB
    }
  }

  /**
   * 处理存储变化事件
   */
  private handleStorageChange = (event: StorageEvent): void => {
    if (event.key && event.key.startsWith(CACHE_CONFIG.CACHE_KEY_PREFIX)) {
      const id = event.key.replace(CACHE_CONFIG.CACHE_KEY_PREFIX, '');

      if (event.newValue) {
        // 更新缓存项
        this.cachedItems.set(id, { timestamp: Date.now(), id });
      } else {
        // 移除缓存项
        this.cachedItems.delete(id);
      }
    }
  };

  /**
   * 添加项目到缓存
   * @param id 项目ID
   * @param data 项目数据
   */
  public addItem(id: string, data: IrradianceData | ProjectData): void {
    try {
      console.log('缓存管理器: 添加项目到缓存, ID:', id, '同步状态:', data.syncStatus);

      // 确保同步状态使用连字符而不是下划线
      if ((data.syncStatus as string) === 'server_only') {
        console.log('缓存管理器: 修正同步状态 server_only -> server-only');
        data.syncStatus = 'server-only';
      } else if ((data.syncStatus as string) === 'local_only') {
        console.log('缓存管理器: 修正同步状态 local_only -> local-only');
        data.syncStatus = 'local-only';
      }

      // 检查数据类型
      const isProjectData = 'dataType' in data && (data.dataType === '项目数据' || data.dataType === 'project');

      // 检查是否是当前正在查看的数据
      const isCurrentlyViewed = this.isCurrentlyViewed(id);
      console.log(`缓存管理器: 数据ID ${id} ${isCurrentlyViewed ? '是' : '不是'}当前正在查看的数据`);

      // 检查数据大小
      const dataString = JSON.stringify(data);
      const dataSize = dataString.length;
      console.log('缓存管理器: 原始数据大小:', dataSize, 'bytes');

      // 获取当前缓存使用情况
      const { percentage, totalSize, itemCount } = this.getCacheUsage();
      console.log(`缓存管理器: 当前缓存使用率: ${percentage.toFixed(2)}%, 项目数: ${itemCount}`);

      // 检查缓存是否已满
      if (this.isCacheFull()) {
        console.log('缓存管理器: 缓存已满，需要清理空间');

        // 估算需要释放的空间
        const estimatedNewItemSize = dataSize * 2; // UTF-16编码，每个字符占2字节
        const availableSpace = this.maxCacheSize - totalSize;

        if (estimatedNewItemSize > availableSpace) {
          console.log(`缓存管理器: 需要释放空间，估计新项目大小: ${estimatedNewItemSize} bytes, 可用空间: ${availableSpace} bytes`);

          // 计算需要删除的项目数量
          // 先获取所有项目的平均大小
          const avgItemSize = totalSize / itemCount;
          // 计算需要删除的项目数量，向上取整以确保释放足够空间
          const itemsToRemove = Math.ceil((estimatedNewItemSize - availableSpace) / avgItemSize) + 1; // 多删除一项以确保有足够空间

          console.log(`缓存管理器: 平均项目大小: ${avgItemSize} bytes, 计划删除 ${itemsToRemove} 个项目`);

          // 删除最旧的几个项目，但避免删除当前正在查看的数据
          this.removeOldestItem(itemsToRemove, id);
        } else {
          // 虽然缓存已满，但还有足够空间存储新项目，只删除一个最旧的项目
          console.log('缓存管理器: 缓存已满但有足够空间，只删除一个最旧的项目');
          this.removeOldestItem(1, id);
        }
      }

      // 首先清除该ID的所有现有数据（包括主数据、分块数据和压缩数据）
      this.removeItem(id);

      // 准备存储数据
      let key = '';

      // 根据数据类型选择不同的处理方式
      if (isProjectData) {
        // 项目数据处理
        key = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}${id}`;
        console.log('缓存管理器: 处理项目数据');

        try {
          // 使用项目数据压缩工具
          const projectData = data as ProjectData;

          // 检查是否是服务器数据且已同步
          if (data.syncStatus === 'synced') {
            console.log('缓存管理器: 处理已同步的服务器项目数据');

            try {
              // 压缩项目数据
              const { metadata, compressedHourlyData } = compressProjectData(projectData);

              // 保存元数据
              localStorage.setItem(key, JSON.stringify({
                ...metadata,
                serverData: true, // 标记为服务器数据
                dataType: '项目数据' // 统一使用中文类型标记
              }));
              console.log('缓存管理器: 已保存服务器项目数据的元数据到localStorage');

              // 如果有压缩的小时数据，保存到单独的键
              if (compressedHourlyData) {
                const hourlyDataKey = `${CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX}${id}`;
                localStorage.setItem(hourlyDataKey, compressedHourlyData);
                console.log('缓存管理器: 已保存压缩的项目小时数据到localStorage');
              }
            } catch (compressionError) {
              console.error('缓存管理器: 压缩项目数据失败:', compressionError);

              // 如果压缩失败，尝试保存不含小时数据的元数据
              try {
                const metaProject = { ...projectData };
                if (metaProject.analysisResults) {
                  metaProject.analysisResults.hourlyData = [];
                }
                localStorage.setItem(key, JSON.stringify({
                  ...metaProject,
                  serverData: true,
                  dataType: '项目数据'
                }));
                console.log('缓存管理器: 已保存不含小时数据的项目元数据到localStorage');
              } catch (metaError) {
                console.error('缓存管理器: 保存项目元数据失败:', metaError);
                message.warning('无法保存项目数据到本地');
              }
            }
          } else {
            // 本地项目数据
            console.log('缓存管理器: 处理本地项目数据');

            try {
              // 压缩项目数据
              const { metadata, compressedHourlyData } = compressProjectData(projectData);

              // 保存元数据
              localStorage.setItem(key, JSON.stringify({
                ...metadata,
                dataType: '项目数据' // 统一使用中文类型标记
              }));
              console.log('缓存管理器: 已保存本地项目数据的元数据到localStorage');

              // 如果有压缩的小时数据，保存到单独的键
              if (compressedHourlyData) {
                const hourlyDataKey = `${CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX}${id}`;
                localStorage.setItem(hourlyDataKey, compressedHourlyData);
                console.log('缓存管理器: 已保存压缩的项目小时数据到localStorage');
              }
            } catch (compressionError) {
              console.error('缓存管理器: 压缩项目数据失败:', compressionError);

              // 如果压缩失败，尝试保存不含小时数据的元数据
              try {
                const metaProject = { ...projectData };
                if (metaProject.analysisResults) {
                  metaProject.analysisResults.hourlyData = [];
                }
                localStorage.setItem(key, JSON.stringify({
                  ...metaProject,
                  dataType: '项目数据'
                }));
                console.log('缓存管理器: 已保存不含小时数据的项目元数据到localStorage');
              } catch (metaError) {
                console.error('缓存管理器: 保存项目元数据失败:', metaError);
                message.warning('无法保存项目数据到本地');
              }
            }
          }
        } catch (error) {
          console.error('缓存管理器: 处理项目数据失败:', error);
          message.warning('处理项目数据失败');
        }
      } else {
        // 光照数据处理
        key = `${CACHE_CONFIG.CACHE_KEY_PREFIX}${id}`;
        console.log('缓存管理器: 处理光照数据');

        // 检查是否是服务器数据且已同步
        if (data.syncStatus === 'synced') {
          console.log('缓存管理器: 处理已同步的服务器数据');

          try {
            // 使用压缩功能处理数据
            if ('data' in data && data.data && Array.isArray(data.data) && data.data.length > 0) {
              console.log('缓存管理器: 使用压缩存储服务器数据');

              // 创建元数据（不包含详细数据）
              const metaData = {
                ...data,
                data: [], // 不在元数据中保存详细数据
                syncStatus: 'synced', // 确保同步状态正确
                serverData: true, // 标记为服务器数据
                compressed: true, // 标记为压缩数据
              };

              // 压缩数据
              const { compressedData } = compressIrradianceData(data as IrradianceData);
              const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;

              console.log(`缓存管理器: 服务器数据压缩后大小: ${compressedData.length} 字节`);

              // 保存元数据
              localStorage.setItem(key, JSON.stringify(metaData));
              console.log('缓存管理器: 已保存服务器数据的元数据到localStorage，同步状态为: synced');

              // 保存压缩数据
              localStorage.setItem(compressedKey, compressedData);
              console.log('缓存管理器: 已保存压缩的服务器数据到localStorage');
            } else {
              // 如果没有详细数据，只保存元数据
              const metaData = {
                ...data,
                data: [], // 不在元数据中保存详细数据
                syncStatus: 'synced', // 确保同步状态正确
                serverData: true, // 标记为服务器数据
              };

              localStorage.setItem(key, JSON.stringify(metaData));
              console.log('缓存管理器: 已保存服务器数据的元数据到localStorage (无详细数据)');
            }
          } catch (error) {
            console.error('缓存管理器: 保存服务器数据失败:', error);
            message.warning('无法保存服务器数据到本地');
          }
        }
        // 如果不是服务器数据，使用压缩存储
        else if (CACHE_CONFIG.USE_COMPRESSION && 'data' in data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          console.log('缓存管理器: 使用压缩存储');

          try {
            // 创建元数据（不包含详细数据）
            const metaData = {
              ...data,
              data: [], // 不在元数据中保存详细数据
              compressed: true, // 标记为压缩数据
            };

            // 压缩数据
            const { compressedData } = compressIrradianceData(data as IrradianceData);
            const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;

            console.log(`缓存管理器: 压缩后数据大小: ${compressedData.length} 字节`);

            // 存储压缩数据
            localStorage.setItem(compressedKey, compressedData);
            console.log('缓存管理器: 压缩数据已成功保存到localStorage');

            // 保存元数据
            localStorage.setItem(key, JSON.stringify(metaData));
            console.log('缓存管理器: 元数据已成功保存到localStorage');
          } catch (compressionError) {
            console.error('缓存管理器: 压缩存储失败:', compressionError);

            // 如果压缩失败，尝试使用分块存储
            this.storeWithChunks(id, data as IrradianceData, dataSize, key);
          }
        }
        // 如果不使用压缩或压缩失败，检查是否需要分块存储
        else if (dataSize > CACHE_CONFIG.MAX_METADATA_SIZE) {
          this.storeWithChunks(id, data as IrradianceData, dataSize, key);
        } else {
          // 数据较小，直接存储
          try {
            localStorage.setItem(key, dataString);
            console.log('缓存管理器: 数据已成功保存到localStorage (无需压缩或分块)');
          } catch (storageError) {
            console.error('缓存管理器: 保存到localStorage失败:', storageError);

            // 尝试只保存元数据
            try {
              const metaData = {
                ...data,
                data: [], // 不保存详细数据
                syncStatus: data.syncStatus,
              };
              localStorage.setItem(key, JSON.stringify(metaData));
              console.log('缓存管理器: 已保存元数据到localStorage (不含详细数据)，同步状态为:', data.syncStatus);

              // 如果是已同步的数据，通知用户
              if ((data.syncStatus as string) === 'synced') {
                message.info('本地存储空间不足，完整数据将从服务器获取');
              }
            } catch (metaDataError) {
              console.error('缓存管理器: 保存元数据也失败:', metaDataError);
              message.warning('本地存储空间不足，部分数据可能无法保存');
            }
          }
        }
      }

      // 更新缓存项记录
      this.cachedItems.set(id, { timestamp: Date.now(), id });
      console.log('缓存管理器: 缓存项记录已更新');
    } catch (error) {
      console.error('缓存管理器: 添加项目到缓存失败:', error);
      throw new Error('缓存数据失败');
    }
  }

  /**
   * 使用分块存储保存数据
   * @param id 数据ID
   * @param data 数据对象
   * @param dataSize 数据大小
   * @param key 存储键
   */
  private storeWithChunks(id: string, data: IrradianceData, dataSize: number, key: string): void {
    console.log(`缓存管理器: 数据大小(${dataSize}字节)超过元数据最大大小(${CACHE_CONFIG.MAX_METADATA_SIZE}字节)，将使用分块存储`);

    try {
      // 创建元数据（不包含详细数据）
      const metaData = {
        ...data,
        data: [], // 不在元数据中保存详细数据
        chunked: true, // 标记为分块数据
        chunkCount: 0, // 初始化块数量
      };

      // 如果有详细数据，进行分块存储
      if (data.data && Array.isArray(data.data) && data.data.length > 0) {
        // 将详细数据序列化为字符串
        const detailDataString = JSON.stringify(data.data);
        const detailDataSize = detailDataString.length;

        // 计算需要的块数
        const chunkCount = Math.ceil(detailDataSize / CACHE_CONFIG.MAX_CHUNK_SIZE);
        metaData.chunkCount = chunkCount;

        console.log(`缓存管理器: 详细数据大小: ${detailDataSize}字节，将分为${chunkCount}个块存储`);

        // 分块存储详细数据
        for (let i = 0; i < chunkCount; i++) {
          const start = i * CACHE_CONFIG.MAX_CHUNK_SIZE;
          const end = Math.min((i + 1) * CACHE_CONFIG.MAX_CHUNK_SIZE, detailDataSize);
          const chunk = detailDataString.substring(start, end);
          const chunkKey = `${CACHE_CONFIG.CHUNK_KEY_PREFIX}${id}_${i}`;

          try {
            localStorage.setItem(chunkKey, chunk);
            console.log(`缓存管理器: 已保存数据块 ${i+1}/${chunkCount}, 大小: ${chunk.length}字节`);
          } catch (chunkError) {
            console.error(`缓存管理器: 保存数据块 ${i+1}/${chunkCount} 失败:`, chunkError);
            // 如果保存某个块失败，清除已保存的所有块
            for (let j = 0; j <= i; j++) {
              const removeKey = `${CACHE_CONFIG.CHUNK_KEY_PREFIX}${id}_${j}`;
              localStorage.removeItem(removeKey);
            }
            throw new Error(`保存数据块 ${i+1}/${chunkCount} 失败`);
          }
        }
      }

      // 保存元数据
      localStorage.setItem(key, JSON.stringify(metaData));
      console.log('缓存管理器: 元数据已成功保存到localStorage，数据已分块存储');
    } catch (storageError) {
      console.error('缓存管理器: 分块存储数据失败:', storageError);

      // 尝试只保存基本元数据（不包含详细数据）
      try {
        const basicMetaData = {
          ...data,
          data: [], // 不保存详细数据
          chunked: false,
          syncStatus: data.syncStatus,
        };
        localStorage.setItem(key, JSON.stringify(basicMetaData));
        console.log('缓存管理器: 已保存基本元数据到localStorage (不含详细数据)，同步状态为:', data.syncStatus);

        // 如果是已同步的数据，通知用户
        if (data.syncStatus === 'synced') {
          message.info('本地存储空间不足，完整数据将从服务器获取');
        }
      } catch (metaDataError) {
        console.error('缓存管理器: 保存基本元数据也失败:', metaDataError);
        message.warning('本地存储空间不足，部分数据可能无法保存');
      }
    }
  }

  /**
   * 检查是否是当前正在查看的数据
   * @param id 数据ID
   * @returns 是否是当前正在查看的数据
   */
  private isCurrentlyViewed(id: string): boolean {
    try {
      // 从localStorage获取当前查看的数据ID
      const currentViewedId = localStorage.getItem('pv_current_viewed_irradiance_id');
      return currentViewedId === id;
    } catch (error) {
      console.error('缓存管理器: 检查当前查看数据失败:', error);
      return false;
    }
  }

  /**
   * 从缓存获取项目
   * @param id 项目ID
   * @param isProjectData 是否是项目数据
   * @returns 项目数据或null
   */
  public getItem(id: string, isProjectData?: boolean): IrradianceData | ProjectData | null {
    try {
      console.log('缓存管理器: 从缓存获取项目, ID:', id, isProjectData ? '(项目数据)' : '');

      // 检查是否是项目数据
      if (isProjectData === undefined) {
        // 如果未指定，尝试检查项目数据是否存在
        const projectKey = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}${id}`;
        const projectData = localStorage.getItem(projectKey);
        isProjectData = !!projectData;
      }

      // 根据数据类型选择不同的处理方式
      if (isProjectData) {
        // 项目数据处理
        const key = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}${id}`;
        const data = localStorage.getItem(key);

        if (data) {
          console.log('缓存管理器: 从缓存中找到项目数据');

          try {
            // 解析数据
            const parsedData = JSON.parse(data);

            // 检查是否有小时数据
            const hourlyDataKey = `${CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX}${id}`;
            const compressedHourlyData = localStorage.getItem(hourlyDataKey);

            if (compressedHourlyData) {
              console.log('缓存管理器: 找到压缩的项目小时数据');

              // 解压缩项目数据
              const result = decompressProjectData(parsedData, compressedHourlyData);
              console.log('缓存管理器: 已成功解压缩项目小时数据');

              // 更新缓存项记录的时间戳
              this.updateCacheItemTimestamp(id);

              return result;
            } else {
              console.log('缓存管理器: 未找到压缩的项目小时数据，返回元数据');

              // 更新缓存项记录的时间戳
              this.updateCacheItemTimestamp(id);

              return parsedData;
            }
          } catch (error) {
            console.error('缓存管理器: 解析项目数据失败:', error);
            return null;
          }
        } else {
          console.log('缓存管理器: 缓存中未找到项目数据');
          return null;
        }
      } else {
        // 光照数据处理
        const key = `${CACHE_CONFIG.CACHE_KEY_PREFIX}${id}`;
        const data = localStorage.getItem(key);

        if (data) {
          console.log('缓存管理器: 从缓存中找到光照数据');

          // 解析数据
          const parsedData = JSON.parse(data);

          // 检查是否是服务器数据
          if (parsedData.serverData === true) {
            console.log('缓存管理器: 检测到服务器数据');

            // 检查是否是压缩数据
            if (parsedData.compressed === true) {
              console.log('缓存管理器: 检测到压缩的服务器数据');

              try {
                // 从压缩存储中读取详细数据
                const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;
                const compressedData = localStorage.getItem(compressedKey);

                if (compressedData) {
                  console.log(`缓存管理器: 已找到压缩的服务器数据, 大小: ${compressedData.length}字节`);

                  // 解压缩数据
                  const decompressedData = decompressIrradianceData(parsedData, compressedData);
                  console.log('缓存管理器: 已成功解压缩服务器数据');

                  // 更新缓存项记录的时间戳
                  this.updateCacheItemTimestamp(id);

                  return decompressedData;
                } else {
                  console.log('缓存管理器: 未找到压缩的服务器数据，将从服务器获取');
                  // 如果没有找到压缩数据，保持同步状态为已同步，但数据为空
                  parsedData.data = [];
                }
              } catch (error) {
                console.error('缓存管理器: 解压缩服务器数据失败:', error);
                // 如果出错，保持同步状态为已同步，但数据为空
                parsedData.data = [];
              }
            } else {
              // 兼容旧版本：尝试从本地获取完整服务器数据（未压缩）
              try {
                const fullDataKey = `${CACHE_CONFIG.CACHE_KEY_PREFIX}full_${id}`;
                const fullDataString = localStorage.getItem(fullDataKey);

                if (fullDataString) {
                  console.log('缓存管理器: 已找到本地存储的完整服务器数据（旧格式）');

                  try {
                    const fullData = JSON.parse(fullDataString);
                    console.log('缓存管理器: 已成功解析完整服务器数据（旧格式）');

                    // 更新缓存项记录的时间戳
                    this.updateCacheItemTimestamp(id);

                    return fullData;
                  } catch (parseError) {
                    console.error('缓存管理器: 解析完整服务器数据失败:', parseError);
                    // 如果解析失败，将同步状态保持为已同步，但数据为空
                    parsedData.data = [];
                  }
                } else {
                  console.log('缓存管理器: 未找到本地存储的完整服务器数据，将从服务器获取');
                  // 如果没有找到完整数据，保持同步状态为已同步，但数据为空
                  parsedData.data = [];
                }
              } catch (error) {
                console.error('缓存管理器: 获取完整服务器数据失败:', error);
                // 如果出错，保持同步状态为已同步，但数据为空
                parsedData.data = [];
              }
            }
          }
          // 检查是否是压缩数据
          else if (parsedData.compressed === true) {
            console.log('缓存管理器: 检测到压缩数据');

            try {
              // 从压缩存储中读取详细数据
              const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;
              const compressedBase64 = localStorage.getItem(compressedKey);

              if (compressedBase64) {
                console.log(`缓存管理器: 已找到压缩数据, 大小: ${compressedBase64.length}字节`);

                // 解压缩数据
                const decompressedData = decompressIrradianceData(parsedData, compressedBase64);
                console.log('缓存管理器: 已成功解压缩数据');

                // 更新缓存项记录的时间戳
                this.updateCacheItemTimestamp(id);

                return decompressedData;
              } else {
                console.error('缓存管理器: 未找到压缩数据');
                // 如果找不到压缩数据，将同步状态设置为仅服务器
                parsedData.data = [];
                parsedData.syncStatus = 'server-only'; // 修正：使用连字符而不是下划线
              }
            } catch (compressionError) {
              console.error('缓存管理器: 解压缩数据失败:', compressionError);
              // 如果解压缩失败，将同步状态设置为仅服务器
              parsedData.data = [];
              parsedData.syncStatus = 'server-only'; // 修正：使用连字符而不是下划线
            }
          }
          // 检查是否是分块数据
          else if (parsedData.chunked === true && parsedData.chunkCount > 0) {
            console.log(`缓存管理器: 检测到分块数据，共${parsedData.chunkCount}块`);

            try {
              // 从分块中读取详细数据
              let detailDataString = '';
              for (let i = 0; i < parsedData.chunkCount; i++) {
                const chunkKey = `${CACHE_CONFIG.CHUNK_KEY_PREFIX}${id}_${i}`;
                const chunk = localStorage.getItem(chunkKey);

                if (chunk) {
                  detailDataString += chunk;
                  console.log(`缓存管理器: 已读取数据块 ${i+1}/${parsedData.chunkCount}, 大小: ${chunk.length}字节`);
                } else {
                  console.error(`缓存管理器: 未找到数据块 ${i+1}/${parsedData.chunkCount}`);
                  throw new Error(`未找到数据块 ${i+1}/${parsedData.chunkCount}`);
                }
              }

              // 解析详细数据并合并到结果中
              if (detailDataString) {
                try {
                  const detailData = JSON.parse(detailDataString);
                  parsedData.data = detailData;
                  console.log('缓存管理器: 已成功合并分块数据');
                } catch (parseError) {
                  console.error('缓存管理器: 解析分块数据失败:', parseError);
                  // 如果解析失败，保持data为空数组
                  parsedData.data = [];
                  parsedData.syncStatus = 'server-only'; // 修正：使用连字符而不是下划线，标记为仅服务器，以便从服务器获取完整数据
                }
              }
            } catch (chunkError) {
              console.error('缓存管理器: 读取分块数据失败:', chunkError);
              // 如果读取分块失败，将同步状态设置为仅服务器
              parsedData.data = [];
              parsedData.syncStatus = 'server-only'; // 修正：使用连字符而不是下划线
            }
          }

          // 更新缓存项记录的时间戳
          this.updateCacheItemTimestamp(id);

          return parsedData;
        } else {
          console.log('缓存管理器: 缓存中未找到光照数据');
          return null;
        }
      }
    } catch (error) {
      console.error('缓存管理器: 从缓存获取项目失败:', error);
      return null;
    }
  }

  /**
   * 更新缓存项的时间戳
   * @param id 项目ID
   */
  private updateCacheItemTimestamp(id: string): void {
    if (this.cachedItems.has(id)) {
      this.cachedItems.set(id, { timestamp: Date.now(), id });
    } else {
      // 如果缓存项记录中没有这个ID，添加它
      this.cachedItems.set(id, { timestamp: Date.now(), id });
    }
  }

  /**
   * 从缓存移除项目
   * @param id 项目ID
   */
  public removeItem(id: string): void {
    try {
      console.log('缓存管理器: 从缓存移除项目, ID:', id);

      // 检查是否是光照数据
      const irradianceKey = `${CACHE_CONFIG.CACHE_KEY_PREFIX}${id}`;
      const irradianceData = localStorage.getItem(irradianceKey);

      if (irradianceData) {
        console.log('缓存管理器: 检测到光照数据');
        // 检查是否是压缩数据键
        if (irradianceKey.includes('compressed')) {
          console.log('缓存管理器: 检测到压缩数据键，直接删除');
          localStorage.removeItem(irradianceKey);
          console.log('缓存管理器: 已删除压缩数据');
          return;
        }

        try {
          const parsedData = JSON.parse(irradianceData);

          // 如果是服务器数据
          if (parsedData.serverData === true) {
            // 检查是否是压缩数据
            if (parsedData.compressed === true) {
              console.log('缓存管理器: 检测到压缩的服务器数据，删除压缩数据');
              const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;
              localStorage.removeItem(compressedKey);
              console.log('缓存管理器: 已删除压缩的服务器数据');
            } else {
              // 兼容旧版本：删除未压缩的完整数据
              console.log('缓存管理器: 检测到未压缩的服务器数据，删除完整数据');
              const fullDataKey = `${CACHE_CONFIG.CACHE_KEY_PREFIX}full_${id}`;
              localStorage.removeItem(fullDataKey);
              console.log('缓存管理器: 已删除完整服务器数据');
            }
          }
          // 如果是压缩数据，删除压缩数据
          else if (parsedData.compressed === true) {
            console.log('缓存管理器: 检测到压缩数据，删除压缩数据');
            const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;
            localStorage.removeItem(compressedKey);
            console.log('缓存管理器: 已删除压缩数据');
          }
          // 如果是分块数据，删除所有块
          else if (parsedData.chunked === true && parsedData.chunkCount > 0) {
            console.log(`缓存管理器: 检测到分块数据，删除${parsedData.chunkCount}个数据块`);

            for (let i = 0; i < parsedData.chunkCount; i++) {
              const chunkKey = `${CACHE_CONFIG.CHUNK_KEY_PREFIX}${id}_${i}`;
              localStorage.removeItem(chunkKey);
              console.log(`缓存管理器: 已删除数据块 ${i+1}/${parsedData.chunkCount}`);
            }
          }

          // 删除光照数据主数据
          localStorage.removeItem(irradianceKey);
          console.log('缓存管理器: 已删除光照数据主数据');
        } catch (parseError) {
          console.error('缓存管理器: 解析光照数据失败，无法确定是否有分块或压缩数据:', parseError);

          // 检查是否是压缩数据相关键
          if (irradianceKey.includes('compressed') || irradianceKey.includes('detail')) {
            console.log('缓存管理器: 检测到压缩数据相关键，跳过删除');
          } else {
            // 即使解析失败，也尝试删除主数据
            localStorage.removeItem(irradianceKey);
            console.log('缓存管理器: 已删除无法解析的主数据');
          }
        }
      }

      // 检查是否是项目数据
      const projectKey = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}${id}`;
      const projectData = localStorage.getItem(projectKey);

      if (projectData) {
        console.log('缓存管理器: 检测到项目数据');

        // 删除项目主数据
        localStorage.removeItem(projectKey);
        console.log('缓存管理器: 已删除项目主数据');

        // 删除项目小时数据 - 新格式
        const hourlyDataKey = `${CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX}${id}`;
        localStorage.removeItem(hourlyDataKey);
        console.log('缓存管理器: 已删除项目小时数据 (新格式)');

        // 删除项目小时数据 - 旧格式
        const oldHourlyDataKey = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}hourly_${id}`;
        localStorage.removeItem(oldHourlyDataKey);
        console.log('缓存管理器: 已删除项目小时数据 (旧格式)');

        // 删除批次信息
        const batchInfoKey = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}hourly_${id}_info`;
        localStorage.removeItem(batchInfoKey);
        console.log('缓存管理器: 已删除项目小时数据批次信息');

        // 尝试删除所有批次数据
        try {
          const batchInfoStr = localStorage.getItem(batchInfoKey);
          if (batchInfoStr) {
            const batchInfo = JSON.parse(batchInfoStr);
            // 删除所有批次数据
            for (let i = 0; i < batchInfo.batchCount; i++) {
              const batchKey = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}hourly_${id}_batch_${i}`;
              localStorage.removeItem(batchKey);
            }
            console.log(`缓存管理器: 已删除${batchInfo.batchCount}个项目小时数据批次`);
          } else {
            // 如果没有批次信息，尝试删除可能存在的批次数据
            let batchesRemoved = 0;
            for (let i = 0; i < 10; i++) { // 假设最多10个批次
              const batchKey = `${CACHE_CONFIG.PROJECT_KEY_PREFIX}hourly_${id}_batch_${i}`;
              const batchData = localStorage.getItem(batchKey);
              if (batchData) {
                localStorage.removeItem(batchKey);
                batchesRemoved++;
              }
            }
            if (batchesRemoved > 0) {
              console.log(`缓存管理器: 已删除${batchesRemoved}个项目小时数据批次（无批次信息）`);
            }
          }
        } catch (error) {
          console.error('缓存管理器: 删除批次数据失败:', error);
        }
      }

      // 从缓存项记录中删除
      this.cachedItems.delete(id);
      console.log('缓存管理器: 已从缓存项记录中删除');

      // 强制刷新缓存统计信息
      this.getCacheUsage();
      console.log('缓存管理器: 已刷新缓存统计信息，数据已完全删除');
    } catch (error) {
      console.error('缓存管理器: 从缓存移除项目失败:', error);
    }
  }

  /**
   * 获取当前缓存使用情况
   * @returns 缓存使用情况 {totalSize: 当前使用字节数, percentage: 使用百分比, itemCount: 缓存项数量}
   */
  private getCacheUsage(): { totalSize: number; percentage: number; itemCount: number } {
    try {
      let totalSize = 0;
      let itemCount = 0;
      let chunkCount = 0;
      let compressedCount = 0;
      let projectCount = 0;
      let projectHourlyCount = 0;

      // 计算主数据大小 - 光照数据
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.CACHE_KEY_PREFIX)) {
          const item = localStorage.getItem(key);
          if (item) {
            totalSize += item.length * 2; // UTF-16 编码，每个字符占2字节
            itemCount++;
          }
        }
      }

      // 计算分块数据大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.CHUNK_KEY_PREFIX)) {
          const chunk = localStorage.getItem(key);
          if (chunk) {
            totalSize += chunk.length * 2; // UTF-16 编码，每个字符占2字节
            chunkCount++;
          }
        }
      }

      // 计算压缩数据大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.COMPRESSED_KEY_PREFIX)) {
          const compressed = localStorage.getItem(key);
          if (compressed) {
            totalSize += compressed.length * 2; // UTF-16 编码，每个字符占2字节
            compressedCount++;
          }
        }
        // 计算完整服务器数据大小
        else if (key.startsWith(`${CACHE_CONFIG.CACHE_KEY_PREFIX}full_`)) {
          const fullData = localStorage.getItem(key);
          if (fullData) {
            totalSize += fullData.length * 2; // UTF-16 编码，每个字符占2字节
          }
        }
      }

      // 计算项目数据大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.PROJECT_KEY_PREFIX) && !key.includes('hourly_')) {
          const item = localStorage.getItem(key);
          if (item) {
            totalSize += item.length * 2; // UTF-16 编码，每个字符占2字节
            projectCount++;
          }
        }
      }

      // 计算项目小时数据大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX) ||
            (key.startsWith(CACHE_CONFIG.PROJECT_KEY_PREFIX) && key.includes('hourly_'))) {
          const item = localStorage.getItem(key);
          if (item) {
            totalSize += item.length * 2; // UTF-16 编码，每个字符占2字节
            projectHourlyCount++;
          }
        }
      }

      const percentage = (totalSize / this.maxCacheSize) * 100;

      // 更新itemCount，包含项目数据
      itemCount += projectCount;

      console.log(`缓存管理器: 当前缓存使用情况 - 大小: ${totalSize} bytes, 百分比: ${percentage.toFixed(2)}%, 总项目数: ${itemCount}, 光照数据: ${itemCount - projectCount}, 项目数据: ${projectCount}, 数据块数: ${chunkCount}, 压缩数据数: ${compressedCount}, 项目小时数据: ${projectHourlyCount}`);

      return {
        totalSize,
        percentage,
        itemCount
      };
    } catch (error) {
      console.error('缓存管理器: 获取缓存使用情况失败:', error);
      return {
        totalSize: 0,
        percentage: 0,
        itemCount: 0
      };
    }
  }

  /**
   * 检查缓存是否已满
   * @returns 是否已满
   */
  private isCacheFull(): boolean {
    try {
      const { percentage, itemCount } = this.getCacheUsage();

      // 如果缓存项数量小于最小保留数量，即使百分比超过阈值也不认为已满
      if (itemCount < CACHE_CONFIG.MIN_ITEMS_TO_KEEP) {
        console.log(`缓存管理器: 缓存项数量(${itemCount})小于最小保留数量(${CACHE_CONFIG.MIN_ITEMS_TO_KEEP})，不认为已满`);
        return false;
      }

      // 检查是否超过最大缓存百分比
      const isFull = percentage >= CACHE_CONFIG.MAX_CACHE_PERCENTAGE;

      if (isFull) {
        console.log(`缓存管理器: 缓存已满 (${percentage.toFixed(2)}% > ${CACHE_CONFIG.MAX_CACHE_PERCENTAGE}%)`);
      }

      return isFull;
    } catch (error) {
      console.error('缓存管理器: 检查缓存是否已满失败:', error);
      return false; // 出错时不认为缓存已满，避免不必要的删除
    }
  }

  /**
   * 获取所有缓存项，按时间戳排序（最旧的在前）
   * @returns 排序后的缓存项数组
   */
  private getSortedCacheItems(): Array<{ id: string; timestamp: number }> {
    const items: Array<{ id: string; timestamp: number }> = [];

    for (const [id, item] of this.cachedItems.entries()) {
      items.push({ id, timestamp: item.timestamp });
    }

    // 按时间戳排序，最旧的在前
    items.sort((a, b) => a.timestamp - b.timestamp);

    return items;
  }

  /**
   * 移除最旧的缓存项
   * @param count 要移除的项目数量，默认为1
   * @param excludeId 要排除的项目ID，通常是当前正在查看的数据
   */
  private removeOldestItem(count: number = 1, excludeId?: string): void {
    if (this.cachedItems.size === 0) return;

    console.log(`缓存管理器: 准备移除${count}个最旧的缓存项${excludeId ? '，排除ID: ' + excludeId : ''}`);

    // 获取排序后的缓存项
    const sortedItems = this.getSortedCacheItems();

    // 如果有排除ID，过滤掉该项目
    const filteredItems = excludeId
      ? sortedItems.filter(item => item.id !== excludeId)
      : sortedItems;

    if (filteredItems.length === 0) {
      console.log('缓存管理器: 没有可删除的项目，所有项目都被排除');
      return;
    }

    // 确保不会删除太多项目
    const itemsToRemove = Math.min(count, filteredItems.length);
    const remainingItems = filteredItems.length - itemsToRemove;

    // 如果删除后剩余的项目数量小于最小保留数量，调整删除数量
    if (remainingItems < CACHE_CONFIG.MIN_ITEMS_TO_KEEP) {
      const adjustedCount = Math.max(0, filteredItems.length - CACHE_CONFIG.MIN_ITEMS_TO_KEEP);
      console.log(`缓存管理器: 调整删除数量从${count}到${adjustedCount}，以保持最少${CACHE_CONFIG.MIN_ITEMS_TO_KEEP}个项目`);

      if (adjustedCount <= 0) {
        console.log('缓存管理器: 无法删除更多项目，已达到最小保留数量');
        return;
      }

      // 移除调整后数量的最旧项目
      for (let i = 0; i < adjustedCount; i++) {
        const { id } = filteredItems[i];
        console.log(`缓存管理器: 移除第${i+1}个最旧的缓存项, ID: ${id}`);
        this.removeItem(id);
      }
    } else {
      // 移除指定数量的最旧项目
      for (let i = 0; i < itemsToRemove; i++) {
        const { id } = filteredItems[i];
        console.log(`缓存管理器: 移除第${i+1}个最旧的缓存项, ID: ${id}`);
        this.removeItem(id);
      }
    }
  }

  /**
   * 清除所有缓存
   */
  public clearCache(): void {
    try {
      let irradianceCount = 0;
      let projectCount = 0;
      let compressedCount = 0;
      let chunkCount = 0;
      let fullDataCount = 0;
      let hourlyDataCount = 0;

      // 获取所有键
      const allKeys = Object.keys(localStorage);
      console.log(`缓存管理器: 开始清除所有缓存，localStorage中共有 ${allKeys.length} 个键`);

      // 清除光照数据缓存
      for (const key of allKeys) {
        if (key.startsWith(CACHE_CONFIG.CACHE_KEY_PREFIX) && !key.includes('full_')) {
          localStorage.removeItem(key);
          irradianceCount++;
        }
      }
      console.log(`缓存管理器: 已清除 ${irradianceCount} 项光照数据主数据`);

      // 清除压缩数据
      for (const key of allKeys) {
        if (key.startsWith(CACHE_CONFIG.COMPRESSED_KEY_PREFIX)) {
          localStorage.removeItem(key);
          compressedCount++;
        }
      }
      console.log(`缓存管理器: 已清除 ${compressedCount} 项压缩数据`);

      // 清除分块数据
      for (const key of allKeys) {
        if (key.startsWith(CACHE_CONFIG.CHUNK_KEY_PREFIX)) {
          localStorage.removeItem(key);
          chunkCount++;
        }
      }
      console.log(`缓存管理器: 已清除 ${chunkCount} 项分块数据`);

      // 清除完整服务器数据
      for (const key of allKeys) {
        if (key.startsWith(`${CACHE_CONFIG.CACHE_KEY_PREFIX}full_`)) {
          localStorage.removeItem(key);
          fullDataCount++;
        }
      }
      console.log(`缓存管理器: 已清除 ${fullDataCount} 项完整服务器数据`);

      // 清除项目数据缓存
      for (const key of allKeys) {
        if (key.startsWith(CACHE_CONFIG.PROJECT_KEY_PREFIX) && !key.includes('hourly_')) {
          localStorage.removeItem(key);
          projectCount++;
        }
      }
      console.log(`缓存管理器: 已清除 ${projectCount} 项项目主数据`);

      // 清除项目小时数据
      for (const key of allKeys) {
        if (key.startsWith(CACHE_CONFIG.PROJECT_KEY_PREFIX) &&
            (key.includes('hourly_') || key.includes('_batch_') || key.includes('_info'))) {
          localStorage.removeItem(key);
          hourlyDataCount++;
        }
      }
      console.log(`缓存管理器: 已清除 ${hourlyDataCount} 项项目小时数据`);

      // 清除缓存项记录
      this.cachedItems.clear();
      console.log(`缓存管理器: 缓存项记录已清空`);

      // 强制刷新缓存统计信息
      this.getCacheUsage();

      console.log(`缓存管理器: 所有缓存已清除，共清除光照数据 ${irradianceCount} 项，压缩数据 ${compressedCount} 项，分块数据 ${chunkCount} 项，完整服务器数据 ${fullDataCount} 项，项目数据 ${projectCount} 项，项目小时数据 ${hourlyDataCount} 项`);
    } catch (error) {
      console.error('缓存管理器: 清除缓存失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计信息
   */
  public getCacheStats(): {
    itemCount: number;
    usedSpace: number;
    maxSpace: number;
    usagePercentage: number;
    items: Array<{id: string; name: string; size: number; timestamp: number; chunked: boolean; compressed: boolean; serverData?: boolean; chunkCount: number; type: string}>;
  } {
    try {
      const { totalSize, percentage, itemCount } = this.getCacheUsage();
      const items: Array<{id: string; name: string; size: number; timestamp: number; chunked: boolean; compressed: boolean; serverData?: boolean; chunkCount: number; type: string}> = [];

      // 创建映射来存储每个ID的分块数据和压缩数据大小
      const chunkSizes: Record<string, number> = {};
      const compressedSizes: Record<string, number> = {};
      const projectHourlySizes: Record<string, number> = {};

      // 首先计算所有分块数据的大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.CHUNK_KEY_PREFIX)) {
          const chunk = localStorage.getItem(key);
          if (chunk) {
            // 从键中提取ID
            const parts = key.replace(CACHE_CONFIG.CHUNK_KEY_PREFIX, '').split('_');
            if (parts.length >= 1) {
              const id = parts[0];
              const chunkSize = chunk.length * 2; // UTF-16编码，每个字符占2字节

              if (!chunkSizes[id]) {
                chunkSizes[id] = 0;
              }
              chunkSizes[id] += chunkSize;
            }
          }
        }
      }

      // 计算所有压缩数据的大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.COMPRESSED_KEY_PREFIX)) {
          const compressed = localStorage.getItem(key);
          if (compressed) {
            // 从键中提取ID
            const id = key.replace(CACHE_CONFIG.COMPRESSED_KEY_PREFIX, '');
            const compressedSize = compressed.length * 2; // UTF-16编码，每个字符占2字节

            compressedSizes[id] = compressedSize;
          }
        }
      }

      // 计算所有项目小时数据的大小
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX) ||
            key.includes('hourly_') ||
            key.includes('_batch_') ||
            key.includes('_info')) {
          const hourlyData = localStorage.getItem(key);
          if (hourlyData) {
            // 从键中提取ID
            let id = '';
            if (key.includes('hourly_')) {
              id = key.split('hourly_')[1].split('_batch_')[0].split('_info')[0];
            } else {
              id = key.replace(CACHE_CONFIG.PROJECT_HOURLY_KEY_PREFIX, '');
            }

            const hourlySize = hourlyData.length * 2; // UTF-16编码，每个字符占2字节

            if (!projectHourlySizes[id]) {
              projectHourlySizes[id] = 0;
            }
            projectHourlySizes[id] += hourlySize;

            console.log(`缓存管理器: 项目 ${id} 的小时数据大小: ${hourlySize} 字节, 累计: ${projectHourlySizes[id]} 字节`);
          }
        }
      }

      // 收集每个缓存项的信息 - 光照数据
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.CACHE_KEY_PREFIX)) {
          const itemStr = localStorage.getItem(key);
          if (itemStr) {
            // 跳过压缩数据键
            if (key.includes('compressed') || key.includes('detail')) {
              console.log(`缓存管理器: 跳过压缩数据键: ${key}`);
              continue;
            }

            try {
              const id = key.replace(CACHE_CONFIG.CACHE_KEY_PREFIX, '');
              const mainSize = itemStr.length * 2; // UTF-16编码，每个字符占2字节
              const timestamp = this.cachedItems.get(id)?.timestamp || Date.now();

              // 尝试解析数据以获取名称和存储信息
              const itemData = JSON.parse(itemStr);
              const name = itemData.name || `数据集 ${id.substring(0, 8)}`;
              const chunked = itemData.chunked === true;
              const compressed = itemData.compressed === true;
              const serverData = itemData.serverData === true;
              const chunkCount = itemData.chunkCount || 0;

              // 计算总大小（主数据 + 分块数据/压缩数据/服务器数据）
              let totalItemSize = mainSize;
              if (chunked) {
                // 分块数据
                totalItemSize += (chunkSizes[id] || 0);
              } else if (compressed || (serverData && compressed)) {
                // 压缩数据（包括压缩的服务器数据）
                const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;
                const compressedData = localStorage.getItem(compressedKey);
                if (compressedData) {
                  totalItemSize += compressedData.length * 2; // UTF-16 编码，每个字符占2字节
                } else {
                  // 如果找不到压缩数据，使用计算的压缩大小
                  totalItemSize += (compressedSizes[id] || 0);
                }
              } else if (serverData && !compressed) {
                // 兼容旧版本：检查是否有完整服务器数据（未压缩）
                const fullDataKey = `${CACHE_CONFIG.CACHE_KEY_PREFIX}full_${id}`;
                const fullData = localStorage.getItem(fullDataKey);
                if (fullData) {
                  totalItemSize += fullData.length * 2; // UTF-16 编码，每个字符占2字节
                }
              }

              items.push({
                id,
                name,
                size: totalItemSize,
                timestamp,
                chunked,
                compressed,
                serverData,
                chunkCount,
                type: itemData.dataType || '光照数据'
              });
            } catch (parseError) {
              console.error('缓存管理器: 解析缓存项失败:', parseError);

              // 检查是否是压缩数据相关键
              if (key.includes('compressed') || key.includes('detail')) {
                console.log(`缓存管理器: 跳过压缩数据相关键: ${key}`);
              } else {
                // 解析失败时使用默认值
                const id = key.replace(CACHE_CONFIG.CACHE_KEY_PREFIX, '');
                items.push({
                  id,
                  name: `数据集 ${id.substring(0, 8)}`,
                  size: itemStr.length * 2 + (chunkSizes[id] || 0) + (compressedSizes[id] || 0),
                  timestamp: Date.now(),
                  chunked: false,
                  compressed: false,
                  serverData: false,
                  chunkCount: 0,
                  type: '光照数据'
                });
              }
            }
          }
        }
      }

      // 收集项目数据信息
      for (const key of Object.keys(localStorage)) {
        if (key.startsWith(CACHE_CONFIG.PROJECT_KEY_PREFIX) && !key.includes('hourly_')) {
          const itemStr = localStorage.getItem(key);
          if (itemStr) {
            try {
              const id = key.replace(CACHE_CONFIG.PROJECT_KEY_PREFIX, '');
              const mainSize = itemStr.length * 2; // UTF-16编码，每个字符占2字节
              const timestamp = Date.now(); // 项目数据没有记录时间戳，使用当前时间

              // 尝试解析数据以获取名称
              const itemData = JSON.parse(itemStr);
              const name = itemData.name || `项目 ${id.substring(0, 8)}`;

              // 计算总大小（主数据 + 小时数据）
              let totalItemSize = mainSize;
              if (projectHourlySizes[id]) {
                totalItemSize += projectHourlySizes[id];
                console.log(`缓存管理器: 项目 ${id} (${name}) 的总大小: ${totalItemSize} 字节, 主数据: ${mainSize} 字节, 小时数据: ${projectHourlySizes[id]} 字节`);
              } else {
                console.log(`缓存管理器: 项目 ${id} (${name}) 的总大小: ${totalItemSize} 字节, 主数据: ${mainSize} 字节, 无小时数据`);
              }

              items.push({
                id,
                name,
                size: totalItemSize,
                timestamp,
                chunked: false,
                compressed: false,
                serverData: itemData.syncStatus === 'synced',
                chunkCount: 0,
                type: itemData.dataType || '项目数据'
              });
            } catch (parseError) {
              console.error('缓存管理器: 解析项目数据失败:', parseError);
              // 解析失败时使用默认值
              const id = key.replace(CACHE_CONFIG.PROJECT_KEY_PREFIX, '');
              items.push({
                id,
                name: `项目 ${id.substring(0, 8)}`,
                size: itemStr.length * 2 + (projectHourlySizes[id] || 0),
                timestamp: Date.now(),
                chunked: false,
                compressed: false,
                serverData: false,
                chunkCount: 0,
                type: '项目数据'
              });
            }
          }
        }
      }

      // 按时间戳排序，最新的在前
      items.sort((a, b) => b.timestamp - a.timestamp);

      return {
        itemCount,
        usedSpace: totalSize,
        maxSpace: this.maxCacheSize,
        usagePercentage: percentage,
        items
      };
    } catch (error) {
      console.error('缓存管理器: 获取缓存统计信息失败:', error);
      return {
        itemCount: 0,
        usedSpace: 0,
        maxSpace: this.maxCacheSize,
        usagePercentage: 0,
        items: []
      };
    }
  }
}

// 导出缓存管理器实例
export const cacheManager = CacheManager.getInstance();

/**
 * 获取数据同步状态
 * @param id 数据ID
 * @param isInServer 是否在服务器上
 * @returns 同步状态
 */
export const getSyncStatus = (id: string, isInServer: boolean): 'synced' | 'local-only' | 'server-only' => {
  // 检查本地存储
  const key = `pv_irradiance_${id}`;
  const storedData = localStorage.getItem(key);
  const isInLocal = storedData !== null;

  console.log(`检查数据 ${id} 的同步状态, 本地存在: ${isInLocal}, 服务器存在: ${isInServer}`);

  // 检查是否是压缩数据键
  if (key.includes('compressed') || key.includes('detail')) {
    console.log(`跳过压缩数据键: ${key}`);
    // 如果是压缩数据键，直接返回服务器状态
    return isInServer ? 'server-only' : 'local-only';
  }

  // 如果本地有数据，检查其完整性
  if (isInLocal && storedData) {
    try {
      const data = JSON.parse(storedData);

      // 检查数据完整性
      // 如果是压缩数据，检查是否有对应的压缩数据文件
      let isDataComplete = false;
      if (data.compressed === true) {
        const compressedKey = `${CACHE_CONFIG.COMPRESSED_KEY_PREFIX}${id}`;
        const compressedData = localStorage.getItem(compressedKey);
        isDataComplete = compressedData !== null && compressedData.length > 0;
      }
      // 如果是服务器数据但未压缩，检查是否有完整数据文件
      else if (data.serverData === true && !data.compressed) {
        const fullDataKey = `${CACHE_CONFIG.CACHE_KEY_PREFIX}full_${id}`;
        const fullData = localStorage.getItem(fullDataKey);
        isDataComplete = fullData !== null;
      }
      // 否则检查数据数组是否存在且不为空
      else {
        isDataComplete = data.data && Array.isArray(data.data) && data.data.length > 0;
      }
      console.log(`数据 ${id} 本地完整性: ${isDataComplete}, 当前同步状态: ${data.syncStatus || '未设置'}`);

      // 如果数据已经有明确的同步状态为"已同步"，且数据完整，保持"已同步"状态
      if (data.syncStatus === 'synced' && isDataComplete) {
        console.log(`数据 ${id} 已标记为"已同步"且数据完整，保持"已同步"状态`);
        return 'synced';
      }

      // 如果数据不完整但在服务器上存在，则视为"仅服务器"
      if (!isDataComplete && isInServer) {
        console.log(`数据 ${id} 本地存在但不完整，服务器上存在，视为"仅服务器"`);
        return 'server-only';
      }

      // 如果数据已经有同步状态且数据完整，优先使用已有状态
      if (data.syncStatus && isDataComplete) {
        console.log(`数据 ${id} 使用已有同步状态: ${data.syncStatus}`);
        return data.syncStatus;
      }

      // 如果数据不完整且有明确的同步状态为"已同步"，改为"仅服务器"
      if (!isDataComplete && data.syncStatus === 'synced') {
        console.log(`数据 ${id} 标记为已同步但不完整，改为"仅服务器"`);
        return 'server-only';
      }

      // 如果数据不完整且有明确的同步状态为"仅本地"，保持"仅本地"状态
      // 这种情况下用户需要重新上传或替换数据
      if (!isDataComplete && data.syncStatus === 'local-only') {
        console.log(`数据 ${id} 标记为仅本地但不完整，保持"仅本地"状态`);
        return 'local-only';
      }
    } catch (e) {
      console.error('解析同步状态失败:', e);

      // 检查是否是压缩数据相关键
      if (key.includes('compressed') || key.includes('detail')) {
        console.log(`跳过解析压缩数据相关键: ${key}`);
      } else {
        // 解析失败，如果服务器上有数据，则视为"仅服务器"
        if (isInServer) {
          return 'server-only';
        }
      }
    }
  }

  // 如果没有明确的同步状态，根据是否在本地和服务器上判断
  if (isInLocal && isInServer) {
    console.log(`数据 ${id} 本地和服务器都存在，设置为"已同步"`);
    return 'synced';
  } else if (isInLocal) {
    console.log(`数据 ${id} 仅本地存在，设置为"仅本地"`);
    return 'local-only';
  } else {
    console.log(`数据 ${id} 仅服务器存在，设置为"仅服务器"`);
    return 'server-only';
  }
};
