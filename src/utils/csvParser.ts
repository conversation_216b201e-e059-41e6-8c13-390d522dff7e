/**
 * CSV解析工具
 * 用于解析光照数据CSV文件
 */
import { CSVParseResult, IrradianceHourlyData } from '../types/database';

/**
 * 解析光照数据CSV文件
 * @param file CSV文件
 * @returns 解析结果Promise
 */
export const parseIrradianceCSV = (file: File): Promise<CSVParseResult> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        if (!event.target || !event.target.result) {
          throw new Error('读取文件失败');
        }

        const csvContent = event.target.result as string;
        const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

        if (lines.length < 4) {
          throw new Error('CSV格式错误：文件内容不完整');
        }

        // 解析城市名（第一行）
        const cityNameLine = lines[0].split(',');
        const cityName = cityNameLine[0]?.trim() || '';

        if (!cityName) {
          throw new Error('CSV格式错误：无法解析城市名');
        }

        console.log('解析到城市名:', cityName);

        // 解析经纬度（第二行）
        const coordinatesLine = lines[1].split(',')[0] || '';
        const coordinates = coordinatesLine.trim().split(/\s+/).filter(Boolean);

        if (coordinates.length < 2) {
          throw new Error('CSV格式错误：无法解析经纬度');
        }

        const latitude = parseFloat(coordinates[0]);
        const longitude = parseFloat(coordinates[1]);

        if (isNaN(latitude) || isNaN(longitude)) {
          throw new Error('CSV格式错误：经纬度格式不正确');
        }

        console.log('解析到经纬度:', latitude, longitude);

        // 找到数据标题行（通常是第四行）
        let headerIndex = -1;
        for (let i = 2; i < Math.min(10, lines.length); i++) {
          if (lines[i].includes('y') && lines[i].includes('m') && lines[i].includes('dm') && lines[i].includes('h')) {
            headerIndex = i;
            break;
          }
        }

        if (headerIndex === -1) {
          throw new Error('CSV格式错误：无法找到数据标题行');
        }

        console.log('找到标题行，索引:', headerIndex);

        // 解析数据
        const data: IrradianceHourlyData[] = [];

        for (let i = headerIndex + 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const values = line.split(',').map(val => val.trim());
          if (values.length < 11) {
            console.warn(`第${i+1}行数据列数不足，跳过:`, values.length);
            continue;
          }

          try {
            const hourlyData: IrradianceHourlyData = {
              year: parseInt(values[0]),
              month: parseInt(values[1]),
              day: parseInt(values[2]),
              hour: parseInt(values[3]),
              temperature: parseFloat(values[4]),
              sunHeight: parseFloat(values[5]),
              sunAngle: parseFloat(values[6]),
              globalHorizontalIrradiance: parseFloat(values[7]),
              diffuseHorizontalIrradiance: parseFloat(values[8]),
              directTime: parseFloat(values[9]),
              directNormalIrradiance: parseFloat(values[10])
            };

            // 验证数据有效性
            if (
              !isNaN(hourlyData.year) &&
              !isNaN(hourlyData.month) &&
              !isNaN(hourlyData.day) &&
              !isNaN(hourlyData.hour) &&
              hourlyData.month >= 1 && hourlyData.month <= 12 &&
              hourlyData.day >= 1 && hourlyData.day <= 31 &&
              hourlyData.hour >= 1 && hourlyData.hour <= 24
            ) {
              data.push(hourlyData);
            } else {
              console.warn(`第${i+1}行数据验证失败:`, hourlyData);
            }
          } catch (error) {
            console.warn(`解析第${i+1}行数据时出错:`, error);
            // 继续解析下一行
          }
        }

        console.log(`成功解析了${data.length}条数据`);

        if (data.length === 0) {
          throw new Error('CSV格式错误：未找到有效数据');
        }

        resolve({
          cityName,
          latitude,
          longitude,
          data
        });
      } catch (error) {
        console.error('CSV解析失败:', error);
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('读取文件失败'));
    };

    reader.readAsText(file);
  });
};

/**
 * 计算每日平均水平辐射强度
 * @param data 小时数据
 * @param year 年份
 * @param month 月份
 * @param day 日期
 * @returns 平均水平辐射强度 (W/m²)
 */
export const calculateDailyAverageIrradiance = (
  data: IrradianceHourlyData[],
  year: number,
  month: number,
  day: number
): number => {
  const dayData = data.filter(
    item => item.year === year && item.month === month && item.day === day
  );

  if (dayData.length === 0) return 0;

  const sum = dayData.reduce((acc, item) => acc + item.globalHorizontalIrradiance, 0);
  return sum / 24; // 除以24小时得到日平均值
};

/**
 * 计算每日累计辐射量
 * @param data 小时数据
 * @param year 年份
 * @param month 月份
 * @param day 日期
 * @returns 累计辐射量 (kWh/m²)
 */
export const calculateDailyTotalIrradiance = (
  data: IrradianceHourlyData[],
  year: number,
  month: number,
  day: number
): number => {
  const dayData = data.filter(
    item => item.year === year && item.month === month && item.day === day
  );

  if (dayData.length === 0) return 0;

  const sum = dayData.reduce((acc, item) => acc + item.globalHorizontalIrradiance, 0);
  return sum / 1000; // 除以1000转换为kWh/m²
};
