/**
 * 光伏组件数据结构定义
 * 包含每个光伏组件的发电量和收益数据
 */

/**
 * 光伏组件小时数据点类型
 * 记录每个组件每小时的发电和收益数据
 */
export interface ModuleHourlyData {
  // 时间标识
  hour: number;       // 小时 (0-23)
  day: number;        // 日 (1-31)
  month: number;      // 月 (1-12)

  // 光伏发电数据
  pvGeneration: number;  // 本小时光伏发电量 (kWh)

  // 经济效益数据
  pvBenefit: number;     // 本小时光伏收益 (JPY)
}

/**
 * 光伏组件日数据类型
 * 汇总每个组件每日的发电和收益数据
 */
export interface ModuleDailyData {
  day: number;        // 日 (1-31)
  month: number;      // 月 (1-12)

  // 光伏发电数据
  pvGeneration: number;  // 当日光伏发电量 (kWh)

  // 经济效益数据
  pvBenefit: number;     // 当日光伏收益 (JPY)
}

/**
 * 光伏组件月数据类型
 * 汇总每个组件每月的发电和收益数据
 */
export interface ModuleMonthlyData {
  month: number;      // 月 (1-12)

  // 光伏发电数据
  pvGeneration: number;  // 当月光伏发电量 (kWh)

  // 经济效益数据
  pvBenefit: number;     // 当月光伏收益 (JPY)
}

/**
 * 光伏组件年数据类型
 * 汇总每个组件全年的发电和收益数据
 */
export interface ModuleYearlyData {
  // 光伏发电数据
  pvGeneration: number;  // 全年光伏发电量 (kWh)

  // 经济效益数据
  pvBenefit: number;     // 全年光伏收益 (JPY)
}

/**
 * 光伏组件分析结果类型
 */
export interface ModuleAnalysisResults {
  // 组件ID
  moduleId: string;

  // 详细数据
  hourlyData: ModuleHourlyData[];   // 8760个小时数据点
  dailyData: ModuleDailyData[];     // 365个日数据点
  monthlyData: ModuleMonthlyData[]; // 12个月数据点
  yearlyData: ModuleYearlyData;     // 年度汇总数据
}

/**
 * 创建空的光伏组件小时数据点
 */
export const createEmptyModuleHourlyData = (hour: number, day: number, month: number): ModuleHourlyData => {
  return {
    hour,
    day,
    month,
    pvGeneration: 0,
    pvBenefit: 0
  };
};

/**
 * 创建空的光伏组件小时数据数组 (8760个小时)
 */
export const createEmptyModuleHourlyDataArray = (): ModuleHourlyData[] => {
  const hourlyData: ModuleHourlyData[] = [];
  
  // 每月的天数（非闰年）
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  
  // 遍历每个月的每一天的每个小时
  for (let month = 1; month <= 12; month++) {
    for (let day = 1; day <= daysInMonth[month - 1]; day++) {
      for (let hour = 0; hour < 24; hour++) {
        hourlyData.push(createEmptyModuleHourlyData(hour, day, month));
      }
    }
  }
  
  return hourlyData;
};
