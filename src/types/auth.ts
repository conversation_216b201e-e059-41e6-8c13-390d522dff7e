// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'user';
  createdAt: string;
}

// 登录请求类型
export interface LoginRequest {
  username: string;
  password: string;
  remember?: boolean;
}

// 注册请求类型
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 认证响应类型
export interface AuthResponse {
  user: User;
  token: string;
}

// 修改密码请求类型
export interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}
