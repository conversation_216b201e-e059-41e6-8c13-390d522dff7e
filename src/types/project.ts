// 项目状态类型
export type ProjectStatus = 'draft' | 'analyzing' | 'completed';

// 项目基础信息类型
export interface ProjectBasicInfo {
  name: string;
  location: string;
  capacity: number;
  description?: string;
  startDate?: string;
  endDate?: string;
}

// 光伏模块类型
export interface PVModule {
  id: string;
  name: string;
  manufacturer: string;
  model: string;
  power: number; // 单位：W
  efficiency: number; // 效率，百分比
  area: number; // 单位：m²
  price: number;
  quantity: number;
  angle: number; // 安装角度
  orientation: string; // 朝向
  orientationAngle?: number; // 朝向角度，与正南方向的夹角，东偏为负，西偏为正
}

// 储能设备类型
export interface EnergyStorage {
  id: string;
  name: string;
  manufacturer: string;
  model: string;
  capacity: number; // 单位：kWh
  power: number; // 单位：kW
  efficiency: number; // 效率，百分比
  cycles: number; // 循环次数
  price: number;
  quantity: number;
}

// 逆变器类型
export interface Inverter {
  id: string;
  name: string;
  manufacturer: string;
  model: string;
  power: number; // 单位：kW
  efficiency: number; // 效率，百分比
  price: number;
  quantity: number;
}

// 其他投资项类型
export interface OtherInvestment {
  id: string;
  name: string;
  category: string;
  price: number;
  description?: string;
}

// 用电数据类型
export interface ElectricityUsage {
  type: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'sameEveryday' | 'sameEveryWeek' | 'monthlyDifferent' | 'dailyDifferent';
  data: Array<{
    hour?: number;
    day?: number;
    week?: number;
    month?: number;
    value: number; // 单位：kWh
  }>;
}

// 分析结果类型
export interface AnalysisResult {
  hourlyData: Array<{
    hour: number;
    day: number;
    month: number;
    pvGeneration: number; // 光伏发电量，单位：kWh
    storageCapacity: number; // 储能容量，单位：kWh
    electricityConsumption: {
      pv: number; // 光伏供电，单位：kWh
      storage: number; // 储能供电，单位：kWh
      grid: number; // 电网供电，单位：kWh
    };
    gridExport: number; // 余电上网量，单位：kWh
    storageCharge: number; // 储能充电量，单位：kWh
    storageDischarge: number; // 储能放电量，单位：kWh
    storageBenefit: number; // 储能套利金额，单位：JPY
    pvBenefit: number; // 光伏发电金额，单位：JPY
    gridExportBenefit: number; // 余电上网金额，单位：JPY
    totalBenefit: number; // 总收益金额，单位：JPY
  }>;
  dailyData: Array<{
    day: number;
    month: number;
    pvGeneration: number;
    electricityConsumption: {
      pv: number;
      storage: number;
      grid: number;
    };
    gridExport: number;
    storageBenefit: number;
    pvBenefit: number;
    gridExportBenefit: number;
    totalBenefit: number;
  }>;
  monthlyData: Array<{
    month: number;
    pvGeneration: number;
    electricityConsumption: {
      pv: number;
      storage: number;
      grid: number;
    };
    gridExport: number;
    storageBenefit: number;
    pvBenefit: number;
    gridExportBenefit: number;
    totalBenefit: number;
  }>;
  yearlyData: {
    pvGeneration: number;
    electricityConsumption: {
      pv: number;
      storage: number;
      grid: number;
    };
    gridExport: number;
    storageBenefit: number;
    pvBenefit: number;
    gridExportBenefit: number;
    totalBenefit: number;
  };
  roi: number; // 投资回报率，百分比
  paybackPeriod: number; // 回收期，单位：年
}

// 同步状态类型
export type SyncStatus = 'synced' | 'local-only' | 'server-only' | 'invalid';

// 项目类型
export interface Project {
  id: string;
  name: string;
  location: string;
  capacity: number;
  status: ProjectStatus;
  createdAt: string;
  updatedAt: string;
  syncStatus?: SyncStatus;
  basicInfo: ProjectBasicInfo;
  irradianceDataId: string;
  electricityPriceId: string;
  electricityUsage: ElectricityUsage;
  pvModules: PVModule[];
  energyStorage: EnergyStorage[];
  inverters: Inverter[];
  otherInvestments: OtherInvestment[];
  analysisResults?: AnalysisResult;
}
