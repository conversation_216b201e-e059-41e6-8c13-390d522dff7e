/* 暂时注释掉Tailwind CSS
@tailwind base;
@tailwind components;
@tailwind utilities;
*/

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 浅色主题变量 */
  --background-color: #ffffff;
  --component-background: #ffffff;
  --body-background: #f0f2f5;
  --popup-background: #ffffff;
  --text-color: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --heading-color: rgba(0, 0, 0, 0.85);
  --border-color: #d9d9d9;
  --border-color-split: #f0f0f0;
  --primary-color: #1677ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --disabled-color: rgba(0, 0, 0, 0.25);
  --disabled-bg: #f5f5f5;
  --item-hover-bg: #f5f5f5;
  --item-active-bg: #e6f4ff;

  color-scheme: light;
}

/* 深色主题变量 */
:root.dark {
  --background-color: #141414;
  --component-background: #1f1f1f;
  --body-background: #000000;
  --popup-background: #1f1f1f;
  --text-color: rgba(255, 255, 255, 0.85);
  --text-color-secondary: rgba(255, 255, 255, 0.45);
  --heading-color: rgba(255, 255, 255, 0.85);
  --border-color: #434343;
  --border-color-split: #303030;
  --primary-color: #1668dc;
  --success-color: #49aa19;
  --warning-color: #d89614;
  --error-color: #d32029;
  --disabled-color: rgba(255, 255, 255, 0.25);
  --disabled-bg: rgba(255, 255, 255, 0.08);
  --item-hover-bg: rgba(255, 255, 255, 0.08);
  --item-active-bg: #1668dc20;

  color-scheme: dark;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}

/* 全局深色模式样式覆盖 */
.dark .ant-layout {
  background-color: var(--background-color);
}

.dark .ant-layout-header {
  background-color: var(--background-color);
  border-bottom-color: var(--border-color);
}

.dark .ant-layout-sider {
  background-color: var(--background-color) !important;
  border-right-color: var(--border-color);
}

.dark-sider {
  background-color: var(--background-color) !important;
}

.light-sider {
  background-color: var(--background-color) !important;
}

.dark .ant-layout-content {
  background-color: var(--background-color);
}

.dark .ant-layout-footer {
  background-color: var(--background-color);
  border-top-color: var(--border-color);
}

.dark .ant-card {
  background-color: var(--background-color);
  border-color: var(--border-color);
}

.dark .ant-menu {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
}

.dark .ant-menu-light {
  background-color: var(--background-color) !important;
}

.dark .ant-menu-dark {
  background-color: var(--component-background) !important;
}

.dark .ant-menu-item {
  color: var(--text-color) !important;
}

.dark .ant-menu-submenu-title {
  color: var(--text-color) !important;
}

.dark .ant-menu-item-selected {
  background-color: var(--item-active-bg) !important;
  color: var(--primary-color) !important;
}

/* 浅色模式下的菜单样式 */
.ant-menu-light {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
}

.ant-menu-dark {
  background-color: var(--component-background) !important;
  color: rgba(255, 255, 255, 0.85) !important;
}

/* 确保菜单项在浅色模式下的样式 */
.ant-menu-light .ant-menu-item {
  color: var(--text-color) !important;
}

.ant-menu-light .ant-menu-submenu-title {
  color: var(--text-color) !important;
}

.ant-menu-light .ant-menu-item-selected {
  background-color: var(--item-active-bg) !important;
  color: var(--primary-color) !important;
}

/* 确保菜单项在深色模式下的样式 */
.ant-menu-dark .ant-menu-item {
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-menu-dark .ant-menu-submenu-title {
  color: rgba(255, 255, 255, 0.85) !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
}

.dark .ant-input {
  background-color: var(--component-background);
  border-color: var(--border-color);
  color: var(--text-color);
}

.dark .ant-select-selector {
  background-color: var(--component-background) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.dark .ant-select-dropdown {
  background-color: var(--popup-background);
  border-color: var(--border-color);
}

.dark .ant-select-item {
  color: var(--text-color);
}

.dark .ant-select-item-option-selected {
  background-color: var(--item-active-bg);
}

.dark .ant-btn {
  border-color: var(--border-color);
  color: var(--text-color);
}

.dark .ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
}

/* 表格样式 - 深色模式 */
.dark .ant-table,
.dark-table {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
}

.dark .ant-table-thead > tr > th,
.dark-table .ant-table-thead > tr > th {
  background-color: var(--component-background) !important;
  color: var(--text-color) !important;
  border-bottom-color: var(--border-color) !important;
}

.dark .ant-table-tbody > tr > td,
.dark-table .ant-table-tbody > tr > td {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
  border-bottom-color: var(--border-color) !important;
}

.dark .ant-table-tbody > tr:hover > td,
.dark-table .ant-table-tbody > tr:hover > td,
tr.ant-table-row.dark-row:hover > td {
  background-color: var(--item-hover-bg) !important;
}

.dark .ant-table-row-selected > td,
.dark-table .ant-table-row-selected > td,
tr.ant-table-row.dark-row.ant-table-row-selected > td {
  background-color: var(--item-active-bg) !important;
}

/* 确保表格悬停效果在深色模式下正确显示 */
.dark .ant-table-tbody > tr.ant-table-row:hover > td,
.dark-table .ant-table-tbody > tr.ant-table-row:hover > td,
tr.ant-table-row.dark-row:hover > td {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

/* 表格样式 - 浅色模式 */
.ant-table {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
}

.ant-table-thead > tr > th {
  background-color: var(--component-background) !important;
  color: var(--text-color) !important;
  border-bottom-color: var(--border-color) !important;
}

.ant-table-tbody > tr > td {
  background-color: var(--background-color) !important;
  color: var(--text-color) !important;
  border-bottom-color: var(--border-color) !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: var(--item-hover-bg) !important;
}

.ant-table-row-selected > td {
  background-color: var(--item-active-bg) !important;
}

/* 表格交替行颜色 */
.ant-table-tbody > tr.ant-table-row:nth-child(odd) > td {
  background-color: var(--background-color) !important;
}

.ant-table-tbody > tr.ant-table-row:nth-child(even) > td {
  background-color: var(--component-background) !important;
}

.dark .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td,
.dark-table .ant-table-tbody > tr.ant-table-row:nth-child(odd) > td,
tr.ant-table-row.dark-row:nth-child(odd) > td {
  background-color: var(--background-color) !important;
}

.dark .ant-table-tbody > tr.ant-table-row:nth-child(even) > td,
.dark-table .ant-table-tbody > tr.ant-table-row:nth-child(even) > td,
tr.ant-table-row.dark-row:nth-child(even) > td {
  background-color: var(--component-background) !important;
}

/* 表格边框 */
.ant-table-container,
.ant-table-wrapper,
.ant-table,
.ant-table-content,
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  border-color: var(--border-color) !important;
}

.dark .ant-table-container,
.dark .ant-table-wrapper,
.dark .ant-table,
.dark .ant-table-content,
.dark .ant-table-thead > tr > th,
.dark .ant-table-tbody > tr > td,
.dark-table .ant-table-container,
.dark-table .ant-table-wrapper,
.dark-table .ant-table,
.dark-table .ant-table-content,
.dark-table .ant-table-thead > tr > th,
.dark-table .ant-table-tbody > tr > td {
  border-color: var(--border-color) !important;
}

.dark .ant-modal-content {
  background-color: var(--background-color);
}

.dark .ant-modal-header {
  background-color: var(--background-color);
  border-bottom-color: var(--border-color);
}

.dark .ant-modal-title {
  color: var(--text-color);
}

.dark .ant-modal-footer {
  border-top-color: var(--border-color);
}

/* 表格分页器样式 */
.dark .ant-pagination-item {
  background-color: var(--component-background) !important;
  border-color: var(--border-color) !important;
}

.dark .ant-pagination-item a {
  color: var(--text-color) !important;
}

.dark .ant-pagination-item-active {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.dark .ant-pagination-item-active a {
  color: #ffffff !important;
}

.dark .ant-pagination-prev button,
.dark .ant-pagination-next button {
  background-color: var(--component-background) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.dark .ant-pagination-disabled button {
  color: var(--disabled-color) !important;
  background-color: var(--disabled-bg) !important;
}

/* 表格空状态样式 */
.dark .ant-empty {
  color: var(--text-color-secondary) !important;
}

.dark .ant-empty-img-simple-ellipse {
  fill: var(--component-background) !important;
}

.dark .ant-empty-img-simple-g {
  stroke: var(--border-color) !important;
}

.dark .ant-empty-img-simple-path {
  fill: var(--component-background) !important;
  stroke: var(--border-color) !important;
}
