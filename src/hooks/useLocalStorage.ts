import { useState, useEffect } from 'react';

/**
 * 使用localStorage的自定义钩子
 * @param key 存储键名
 * @param initialValue 初始值
 * @returns [存储的值, 设置值的函数]
 */
export function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T) => void] {
  // 创建状态变量
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // 尝试从localStorage获取值
      const item = window.localStorage.getItem(key);
      // 如果存在则解析并返回，否则返回初始值
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // 如果出错则返回初始值
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // 当值变化时更新localStorage
  const setValue = (value: T) => {
    try {
      // 允许值为函数
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      // 保存到状态
      setStoredValue(valueToStore);
      // 保存到localStorage
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  // 监听其他窗口的localStorage变化
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue) {
        setStoredValue(JSON.parse(e.newValue));
      }
    };

    // 添加事件监听
    window.addEventListener('storage', handleStorageChange);

    // 清理函数
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key]);

  return [storedValue, setValue];
}
