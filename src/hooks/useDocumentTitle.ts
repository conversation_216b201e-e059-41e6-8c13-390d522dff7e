import { useEffect } from 'react';
import { useAppSelector } from '../store';

/**
 * 自定义钩子，用于动态更新文档标题和网页图标
 * 根据设置中的网站名称和logo更新浏览器标题栏和图标
 */
export const useDocumentTitle = () => {
  const { siteName, siteLogo } = useAppSelector((state) => state.settings);

  useEffect(() => {
    // 如果有设置网站名称，则使用它作为文档标题
    if (siteName) {
      document.title = siteName;
    }
  }, [siteName]);

  useEffect(() => {
    // 如果有设置网站logo，则使用它作为网页图标
    if (siteLogo) {
      // 查找现有的图标链接元素
      const existingLink = document.querySelector('link[rel="icon"]');

      // 确保logo URL是有效的
      let logoUrl = siteLogo;

      // 如果是相对路径，添加服务器基础URL
      if (logoUrl && !logoUrl.startsWith('http') && !logoUrl.startsWith('data:')) {
        const baseUrl = window.location.origin;
        const url = logoUrl.startsWith('/') ? logoUrl : `/${logoUrl}`;
        logoUrl = `${baseUrl}${url}`;
      }

      if (existingLink) {
        // 如果存在，则更新href属性
        existingLink.setAttribute('href', logoUrl);
      } else {
        // 如果不存在，则创建新的链接元素
        const link = document.createElement('link');
        link.rel = 'icon';
        link.href = logoUrl;
        document.head.appendChild(link);
      }
    }
  }, [siteLogo]);

  return null;
};

export default useDocumentTitle;
