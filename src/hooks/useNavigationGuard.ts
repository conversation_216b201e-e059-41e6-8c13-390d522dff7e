import { useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * 导航守卫钩子
 * 用于在用户尝试离开当前页面时进行拦截
 * 
 * @param hasChanges 是否有未保存的更改
 * @param onNavigationAttempt 导航尝试回调函数，接收目标路径作为参数
 */
export function useNavigationGuard(
  hasChanges: boolean,
  onNavigationAttempt: (targetPath: string) => void
) {
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;

  // 处理浏览器的前进/后退按钮
  useEffect(() => {
    // 只有当有未保存的更改时才添加事件监听
    if (!hasChanges) return;

    // 拦截浏览器的前进/后退操作
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // 显示浏览器的确认对话框
      event.preventDefault();
      event.returnValue = '';
      return '';
    };

    // 添加事件监听
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasChanges]);

  // 拦截React Router的导航
  const interceptNavigation = useCallback(
    (targetPath: string) => {
      // 如果目标路径与当前路径相同，或者没有未保存的更改，则直接导航
      if (targetPath === currentPath || !hasChanges) {
        navigate(targetPath);
        return;
      }

      // 否则，触发导航尝试回调
      onNavigationAttempt(targetPath);
    },
    [currentPath, hasChanges, navigate, onNavigationAttempt]
  );

  return { interceptNavigation };
}
