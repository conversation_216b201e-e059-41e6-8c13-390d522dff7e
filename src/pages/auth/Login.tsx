import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Checkbox, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch } from '../../store';
import { loginStart, loginSuccess, loginFailure } from '../../store/slices/authSlice';

const Login: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // 从环境变量中获取管理员账号
  const [adminUsername, setAdminUsername] = useState('admin');
  const [adminPassword, setAdminPassword] = useState('admin');

  // 获取环境变量中的管理员账号
  useEffect(() => {
    // 尝试从环境变量中获取管理员账号
    const envUsername = import.meta.env.VITE_ADMIN_USERNAME;
    const envPassword = import.meta.env.VITE_ADMIN_PASSWORD;

    if (envUsername) {
      setAdminUsername(envUsername);
    }

    if (envPassword) {
      setAdminPassword(envPassword);
    }

    console.log('管理员账号已配置:', envUsername ? '是' : '否');
  }, []);

  // 处理登录
  const handleLogin = (values: { username: string; password: string; remember: boolean }) => {
    dispatch(loginStart());

    // 模拟登录请求
    setTimeout(() => {
      // 这里应该是实际的API调用
      if ((values.username === 'admin' && values.password === 'admin') ||
          (values.username === adminUsername && values.password === adminPassword)) {
        dispatch(
          loginSuccess({
            user: {
              id: '1',
              username: values.username,
              email: `${values.username}@example.com`,
            },
            token: 'fake-token',
          })
        );
        message.success(t('common.success'));
        navigate('/projects');
      } else {
        dispatch(loginFailure('Invalid username or password'));
        message.error(t('common.error'));
      }
    }, 1000);
  };

  return (
    <Card style={{
      width: '100%',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
      background: 'var(--background-color)',
      border: '1px solid var(--border-color)'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '24px' }}>
        <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>{t('app.title')}</h1>
        <p style={{ color: 'var(--text-color)' }}>{t('auth.login')}</p>
      </div>

      <Form
        name="login"
        initialValues={{ remember: true }}
        onFinish={handleLogin}
        layout="vertical"
      >
        <Form.Item
          name="username"
          rules={[{ required: true, message: t('common.required') }]}
        >
          <Input
            prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
            placeholder={t('auth.username')}
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[{ required: true, message: t('common.required') }]}
        >
          <Input.Password
            prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
            placeholder={t('auth.password')}
            size="large"
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox>{t('auth.remember')}</Checkbox>
            </Form.Item>
            <Link to="/forgot-password" style={{ color: '#1890ff' }}>
              {t('auth.forgotPassword')}
            </Link>
          </div>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ width: '100%' }} size="large">
            {t('auth.login')}
          </Button>
        </Form.Item>

        <div style={{ textAlign: 'center' }}>
          <span style={{ color: '#8c8c8c' }}>{t('auth.noAccount')} </span>
          <Link to="/register" style={{ color: '#1890ff' }}>
            {t('auth.register')}
          </Link>
        </div>
      </Form>
    </Card>
  );
};

export default Login;
