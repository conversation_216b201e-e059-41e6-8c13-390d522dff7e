import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Divider,
  message,
  Tag,
  Row,
  Col,
  Tooltip,
  Tabs,
  Typography
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BulbOutlined,
  DatabaseOutlined,
  <PERSON>boltOutlined,
  AppstoreOutlined,
  FileOutlined,
  UploadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  fetchDataStart,
  fetchEquipmentSuccess,
  addEquipment,
  updateEquipment,
  deleteEquipment as deleteEquipmentAction
} from '../../store/slices/databasesSlice';
import {
  PageHeader,
  EmptyState,
  LoadingSpinner,
  SyncStatus,
  ConfirmDialog,
  FilterForm,
  EquipmentForm,
  AttachmentPreview
} from '../../components/common';
import {
  getEquipmentList,
  getEquipmentDetail,
  createEquipment,
  updateEquipment as updateEquipmentService,
  deleteEquipment as deleteEquipmentService
} from '../../services/equipmentService';
import { Equipment, EquipmentAttachment } from '../../types/database';

const { TabPane } = Tabs;

const EquipmentDatabase: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    equipment,
    isLoading
  } = useAppSelector(state => state.databases);

  const [currentEquipment, setCurrentEquipment] = useState<Equipment | null>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string>('');
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('all');

  // 筛选条件
  const [filters, setFilters] = useState({
    type: '',
    manufacturer: '',
    priceRange: ''
  });

  // 获取设备列表
  useEffect(() => {
    fetchEquipmentList();
  }, []);

  // 获取设备列表
  const fetchEquipmentList = async () => {
    try {
      dispatch(fetchDataStart());
      const response = await getEquipmentList();
      dispatch(fetchEquipmentSuccess(response.items));
    } catch (error) {
      console.error('获取设备列表失败:', error);
      dispatch(fetchEquipmentSuccess([]));
    }
  };

  // 处理选择设备
  const handleSelectEquipment = async (id: string) => {
    try {
      if (currentEquipment && currentEquipment.id === id) {
        return;
      }

      const data = await getEquipmentDetail(id);
      setCurrentEquipment(data);
    } catch (error) {
      console.error('获取设备详情失败:', error);
      message.error(t('databases.equipment.loadError'));
    }
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (id: string) => {
    setDeleteItemId(id);
    setDeleteModalVisible(true);
  };

  // 处理删除设备
  const handleDeleteEquipment = async () => {
    try {
      await deleteEquipmentService(deleteItemId);
      dispatch(deleteEquipmentAction(deleteItemId));

      // 如果删除的是当前选中的设备，清空当前设备
      if (currentEquipment && currentEquipment.id === deleteItemId) {
        setCurrentEquipment(null);
      }

      setDeleteModalVisible(false);
      message.success(t('databases.equipment.deleteSuccess'));
    } catch (error) {
      console.error('删除设备失败:', error);
      message.error(t('databases.equipment.deleteError'));
    }
  };

  // 处理创建设备
  const handleCreateEquipment = () => {
    createForm.resetFields();
    setCreateModalVisible(true);
  };

  // 处理提交创建表单
  const handleCreateFormSubmit = async () => {
    try {
      const values = await createForm.validateFields();

      // 创建新的设备
      const newEquipment: Partial<Equipment> = {
        name: values.name,
        type: values.type,
        manufacturer: values.manufacturer,
        model: values.model,
        specs: values.specs,
        price: values.price,
        supplierId: values.supplierId,
      };

      // 调用创建服务
      const createdData = await createEquipment(newEquipment);

      // 更新Redux store
      dispatch(addEquipment(createdData));

      // 关闭创建表单
      setCreateModalVisible(false);
      createForm.resetFields();

      message.success(t('databases.equipment.createSuccess'));

      // 刷新数据列表
      await fetchEquipmentList();
    } catch (error) {
      message.error(t('databases.equipment.createError'));
      console.error('创建设备失败:', error);
    }
  };

  // 处理编辑设备
  const handleEditEquipment = (equipment: Equipment) => {
    // 设置当前选中的设备
    setCurrentEquipment(equipment);

    // 设置表单初始值
    editForm.setFieldsValue({
      ...equipment
    });

    // 显示编辑对话框
    setEditModalVisible(true);

    console.log('编辑设备:', equipment);
  };

  // 处理提交编辑表单
  const handleEditFormSubmit = async () => {
    try {
      console.log('开始提交编辑表单');
      console.log('当前选中的设备:', currentEquipment);

      const values = await editForm.validateFields();
      console.log('表单验证通过，表单值:', values);

      if (!currentEquipment) {
        console.error('没有选中的设备');
        message.error(t('databases.equipment.noEquipmentSelected'));
        return;
      }

      // 更新设备数据
      const updatedEquipment: Equipment = {
        ...currentEquipment,
        name: values.name,
        type: values.type,
        manufacturer: values.manufacturer,
        model: values.model,
        specs: values.specs,
        price: values.price,
        supplierId: values.supplierId,
      };

      console.log('准备更新设备:', updatedEquipment);

      // 调用更新服务
      const updatedData = await updateEquipmentService(currentEquipment.id, updatedEquipment);
      console.log('更新服务返回数据:', updatedData);

      // 更新Redux store
      dispatch(updateEquipment(updatedData));
      console.log('已更新Redux store');

      // 更新当前选中的设备
      setCurrentEquipment(updatedData);

      // 关闭编辑表单
      setEditModalVisible(false);
      console.log('已关闭编辑表单');

      message.success(t('databases.equipment.updateSuccess'));

      // 刷新数据列表
      console.log('开始刷新数据列表');
      await fetchEquipmentList();
      console.log('数据列表刷新完成');
    } catch (error) {
      message.error(t('databases.equipment.updateError'));
      console.error('更新设备失败:', error);
      // 显示详细错误信息
      if (error instanceof Error) {
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
      }
    }
  };

  // 处理筛选条件变更
  const handleFilterChange = (field: string, value: string) => {
    setFilters({
      ...filters,
      [field]: value
    });
  };

  // 重置筛选条件
  const resetFilters = () => {
    setFilters({
      type: '',
      manufacturer: '',
      priceRange: ''
    });
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 获取设备类型图标
  const getEquipmentTypeIcon = (type: string) => {
    switch (type) {
      case 'pv':
        return <BulbOutlined />;
      case 'storage':
        return <DatabaseOutlined />;
      case 'inverter':
        return <ThunderboltOutlined />;
      default:
        return <AppstoreOutlined />;
    }
  };

  // 获取设备类型标签颜色
  const getEquipmentTypeColor = (type: string) => {
    switch (type) {
      case 'pv':
        return 'blue';
      case 'storage':
        return 'green';
      case 'inverter':
        return 'purple';
      default:
        return 'default';
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('databases.equipment.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('databases.equipment.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={getEquipmentTypeColor(type)} icon={getEquipmentTypeIcon(type)}>
          {t(`databases.equipment.${type}`)}
        </Tag>
      ),
    },
    {
      title: t('databases.equipment.manufacturer'),
      dataIndex: 'manufacturer',
      key: 'manufacturer',
    },
    {
      title: t('databases.equipment.model'),
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: t('databases.equipment.attachments.title'),
      dataIndex: 'attachments',
      key: 'attachments',
      render: (attachments: EquipmentAttachment[] | undefined) => (
        attachments && attachments.length > 0 ? (
          <AttachmentPreview
            attachments={attachments}
            size="small"
            maxDisplay={2}
          />
        ) : (
          <Typography.Text type="secondary">
            {t('databases.equipment.attachments.noAttachments')}
          </Typography.Text>
        )
      ),
    },
    {
      title: t('databases.equipment.price'),
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `${price.toLocaleString()} JPY`,
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status: 'synced' | 'local-only' | 'server-only') => (
        <SyncStatus status={status} />
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: Equipment) => (
        <Space size="small">
          <Tooltip title={t('common.edit')}>
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation(); // 阻止事件冒泡
                handleEditEquipment(record);
              }}
            />
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation(); // 阻止事件冒泡
                showDeleteConfirm(record.id);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 筛选选项
  const filterOptions = [
    {
      key: 'type',
      label: t('databases.equipment.type'),
      type: 'select' as const,
      options: [
        { value: 'pv', label: t('databases.equipment.pv') },
        { value: 'storage', label: t('databases.equipment.storage') },
        { value: 'inverter', label: t('databases.equipment.inverter') },
        { value: 'other', label: t('databases.equipment.other') }
      ]
    },
    {
      key: 'manufacturer',
      label: t('databases.equipment.manufacturer'),
      type: 'input' as const,
    },
    {
      key: 'priceRange',
      label: t('databases.equipment.priceRange'),
      type: 'select' as const,
      options: [
        { value: 'low', label: t('databases.equipment.priceLow') },
        { value: 'medium', label: t('databases.equipment.priceMedium') },
        { value: 'high', label: t('databases.equipment.priceHigh') }
      ]
    }
  ];

  // 处理搜索
  const handleSearch = () => {
    console.log('搜索条件:', filters);
    // 这里可以添加搜索逻辑
  };

  // 渲染筛选条件
  const renderFilters = () => (
    <FilterForm
      filters={filters}
      filterOptions={filterOptions}
      onFilterChange={handleFilterChange}
      onSearch={handleSearch}
      onReset={resetFilters}
    />
  );

  // 获取过滤后的设备列表
  const getFilteredEquipment = () => {
    let filteredList = [...equipment];

    // 根据标签页过滤
    if (activeTab !== 'all') {
      filteredList = filteredList.filter(item => item.type === activeTab);
    }

    // 根据筛选条件过滤
    if (filters.type) {
      filteredList = filteredList.filter(item => item.type === filters.type);
    }

    if (filters.manufacturer) {
      filteredList = filteredList.filter(item =>
        item.manufacturer.toLowerCase().includes(filters.manufacturer.toLowerCase())
      );
    }

    if (filters.priceRange) {
      switch (filters.priceRange) {
        case 'low':
          filteredList = filteredList.filter(item => item.price < 100000);
          break;
        case 'medium':
          filteredList = filteredList.filter(item => item.price >= 100000 && item.price < 500000);
          break;
        case 'high':
          filteredList = filteredList.filter(item => item.price >= 500000);
          break;
      }
    }

    return filteredList;
  };

  // 渲染设备列表
  const renderEquipmentList = () => (
    <Card>
      <Tabs activeKey={activeTab} onChange={handleTabChange} tabBarExtraContent={
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateEquipment}>
          {t('databases.equipment.new')}
        </Button>
      }>
        <TabPane tab={t('databases.equipment.all')} key="all">
          {renderEquipmentTable(getFilteredEquipment())}
        </TabPane>
        <TabPane
          tab={<span><BulbOutlined /> {t('databases.equipment.pv')}</span>}
          key="pv"
        >
          {renderEquipmentTable(getFilteredEquipment())}
        </TabPane>
        <TabPane
          tab={<span><DatabaseOutlined /> {t('databases.equipment.storage')}</span>}
          key="storage"
        >
          {renderEquipmentTable(getFilteredEquipment())}
        </TabPane>
        <TabPane
          tab={<span><ThunderboltOutlined /> {t('databases.equipment.inverter')}</span>}
          key="inverter"
        >
          {renderEquipmentTable(getFilteredEquipment())}
        </TabPane>
        <TabPane
          tab={<span><AppstoreOutlined /> {t('databases.equipment.other')}</span>}
          key="other"
        >
          {renderEquipmentTable(getFilteredEquipment())}
        </TabPane>
      </Tabs>
    </Card>
  );

  // 渲染设备表格
  const renderEquipmentTable = (data: any[]) => {
    if (isLoading) {
      return <LoadingSpinner />;
    }

    if (data.length === 0) {
      return (
        <EmptyState
          title={t('databases.equipment.noData')}
          description={t('databases.equipment.createPrompt')}
          actionText={t('databases.equipment.new')}
          onAction={handleCreateEquipment}
        />
      );
    }

    return (
      <Table
        dataSource={data}
        columns={columns}
        rowKey="id"
        onRow={(record) => ({
          onClick: () => handleSelectEquipment(record.id),
          style: { cursor: 'pointer' }
        })}
      />
    );
  };

  // 渲染设备详情
  const renderEquipmentDetail = () => {
    if (!currentEquipment) {
      return (
        <Card title={t('databases.equipment.detail')}>
          <EmptyState
            title={t('databases.equipment.noEquipmentSelected')}
            description={t('databases.equipment.selectEquipmentPrompt')}
          />
        </Card>
      );
    }

    return (
      <Card
        title={t('databases.equipment.detail')}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => handleEditEquipment(currentEquipment)}
            >
              {t('common.edit')}
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={() => showDeleteConfirm(currentEquipment.id)}
            >
              {t('common.delete')}
            </Button>
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <h3>{t('databases.equipment.attachments.title')}</h3>
            {currentEquipment.attachments && currentEquipment.attachments.length > 0 ? (
              <AttachmentPreview
                attachments={currentEquipment.attachments}
                size="large"
                showFileName={true}
                layout="vertical"
              />
            ) : (
              <Typography.Text type="secondary">
                {t('databases.equipment.attachments.noAttachments')}
              </Typography.Text>
            )}
          </Col>
          <Col span={8}>
            <h3>{t('databases.equipment.basicInfo')}</h3>
            <p><strong>{t('databases.equipment.name')}:</strong> {currentEquipment.name}</p>
            <p>
              <strong>{t('databases.equipment.type')}:</strong>
              <Tag color={getEquipmentTypeColor(currentEquipment.type)} icon={getEquipmentTypeIcon(currentEquipment.type)} style={{ marginLeft: 8 }}>
                {t(`databases.equipment.${currentEquipment.type}`)}
              </Tag>
            </p>
            <p><strong>{t('databases.equipment.manufacturer')}:</strong> {currentEquipment.manufacturer}</p>
            <p><strong>{t('databases.equipment.model')}:</strong> {currentEquipment.model}</p>
            <p><strong>{t('databases.equipment.price')}:</strong> {currentEquipment.price.toLocaleString()} JPY</p>
          </Col>
          <Col span={8}>
            <h3>{t('databases.equipment.specs')}</h3>
            {renderEquipmentSpecs(currentEquipment)}
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染设备规格
  const renderEquipmentSpecs = (equipment: Equipment) => {
    const { type, specs } = equipment;

    // 渲染尺寸信息
    const renderDimensions = (specs: any) => {
      if (specs.length || specs.width || specs.height) {
        return (
          <div style={{ marginTop: 16 }}>
            <h4>{t(`databases.equipment.${type}Specs.dimensions`)}</h4>
            {specs.length && <p><strong>{t(`databases.equipment.${type}Specs.length`)}:</strong> {specs.length} mm</p>}
            {specs.width && <p><strong>{t(`databases.equipment.${type}Specs.width`)}:</strong> {specs.width} mm</p>}
            {specs.height && <p><strong>{t(`databases.equipment.${type}Specs.height`)}:</strong> {specs.height} mm</p>}
          </div>
        );
      }
      return null;
    };

    switch (type) {
      case 'pv':
        return (
          <>
            <p><strong>{t('databases.equipment.pvSpecs.power')}:</strong> {specs.power} W</p>
            <p><strong>{t('databases.equipment.pvSpecs.efficiency')}:</strong> {specs.efficiency}%</p>
            <p><strong>{t('databases.equipment.pvSpecs.area')}:</strong> {specs.area} m²</p>
            <p><strong>{t('databases.equipment.pvSpecs.warranty')}:</strong> {specs.warranty} {t('databases.equipment.pvSpecs.years')}</p>
            <p><strong>{t('databases.equipment.pvSpecs.degradation')}:</strong> {specs.degradation}%</p>
            {renderDimensions(specs)}
          </>
        );
      case 'storage':
        return (
          <>
            <p><strong>{t('databases.equipment.storageSpecs.capacity')}:</strong> {specs.capacity} kWh</p>
            <p><strong>{t('databases.equipment.storageSpecs.power')}:</strong> {specs.power} kW</p>
            <p><strong>{t('databases.equipment.storageSpecs.efficiency')}:</strong> {specs.efficiency}%</p>
            <p><strong>{t('databases.equipment.storageSpecs.cycles')}:</strong> {specs.cycles}</p>
            <p><strong>{t('databases.equipment.storageSpecs.warranty')}:</strong> {specs.warranty} {t('databases.equipment.storageSpecs.years')}</p>
            <p><strong>{t('databases.equipment.storageSpecs.depthOfDischarge')}:</strong> {specs.depthOfDischarge}%</p>
            {renderDimensions(specs)}
          </>
        );
      case 'inverter':
        return (
          <>
            <p><strong>{t('databases.equipment.inverterSpecs.power')}:</strong> {specs.power} kW</p>
            <p><strong>{t('databases.equipment.inverterSpecs.efficiency')}:</strong> {specs.efficiency}%</p>
            <p><strong>{t('databases.equipment.inverterSpecs.mpptRange')}:</strong> {specs.mpptRange?.min} - {specs.mpptRange?.max} V</p>
            <p><strong>{t('databases.equipment.inverterSpecs.warranty')}:</strong> {specs.warranty} {t('databases.equipment.inverterSpecs.years')}</p>
            {renderDimensions(specs)}
          </>
        );
      default:
        return (
          <p><strong>{t('databases.equipment.description')}:</strong> {specs.description || t('databases.equipment.noDescription')}</p>
        );
    }
  };

  return (
    <div>
      <PageHeader title={t('databases.equipment.title')} />

      {renderFilters()}

      <Divider />

      {renderEquipmentList()}

      <Divider />

      {renderEquipmentDetail()}

      {/* 创建设备对话框 */}
      <Modal
        title={t('databases.equipment.new')}
        open={createModalVisible}
        onOk={handleCreateFormSubmit}
        onCancel={() => setCreateModalVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        width={700}
      >
        <EquipmentForm form={createForm} mode="create" />
      </Modal>

      {/* 编辑设备对话框 */}
      <Modal
        title={t('databases.equipment.edit')}
        open={editModalVisible}
        onOk={handleEditFormSubmit}
        onCancel={() => setEditModalVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        width={700}
      >
        <EquipmentForm form={editForm} initialValues={currentEquipment || undefined} mode="edit" />
      </Modal>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        title={t('databases.equipment.delete')}
        content={t('databases.equipment.deleteConfirm')}
        visible={deleteModalVisible}
        onConfirm={handleDeleteEquipment}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </div>
  );
};

export default EquipmentDatabase;
