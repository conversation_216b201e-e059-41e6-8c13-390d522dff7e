import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Slider,
  Divider,
  message,
  Tabs,
  InputNumber
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  UploadOutlined,
  DownloadOutlined,
  SyncOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  fetchDataStart,
  fetchIrradianceDataSuccess,
  setCurrentIrradianceData,
  setIrradianceVisualizationOptions,
  deleteIrradianceData as deleteIrradianceDataAction,
  updateIrradianceDataSyncStatus
} from '../../store/slices/databasesSlice';
import {
  PageHeader,
  EmptyState,
  LoadingSpinner,
  FileUpload,
  SyncStatus,
  ClearDataButton,
  ConfirmDialog
} from '../../components/common';
import { IrradianceChart } from '../../components/charts';
import {
  getIrradianceList,
  getIrradianceDetail,
  deleteIrradianceData,
  uploadIrradianceCSV,
  createIrradianceData,
  updateIrradianceData
} from '../../services/irradianceService';
import { parseIrradianceCSV } from '../../utils/csvParser';
import { cacheManager } from '../../utils/dataSynchronization';
import { IrradianceData, IrradianceVisualizationOptions } from '../../types/database';

const { Option } = Select;
const { TabPane } = Tabs;
const { confirm } = Modal;

const IrradianceDatabase: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const {
    irradianceData,
    currentIrradianceData,
    irradianceVisualizationOptions,
    isLoading,
    isUploading,
    uploadProgress,
    error
  } = useAppSelector(state => state.databases);

  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string>('');
  const [uploadForm] = Form.useForm();
  const [editForm] = Form.useForm();

  const [currentEditData, setCurrentEditData] = useState<IrradianceData | null>(null);

  // 获取光照数据列表
  useEffect(() => {
    fetchIrradianceDataList();
  }, []);

  // 获取光照数据列表
  const fetchIrradianceDataList = async () => {
    try {
      dispatch(fetchDataStart());
      const response = await getIrradianceList();
      dispatch(fetchIrradianceDataSuccess(response.items));
    } catch (error) {
      // 不显示错误消息，只是设置空数据
      dispatch(fetchIrradianceDataSuccess([]));
      console.error('获取光照数据列表失败:', error);
    }
  };

  // 处理选择数据集
  const handleSelectIrradianceData = async (id: string) => {
    try {
      if (currentIrradianceData && currentIrradianceData.id === id) {
        return;
      }

      // 设置加载状态
      dispatch(fetchDataStart());

      console.log('选择数据集, ID:', id);

      // 记录当前正在查看的数据ID，以便缓存管理器可以避免删除它
      localStorage.setItem('pv_current_viewed_irradiance_id', id);
      console.log('已记录当前正在查看的数据ID:', id);

      // 获取选中的数据项
      const selectedItem = irradianceData.find(item => item.id === id);

      // 检查数据是否有效
      if (selectedItem && !isDataValid(selectedItem)) {
        console.log('选中的数据无效, ID:', id);
        message.warning(t('irradiance.invalidData', '数据无效或不完整，请删除并重新上传'));
        dispatch(setCurrentIrradianceData(null));
        return;
      }

      // 检查是否需要从服务器获取数据
      // 1. 明确标记为"仅服务器"的数据
      // 2. 本地数据不完整但服务器上有的数据
      const needServerData = selectedItem && (
        selectedItem.syncStatus === 'server-only' ||
        // 检查本地数据是否存在但不完整
        (selectedItem.dataSize > 0 && !isLocalDataComplete(id))
      );

      if (needServerData) {
        console.log('需要从服务器获取数据, ID:', id, '同步状态:', selectedItem?.syncStatus);
        message.info(t('irradiance.downloadingData', '正在从服务器下载数据...'));

        try {
          // 从服务器获取数据
          const data = await getIrradianceDetail(id);
          console.log('从服务器获取到数据详情, ID:', id, '同步状态:', data.syncStatus, '数据条数:', data.data?.length || 0);

          // 验证服务器数据的有效性
          if (!isDataValid(data)) {
            console.error('从服务器获取的数据无效');
            message.error(t('irradiance.dataInvalid', '服务器数据无效或不完整，请删除并重新上传'));

            // 删除无效的服务器数据
            try {
              await deleteIrradianceData(id);
              console.log('已删除无效的服务器数据, ID:', id);
              message.info(t('irradiance.invalidDataDeleted', '已删除无效数据'));

              // 刷新列表
              await fetchIrradianceDataList();
            } catch (deleteError) {
              console.error('删除无效的服务器数据失败:', deleteError);
            }

            dispatch(setCurrentIrradianceData(null));
            return;
          }

          // 确保数据的同步状态为"已同步"
          data.syncStatus = 'synced';
          console.log('确保数据同步状态为"已同步", ID:', id);

          // 更新当前选中的数据
          dispatch(setCurrentIrradianceData(data));

          // 更新Redux中的同步状态
          dispatch(updateIrradianceDataSyncStatus({ id, status: 'synced' }));

          // 更新本地存储和缓存中的数据同步状态
          try {
            // 1. 更新localStorage
            const key = `pv_irradiance_${id}`;
            const storedData = localStorage.getItem(key);
            if (storedData) {
              const parsedData = JSON.parse(storedData);
              parsedData.syncStatus = 'synced';
              // 如果服务器数据中有完整数据，也更新到本地存储
              if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                parsedData.data = data.data;
                parsedData.dataSize = data.data.length;
              }
              localStorage.setItem(key, JSON.stringify(parsedData));
              console.log('本地存储中的数据同步状态已更新为"已同步", ID:', id);
            } else {
              // 如果本地没有数据，则保存完整的服务器数据到本地
              localStorage.setItem(key, JSON.stringify(data));
              console.log('服务器数据已保存到本地存储, ID:', id);
            }

            // 2. 更新缓存
            try {
              // 从缓存获取数据
              const cachedData = cacheManager.getItem(id);
              if (cachedData) {
                // 确保缓存中的数据同步状态为"已同步"
                cachedData.syncStatus = 'synced';
                // 如果服务器数据中有完整数据，也更新到缓存
                if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                  cachedData.data = data.data;
                  cachedData.dataSize = data.data.length;
                }
                cacheManager.addItem(id, cachedData);
                console.log('缓存中的数据同步状态已更新为"已同步", ID:', id);
              } else {
                // 如果缓存中没有数据，则添加到缓存
                cacheManager.addItem(id, data);
                console.log('服务器数据已添加到缓存, ID:', id);
              }
            } catch (cacheError) {
              console.error('更新缓存中的数据同步状态失败:', cacheError);
            }
          } catch (storageError) {
            console.error('更新本地存储中的数据同步状态失败:', storageError);
          }

          // 刷新列表以更新同步状态
          console.log('服务器数据已下载，刷新列表');
          await fetchIrradianceDataList();

          message.success(t('irradiance.downloadSuccess', '数据下载成功'));
        } catch (serverError) {
          console.error('从服务器获取数据失败:', serverError);
          message.error(t('irradiance.downloadError', '从服务器下载数据失败'));
          dispatch(setCurrentIrradianceData(null));
        }
      } else {
        // 获取本地数据
        try {
          const data = await getIrradianceDetail(id);
          console.log('获取到本地数据详情, ID:', id, '同步状态:', data.syncStatus);

          // 验证本地数据的有效性
          if (!isDataValid(data)) {
            console.error('本地数据无效');
            message.error(t('irradiance.dataInvalid', '本地数据无效或不完整，请删除并重新上传'));

            // 删除无效的本地数据
            try {
              await deleteIrradianceData(id);
              console.log('已删除无效的本地数据, ID:', id);
              message.info(t('irradiance.invalidDataDeleted', '已删除无效数据'));

              // 刷新列表
              await fetchIrradianceDataList();
            } catch (deleteError) {
              console.error('删除无效的本地数据失败:', deleteError);
            }

            dispatch(setCurrentIrradianceData(null));
            return;
          }

          // 更新当前选中的数据
          dispatch(setCurrentIrradianceData(data));
        } catch (localError) {
          console.error('获取本地数据失败:', localError);
          message.error(t('irradiance.loadError', '加载数据失败'));
          dispatch(setCurrentIrradianceData(null));
        }
      }
    } catch (error) {
      // 显示错误消息
      message.error(t('irradiance.loadError', '加载数据失败'));
      dispatch(setCurrentIrradianceData(null));
      console.error('获取光照数据详情失败:', error);
    }
  };

  // 检查数据是否有效
  const isDataValid = (data: IrradianceData | undefined): boolean => {
    if (!data) return false;
    return data.data && Array.isArray(data.data) && data.data.length > 0;
  };

  // 检查本地数据是否完整
  const isLocalDataComplete = (id: string): boolean => {
    try {
      const key = `pv_irradiance_${id}`;
      const storedData = localStorage.getItem(key);

      if (!storedData) {
        return false;
      }

      const data = JSON.parse(storedData);
      return isDataValid(data);
    } catch (error) {
      console.error('检查本地数据完整性失败:', error);
      return false;
    }
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (id: string) => {
    console.log('删除按钮被点击，ID:', id);
    setDeleteItemId(id);
    setDeleteModalVisible(true);
    console.log('已设置删除对话框为可见，ID:', id);
  };

  // 处理确认删除
  const handleConfirmDelete = async () => {
    const id = deleteItemId;
    console.log('确认删除按钮被点击，ID:', id);

    try {
      console.log('确认删除数据，开始删除过程，ID:', id);
      console.log('当前数据列表长度:', irradianceData.length);
      console.log('当前选中的数据:', currentIrradianceData?.id);

      // 如果当前选中的数据被删除，清空当前数据
      if (currentIrradianceData && currentIrradianceData.id === id) {
        console.log('正在清空当前选中的数据');
        dispatch(setCurrentIrradianceData(null));
      }

      // 更新Redux状态
      console.log('正在更新Redux状态，删除ID:', id);
      dispatch(deleteIrradianceDataAction(id));
      console.log('Redux状态更新完成');

      // 从localStorage中删除数据
      const key = `pv_irradiance_${id}`;
      console.log('正在从localStorage中删除数据，键名:', key);
      localStorage.removeItem(key);

      // 检查是否删除成功
      const checkItem = localStorage.getItem(key);
      console.log('检查删除后的数据是否存在:', checkItem ? '仍然存在' : '已删除');

      if (checkItem) {
        console.log('首次删除失败，尝试再次删除');
        localStorage.removeItem(key);
      }

      // 调用删除服务
      console.log('正在调用删除服务');
      await deleteIrradianceData(id);
      console.log('删除服务调用完成');

      message.success(t('irradiance.deleteSuccess'));

      // 刷新数据列表
      console.log('正在刷新数据列表');
      await fetchIrradianceDataList();
      console.log('数据列表刷新完成，当前列表长度:', irradianceData.length);

      // 关闭对话框
      setDeleteModalVisible(false);
    } catch (error) {
      message.error(t('irradiance.deleteError'));
      console.error('删除光照数据失败:', error);
    }
  };

  // 处理取消删除
  const handleCancelDelete = () => {
    console.log('取消删除按钮被点击');
    setDeleteModalVisible(false);
  };

  // 处理上传CSV文件
  const handleUploadCSV = async (file: File) => {
    try {
      console.log('开始处理上传的CSV文件:', file.name, '大小:', file.size, 'bytes');

      // 解析CSV文件
      const parseResult = await parseIrradianceCSV(file);
      console.log('CSV解析成功，城市:', parseResult.cityName, '数据条数:', parseResult.data.length);

      // 设置表单数据
      uploadForm.setFieldsValue({
        file: file, // 保存文件对象，但不在表单中显示
        name: `${parseResult.cityName}辐照度数据`,
        location: parseResult.cityName,
        latitude: parseResult.latitude,
        longitude: parseResult.longitude,
        year: parseResult.data[0]?.year || new Date().getFullYear(),
      });

      console.log('表单数据已设置，准备显示上传表单');

      // 打开上传表单
      setUploadModalVisible(true);
    } catch (error) {
      message.error(t('irradiance.parseError'));
      console.error('解析CSV文件失败:', error);
    }
  };

  // 处理提交上传表单
  const handleUploadFormSubmit = async () => {
    try {
      const values = await uploadForm.validateFields();
      console.log('表单验证通过，准备处理上传:', values);

      // 获取文件对象
      const file = values.file;
      if (!file) {
        throw new Error('文件对象丢失');
      }

      console.log('开始解析CSV文件:', file.name);
      // 解析CSV文件
      const parseResult = await parseIrradianceCSV(file);
      console.log('CSV解析成功，数据条数:', parseResult.data.length);

      // 创建新的光照数据记录
      const newIrradianceData = {
        name: values.name,
        location: values.location,
        latitude: values.latitude,
        longitude: values.longitude,
        year: values.year,
        dataSize: parseResult.data.length,
        data: parseResult.data,
        syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
      };

      console.log('准备创建光照数据:', newIrradianceData.name);
      // 创建光照数据
      const createdData = await createIrradianceData(newIrradianceData);
      console.log('光照数据创建成功，ID:', createdData.id);

      // 更新Redux store
      console.log('更新Redux store，当前数据条数:', irradianceData.length);
      await fetchIrradianceDataList(); // 直接刷新列表，而不是手动更新

      // 关闭上传表单
      setUploadModalVisible(false);
      uploadForm.resetFields();

      message.success(t('irradiance.uploadSuccess'));
    } catch (error) {
      message.error(t('irradiance.uploadError'));
      console.error('上传CSV文件失败:', error);
    }
  };

  // 处理编辑数据集
  const handleEditIrradianceData = (record: IrradianceData) => {
    setCurrentEditData(record);
    editForm.setFieldsValue({
      name: record.name,
      location: record.location,
      latitude: record.latitude,
      longitude: record.longitude,
      year: record.year,
    });
    setEditModalVisible(true);
  };

  // 处理提交编辑表单
  const handleEditFormSubmit = async () => {
    try {
      const values = await editForm.validateFields();

      if (!currentEditData) {
        return;
      }

      // 更新数据
      const updatedData = {
        ...currentEditData,
        name: values.name,
        location: values.location,
        latitude: values.latitude,
        longitude: values.longitude,
        year: values.year,
      };

      // 调用更新服务
      await updateIrradianceData(currentEditData.id, updatedData);

      // 刷新数据列表
      fetchIrradianceDataList();

      // 关闭编辑表单
      setEditModalVisible(false);
      editForm.resetFields();
      setCurrentEditData(null);

      message.success(t('irradiance.updateSuccess'));
    } catch (error) {
      message.error(t('irradiance.updateError'));
      console.error('更新光照数据失败:', error);
    }
  };

  // 处理替换数据集
  const handleReplaceIrradianceData = (id: string) => {
    // 创建一个隐藏的文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.csv';
    fileInput.style.display = 'none';

    // 当用户选择文件后处理
    fileInput.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          // 解析CSV文件
          const parseResult = await parseIrradianceCSV(file);

          // 获取当前数据
          const currentData = irradianceData.find(item => item.id === id);
          if (!currentData) {
            message.error(t('irradiance.dataNotFound'));
            return;
          }

          // 更新数据
          const updatedData = {
            ...currentData,
            dataSize: parseResult.data.length,
            data: parseResult.data,
            updatedAt: new Date().toISOString(),
          };

          // 调用更新服务
          await updateIrradianceData(id, updatedData);

          // 刷新数据列表
          fetchIrradianceDataList();

          message.success(t('irradiance.replaceSuccess'));
        } catch (error) {
          message.error(t('irradiance.parseError'));
          console.error('解析CSV文件失败:', error);
        }
      }
    };

    // 触发文件选择对话框
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  };

  // 处理可视化选项变更
  const handleVisualizationOptionsChange = (options: Partial<IrradianceVisualizationOptions>) => {
    dispatch(setIrradianceVisualizationOptions(options));
  };

  // 表格列定义
  const columns = [
    {
      title: t('irradiance.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('irradiance.location'),
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: t('irradiance.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => new Date(text).toLocaleDateString(),
    },
    {
      title: t('irradiance.year'),
      dataIndex: 'year',
      key: 'year',
    },
    {
      title: t('irradiance.dataSize'),
      dataIndex: 'dataSize',
      key: 'dataSize',
      render: (text: number) => `${text}条记录`,
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status: 'synced' | 'local-only' | 'server-only' | 'invalid', record: IrradianceData) => {
        // 检查数据是否有效
        const isValid = isDataValid(record);
        return <SyncStatus status={status} isValid={isValid} />;
      },
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: IrradianceData) => {
        // 检查数据是否有效
        const isValid = isDataValid(record);

        // 如果数据无效，只显示删除按钮
        if (!isValid) {
          return (
            <Space size="small">
              <Button
                danger
                icon={<DeleteOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  showDeleteConfirm(record.id);
                }}
              />
            </Space>
          );
        }

        // 根据同步状态显示不同的操作按钮
        if (record.syncStatus === 'server-only') {
          // 服务器数据只显示下载按钮
          return (
            <Space size="small">
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  handleSelectIrradianceData(record.id); // 直接调用选择函数，会触发下载
                }}
              />
              <Button
                danger
                icon={<DeleteOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  showDeleteConfirm(record.id);
                }}
              />
            </Space>
          );
        } else {
          // 本地数据或已同步数据显示编辑、替换和删除按钮
          return (
            <Space size="small">
              <Button
                type="primary"
                icon={<EditOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  handleEditIrradianceData(record);
                }}
              />
              <Button
                icon={<UploadOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  handleReplaceIrradianceData(record.id);
                }}
              />
              <Button
                danger
                icon={<DeleteOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  showDeleteConfirm(record.id);
                }}
              />
            </Space>
          );
        }
      },
    },
  ];

  // 渲染可视化选项
  const renderVisualizationOptions = () => {
    const { metric, viewMode, month, day } = irradianceVisualizationOptions;

    return (
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '16px', marginBottom: 16 }}>
          {/* 数据指标 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('irradiance.metric')}:</span>
            <Select
              value={metric}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ metric: value })}
            >
              <Option value="G_Gh">{t('irradiance.globalHorizontalIrradiance')}</Option>
              <Option value="Ta">{t('irradiance.temperature')}</Option>
              <Option value="Sd">{t('irradiance.directTime')}</Option>
            </Select>
          </div>

          {/* 查看方式 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('irradiance.viewMode')}:</span>
            <Select
              value={viewMode}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ viewMode: value })}
            >
              <Option value="hourly">{t('irradiance.hourly')}</Option>
              <Option value="daily">{t('irradiance.daily')}</Option>
              <Option value="monthly">{t('irradiance.monthly')}</Option>
            </Select>
          </div>

          {/* 月份选择 - 仅在非月视图时显示 */}
          {viewMode !== 'monthly' && (
            <div>
              <span style={{ marginRight: 8 }}>{t('irradiance.month')}:</span>
              <Select
                value={month !== undefined ? month : 1}
                style={{ width: 80 }}
                onChange={(value) => handleVisualizationOptionsChange({ month: value })}
              >
                <Option key={0} value={0}>{t('irradiance.fullYear')}</Option>
                {Array.from({ length: 12 }, (_, i) => i + 1).map(m => (
                  <Option key={m} value={m}>{m}{t('irradiance.monthUnit')}</Option>
                ))}
              </Select>
            </div>
          )}
        </div>

        {/* 日期滑块 - 仅在小时视图时显示 */}
        {viewMode === 'hourly' && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <span style={{ marginRight: 8, minWidth: '40px' }}>{t('irradiance.day')}:</span>
            <Slider
              min={1}
              max={31}
              value={day || 1}
              style={{ flex: 1, marginRight: 8 }}
              onChange={(value) => handleVisualizationOptionsChange({ day: value })}
            />
            <span style={{ minWidth: '40px', textAlign: 'right' }}>{day || 1}{t('irradiance.dayUnit')}</span>
          </div>
        )}
      </div>
    );
  };

  // 渲染数据统计
  const renderDataStatistics = () => {
    if (!currentIrradianceData) return null;

    return (
      <div style={{ marginTop: 24 }}>
        <Divider orientation="left">{t('irradiance.dataStatistics')}</Divider>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
          <Card title={t('irradiance.name')} style={{ width: 300 }}>
            {currentIrradianceData.name}
          </Card>
          <Card title={t('irradiance.location')} style={{ width: 300 }}>
            {currentIrradianceData.location}
          </Card>
          <Card title={t('irradiance.coordinates')} style={{ width: 300 }}>
            {currentIrradianceData.latitude}, {currentIrradianceData.longitude}
          </Card>
        </div>
      </div>
    );
  };

  return (
    <div>
      <PageHeader
        title={t('irradiance.title')}
        extra={[
          <Button
            key="upload"
            type="primary"
            icon={<UploadOutlined />}
            onClick={() => {
              // 创建一个隐藏的文件输入元素
              const fileInput = document.createElement('input');
              fileInput.type = 'file';
              fileInput.accept = '.csv';
              fileInput.style.display = 'none';

              // 当用户选择文件后处理
              fileInput.onchange = async (e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                if (file) {
                  await handleUploadCSV(file);
                }
              };

              // 触发文件选择对话框
              document.body.appendChild(fileInput);
              fileInput.click();
              document.body.removeChild(fileInput);
            }}
          >
            {t('irradiance.upload')}
          </Button>,
        ]}
      />



      <Card title={t('irradiance.datasetList')} style={{ marginBottom: 16 }}>
        <Table
          rowKey="id"
          columns={columns}
          dataSource={irradianceData}
          loading={isLoading}
          pagination={{ pageSize: 10 }}
          onRow={(record) => ({
            onClick: () => handleSelectIrradianceData(record.id),
            style: {
              cursor: 'pointer',
              background: currentIrradianceData?.id === record.id ? '#f0f7ff' : undefined
            }
          })}
        />
      </Card>


      <Card title={t('irradiance.dataVisualization')}>
        {currentIrradianceData ? (
          <>
            {renderVisualizationOptions()}
            <IrradianceChart
              data={currentIrradianceData}
              options={irradianceVisualizationOptions}
            />
            {renderDataStatistics()}
          </>
        ) : (
          <EmptyState
            title={t('irradiance.noDataSelected')}
            description={t('irradiance.selectDataPrompt')}
          />
        )}
      </Card>

      {/* 上传表单模态框 */}
      <Modal
        title={t('irradiance.dataInfo')}
        open={uploadModalVisible}
        onOk={handleUploadFormSubmit}
        onCancel={() => {
          setUploadModalVisible(false);
          uploadForm.resetFields();
        }}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <Form
          form={uploadForm}
          layout="vertical"
        >
          {/* 文件已经选择，隐藏文件字段但保留值 */}
          <Form.Item
            name="file"
            hidden
          />
          <Form.Item
            name="name"
            label={t('irradiance.name')}
            rules={[{ required: true, message: t('irradiance.nameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="location"
            label={t('irradiance.location')}
            rules={[{ required: true, message: t('irradiance.locationRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="latitude"
            label={t('irradiance.latitude')}
            rules={[{ required: true, message: t('irradiance.latitudeRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="longitude"
            label={t('irradiance.longitude')}
            rules={[{ required: true, message: t('irradiance.longitudeRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="year"
            label={t('irradiance.year')}
            rules={[{ required: true, message: t('irradiance.yearRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑表单模态框 */}
      <Modal
        title={t('irradiance.dataInfo')}
        open={editModalVisible}
        onOk={handleEditFormSubmit}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setCurrentEditData(null);
        }}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <Form
          form={editForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('irradiance.name')}
            rules={[{ required: true, message: t('irradiance.nameRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="location"
            label={t('irradiance.location')}
            rules={[{ required: true, message: t('irradiance.locationRequired') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="latitude"
            label={t('irradiance.latitude')}
            rules={[{ required: true, message: t('irradiance.latitudeRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="longitude"
            label={t('irradiance.longitude')}
            rules={[{ required: true, message: t('irradiance.longitudeRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="year"
            label={t('irradiance.year')}
            rules={[{ required: true, message: t('irradiance.yearRequired') }]}
          >
            <Input type="number" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        title={t('irradiance.confirmDelete')}
        content={t('irradiance.deleteWarning')}
        visible={deleteModalVisible}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </div>
  );
};

export default IrradianceDatabase;
