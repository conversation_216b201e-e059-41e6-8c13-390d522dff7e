import React, { useEffect, useState } from 'react';
import { Table, Button, Input, Select, Card, Tag, Space, message } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, BarChartOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { fetchProjectsStart, fetchProjectsSuccess, deleteProject, setCurrentProject } from '../../store/slices/projectsSlice';
import { PageHeader, EmptyState, LoadingSpinner, ClearDataButton, ConfirmDialog, SyncStatus } from '../../components/common';
import { formatDate, formatCurrency } from '../../utils';
import { Project, ProjectStatus } from '../../types';
import { CurrencyType } from '../../types/settings';
import { getProjectList, deleteProject as deleteProjectSync, syncProjectToServer } from '../../services/projectSyncService';

const { Option } = Select;

const ProjectList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { projects, isLoading } = useAppSelector((state) => state.projects);
  const { currency } = useAppSelector((state) => state.settings);

  // 搜索和筛选状态
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<ProjectStatus | 'all'>('all');

  // 删除确认对话框状态
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  // 获取项目列表
  useEffect(() => {
    console.log('项目列表页面加载，当前项目数量:', projects.length);
    fetchProjects();
  }, []);

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      console.log('开始获取项目列表...', new Date().toISOString());
      dispatch(fetchProjectsStart());

      // 使用同步服务获取项目列表
      console.log('调用getProjectList...');
      const response = await getProjectList();

      console.log('获取到项目数据条数:', response.items ? response.items.length : '未知');

      if (response.items && Array.isArray(response.items)) {
        console.log('项目数据是数组，长度:', response.items.length);

        // 检查第一个项目的结构
        if (response.items.length > 0) {
          const firstProject = response.items[0];
          console.log('第一个项目ID:', firstProject.id);
          console.log('第一个项目名称:', firstProject.name);
          console.log('第一个项目状态:', firstProject.status);
          console.log('第一个项目同步状态:', firstProject.syncStatus);

          // 检查分析结果
          if (firstProject.analysisResults) {
            console.log('第一个项目有分析结果');
            console.log('小时数据点数:', firstProject.analysisResults.hourlyData ? firstProject.analysisResults.hourlyData.length : '无小时数据');

            // 检查第一个小时数据点
            if (firstProject.analysisResults.hourlyData && firstProject.analysisResults.hourlyData.length > 0) {
              const firstHourData = firstProject.analysisResults.hourlyData[0];
              console.log('第一个小时数据点:', JSON.stringify(firstHourData));
            }
          } else {
            console.log('第一个项目没有分析结果');
          }
        }
      } else {
        console.error('项目数据不是数组:', response);
      }

      dispatch(fetchProjectsSuccess(response.items || []));
    } catch (error) {
      console.error('获取项目列表失败:', error);
      console.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
      message.error(t('projects.fetchFailed'));
      dispatch(fetchProjectsSuccess([]));
    }
  };

  // 处理新建项目
  const handleNewProject = () => {
    navigate('/projects/new');
  };

  // 处理编辑项目
  const handleEditProject = (id: string) => {
    // 先设置当前项目，然后导航到新建项目页面
    const projectToEdit = projects.find(project => project.id === id);
    if (projectToEdit) {
      dispatch(setCurrentProject(projectToEdit));
      navigate('/projects/new');
    } else {
      message.error(t('projects.notFound'));
    }
  };

  // 处理查看项目分析结果
  const handleViewAnalysis = (id: string) => {
    navigate(`/projects/analysis/${id}`);
  };

  // 处理删除项目
  const handleDeleteProject = (id: string) => {
    // 显示确认对话框
    setProjectToDelete(id);
    setDeleteConfirmVisible(true);
  };

  // 确认删除项目
  const confirmDeleteProject = async () => {
    if (projectToDelete) {
      try {
        // 使用同步服务删除项目
        await deleteProjectSync(projectToDelete);

        // 更新Redux状态
        dispatch(deleteProject(projectToDelete));

        message.success(t('projects.deleted'));
      } catch (error) {
        console.error('删除项目失败:', error);
        message.error(t('projects.deleteFailed'));
      } finally {
        setDeleteConfirmVisible(false);
        setProjectToDelete(null);
      }
    }
  };

  // 取消删除项目
  const cancelDeleteProject = () => {
    setDeleteConfirmVisible(false);
    setProjectToDelete(null);
  };

  // 同步项目到服务器
  const handleSyncProject = async (id: string) => {
    try {
      message.loading({ content: t('projects.syncing'), key: 'sync', duration: 0 });
      console.log(`开始同步项目 ${id} 到服务器...`);

      // 使用同步服务同步项目到服务器
      try {
        const syncedProject = await syncProjectToServer(id);
        console.log('同步结果:', syncedProject);

        // 更新Redux状态
        dispatch(fetchProjectsStart());
        await fetchProjects();

        message.success({ content: t('projects.syncSuccess'), key: 'sync' });
      } catch (syncError) {
        console.error('同步项目到服务器失败:', syncError);
        message.error({ content: t('projects.syncFailed'), key: 'sync' });

        // 刷新项目列表以显示最新状态
        dispatch(fetchProjectsStart());
        await fetchProjects();
      }
    } catch (error) {
      console.error('同步项目处理失败:', error);
      message.error({ content: t('projects.syncFailed'), key: 'sync' });
    }
  };

  // 过滤项目
  const filteredProjects = projects.filter((project) => {
    const matchesSearch = project.name.toLowerCase().includes(searchText.toLowerCase()) ||
      project.location.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // 表格列定义
  const columns = [
    {
      title: t('projects.name'),
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Project, b: Project) => a.name.localeCompare(b.name),
    },
    {
      title: t('projects.location'),
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: t('projects.capacity'),
      dataIndex: 'capacity',
      key: 'capacity',
      render: (capacity: number) => `${capacity} kW`,
      sorter: (a: Project, b: Project) => a.capacity - b.capacity,
    },
    {
      title: t('projects.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: ProjectStatus) => {
        let color = 'default';
        if (status === 'completed') color = 'success';
        if (status === 'analyzing') color = 'processing';
        if (status === 'draft') color = 'warning';
        return <Tag color={color}>{t(`projects.${status}`)}</Tag>;
      },
    },
    {
      title: t('common.syncStatus'),
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (syncStatus: 'synced' | 'local-only' | 'server-only' | 'invalid' | undefined) => <SyncStatus status={syncStatus} />,
    },
    {
      title: t('projects.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
      sorter: (a: Project, b: Project) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: t('analysis.totalBenefit'),
      key: 'totalBenefit',
      render: (_: any, record: Project) => {
        // 添加更多的安全检查
        return (record.analysisResults && record.analysisResults.yearlyData &&
                typeof record.analysisResults.yearlyData.totalBenefit === 'number')
          ? formatCurrency(record.analysisResults.yearlyData.totalBenefit, currency as CurrencyType)
          : '-';
      },
      sorter: (a: Project, b: Project) => {
        // 添加更多的安全检查
        const aValue = (a.analysisResults && a.analysisResults.yearlyData &&
                       typeof a.analysisResults.yearlyData.totalBenefit === 'number')
          ? a.analysisResults.yearlyData.totalBenefit : 0;
        const bValue = (b.analysisResults && b.analysisResults.yearlyData &&
                       typeof b.analysisResults.yearlyData.totalBenefit === 'number')
          ? b.analysisResults.yearlyData.totalBenefit : 0;
        return aValue - bValue;
      },
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: Project) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditProject(record.id)}
          />
          {record.status === 'completed' && (
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => handleViewAnalysis(record.id)}
            />
          )}
          {record.syncStatus === 'local-only' && (
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleSyncProject(record.id)}
              title={t('projects.syncToServer')}
            />
          )}
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteProject(record.id)}
          />
        </Space>
      ),
    },
  ];

  if (isLoading) {
    return <LoadingSpinner fullScreen />;
  }

  // 调试信息已移除

  return (
    <div>
      <PageHeader
        title={t('projects.title')}
        extra={
          <Space>
            <ClearDataButton />
            <Button type="primary" icon={<PlusOutlined />} onClick={handleNewProject}>
              {t('projects.new')}
            </Button>
          </Space>
        }
      />


      <Card style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
          <Input
            placeholder={t('projects.search')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '256px' }}
          />
          <Select
            placeholder={t('projects.filter')}
            value={statusFilter}
            onChange={(value) => setStatusFilter(value)}
            style={{ width: '160px' }}
          >
            <Option value="all">{t('common.all')}</Option>
            <Option value="draft">{t('projects.draft')}</Option>
            <Option value="analyzing">{t('projects.analyzing')}</Option>
            <Option value="completed">{t('projects.completed')}</Option>
          </Select>
        </div>
      </Card>

      {filteredProjects.length === 0 ? (
        <EmptyState
          title={t('common.noData')}
          description={t('projects.noProjects')}
          actionText={t('projects.new')}
          onAction={handleNewProject}
        />
      ) : (
        <Table
          dataSource={filteredProjects}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      )}

      {/* 删除确认对话框 */}
      <ConfirmDialog
        visible={deleteConfirmVisible}
        title={t('projects.confirmDelete')}
        content={t('projects.deleteConfirmation')}
        onConfirm={confirmDeleteProject}
        onCancel={cancelDeleteProject}
      />
    </div>
  );
};

export default ProjectList;
