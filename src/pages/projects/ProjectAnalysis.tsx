import React, { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tabs,
  Button,
  Descriptions,
  Typography,
  Divider,
  Tag,
  Space,
  message,
  Modal,
  Progress,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  PieChartOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  DownloadOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  FilePdfOutlined
} from '@ant-design/icons';
import { useAppSelector, useAppDispatch } from '../../store';
import { updateProject as updateReduxProject } from '../../store/slices/projectsSlice';
import { fetchDataStart, fetchEquipmentSuccess } from '../../store/slices/databasesSlice';
import { PageHeader, LoadingSpinner } from '../../components/common';
import { formatCurrency, formatNumber } from '../../utils';
import { exportProjectHourlyData } from '../../utils/exportUtils';
import { ProjectData, ProjectYearlyData } from '../../types/projectData';
import { getProject, updateProject } from '../../services/projectSyncService';
import { get } from '../../services/api';
import { special } from '../../config/settings';
import { analyzeProject } from '../../services/projectAnalysisService';
import { dataSyncManager } from '../../services/dataSyncManager';
import DataConflictDialog from '../../components/common/DataConflictDialog';
import { getEquipmentList } from '../../services/equipmentService';
import { generateProjectReport } from '../../services/reportService';
import ProjectOverviewTab from '../../components/analysis/ProjectOverviewTab';
import PVAnalysisTab from '../../components/analysis/PVAnalysisTab';
import StorageAnalysisTab from '../../components/analysis/StorageAnalysisTab';
import ProjectReportModal from '../../components/analysis/ProjectReportModal';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 项目分析结果页面
 */
const ProjectAnalysis: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // 项目数据
  const [project, setProject] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isConfigChanged, setIsConfigChanged] = useState<boolean>(false);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [analyzeProgress, setAnalyzeProgress] = useState<number>(0);
  const [analyzeModalVisible, setAnalyzeModalVisible] = useState<boolean>(false);
  const [reportModalVisible, setReportModalVisible] = useState<boolean>(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState<boolean>(false);

  // 数据冲突解决相关状态
  const [conflictDialogVisible, setConflictDialogVisible] = useState<boolean>(false);
  const [conflictLocalData, setConflictLocalData] = useState<ProjectData | null>(null);
  const [conflictServerData, setConflictServerData] = useState<ProjectData | null>(null);
  const [isResolvingConflict, setIsResolvingConflict] = useState<boolean>(false);

  // 从Redux获取项目列表和设备数据
  const { projects } = useAppSelector((state) => state.projects);
  const { equipment } = useAppSelector((state) => state.databases);

  // 加载设备数据
  useEffect(() => {
    const fetchEquipmentData = async () => {
      try {
        // 如果Redux store中没有设备数据，则从服务器获取
        if (equipment.length === 0) {
          console.log('ProjectAnalysis: Redux store中没有设备数据，从服务器获取');
          dispatch(fetchDataStart());
          const response = await getEquipmentList();
          dispatch(fetchEquipmentSuccess(response.items));
          console.log('ProjectAnalysis: 成功获取设备列表，数据条数:', response.items.length);
        } else {
          console.log('ProjectAnalysis: Redux store中已有设备数据，数据条数:', equipment.length);
        }
      } catch (error) {
        console.error('ProjectAnalysis: 获取设备列表失败:', error);
        message.error(t('common.fetchDataFailed'));
      }
    };

    fetchEquipmentData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 加载项目数据
  useEffect(() => {
    const loadProject = async () => {
      if (!id) return;

      try {
        setLoading(true);
        console.log('开始加载项目数据，项目ID:', id);

        // 先从Redux中查找
        const projectFromRedux = projects.find(p => p.id === id);
        console.log('从Redux中查找项目:', projectFromRedux ? '找到' : '未找到');

        // 检查Redux中的项目是否有小时数据
        const hasHourlyData = projectFromRedux &&
                             projectFromRedux.analysisResults?.hourlyData &&
                             projectFromRedux.analysisResults.hourlyData.length > 0;

        console.log('Redux中的项目是否有小时数据:', hasHourlyData ? '是' : '否');

        if (projectFromRedux && projectFromRedux.analysisResults?.analysisCompleted && hasHourlyData) {
          console.log('使用Redux中的项目数据，分析已完成且有小时数据');
          console.log('项目数据概览:', {
            id: projectFromRedux.id,
            name: projectFromRedux.name,
            pvModules: projectFromRedux.pvModules.length,
            energyStorage: projectFromRedux.energyStorage.length,
            inverters: projectFromRedux.inverters.length,
            hourlyDataPoints: projectFromRedux.analysisResults?.hourlyData?.length || 0
          });
          setProject(projectFromRedux as ProjectData);
        } else {
          console.log('从服务器获取项目数据');
          // 如果Redux中没有或分析未完成或没有小时数据，从服务器获取
          const projectData = await getProject(id);
          console.log('从服务器获取的项目数据:', projectData ? '成功' : '失败');

          if (!projectData) {
            console.error('未找到项目数据');
            throw new Error(t('projects.notFound'));
          }

          if (!projectData.analysisResults?.analysisCompleted) {
            console.error('项目分析未完成');
            throw new Error(t('analysis.notCompleted'));
          }

          console.log('项目数据概览:', {
            id: projectData.id,
            name: projectData.name,
            pvModules: projectData.pvModules.length,
            energyStorage: projectData.energyStorage.length,
            inverters: projectData.inverters.length,
            hourlyDataPoints: projectData.analysisResults?.hourlyData?.length || 0
          });

          // 检查hourlyData中的数据
          if (projectData.analysisResults?.hourlyData?.length > 0) {
            const firstHour = projectData.analysisResults.hourlyData[0];
            const middleHour = projectData.analysisResults.hourlyData[Math.floor(projectData.analysisResults.hourlyData.length / 2)];
            const lastHour = projectData.analysisResults.hourlyData[projectData.analysisResults.hourlyData.length - 1];

            console.log('小时数据样本 - 第一个小时:', firstHour);
            console.log('小时数据样本 - 中间小时:', middleHour);
            console.log('小时数据样本 - 最后一个小时:', lastHour);
          } else {
            console.warn('项目没有小时数据，尝试直接从服务器文件加载');

            // 如果没有小时数据，尝试直接从服务器文件加载
            try {
              const response = await get<ApiResponse<any>>(`/projects/${id}/hourly-data`);
              if (response.data.success && response.data.data) {
                console.log('成功从服务器获取小时数据');
                console.log('获取到的小时数据点数:', response.data.data.length);

                // 更新项目数据中的小时数据
                if (projectData.analysisResults) {
                  projectData.analysisResults.hourlyData = response.data.data;
                  console.log('已更新项目数据中的小时数据');
                }
              }
            } catch (hourlyDataError) {
              console.error('获取小时数据失败:', hourlyDataError);
            }
          }

          setProject(projectData as ProjectData);

          // 更新Redux中的项目数据
          if (projectFromRedux) {
            dispatch(updateReduxProject(projectData as ProjectData));
            console.log('已更新Redux中的项目数据');
          }
        }
      } catch (err) {
        console.error('加载项目分析结果失败:', err);
        setError((err as Error).message);
        message.error((err as Error).message);
      } finally {
        setLoading(false);
      }
    };

    loadProject();
  }, [id, projects, t]);

  // 处理数据同步
  const handleDataSync = async (updatedProject: ProjectData, immediate: boolean = false) => {
    if (!updatedProject.id) return updatedProject;

    try {
      let syncResult;

      if (immediate) {
        // 立即同步
        syncResult = await dataSyncManager.syncImmediately(updatedProject.id, updatedProject);
      } else {
        // 延迟同步
        dataSyncManager.syncWithDelay(updatedProject.id, updatedProject);
        return updatedProject;
      }

      if (syncResult.success) {
        console.log('数据同步成功');
        return syncResult.data!;
      } else if (syncResult.conflict) {
        // 处理数据冲突
        console.warn('检测到数据冲突，显示冲突解决对话框');
        setConflictLocalData(updatedProject);
        // 这里需要获取服务器数据，暂时使用当前数据
        setConflictServerData(project);
        setConflictDialogVisible(true);
        return updatedProject;
      } else {
        throw new Error(syncResult.error || '数据同步失败');
      }
    } catch (error) {
      console.error('数据同步失败:', error);
      message.error('数据同步失败: ' + (error as Error).message);
      return updatedProject;
    }
  };

  // 处理数据冲突解决
  const handleConflictResolve = async (resolution: 'local' | 'server' | 'merge') => {
    if (!conflictLocalData || !conflictServerData || !id) return;

    try {
      setIsResolvingConflict(true);

      const result = await dataSyncManager.resolveConflict(
        id,
        conflictLocalData,
        conflictServerData,
        resolution
      );

      if (result.success && result.data) {
        setProject(result.data);
        dispatch(updateReduxProject(result.data));
        message.success('数据冲突已解决');
        setConflictDialogVisible(false);
        setConflictLocalData(null);
        setConflictServerData(null);
      } else {
        throw new Error(result.error || '解决数据冲突失败');
      }
    } catch (error) {
      console.error('解决数据冲突失败:', error);
      message.error('解决数据冲突失败: ' + (error as Error).message);
    } finally {
      setIsResolvingConflict(false);
    }
  };

  // 处理返回项目列表
  const handleBackToList = () => {
    navigate('/projects');
  };

  // 处理导出项目小时数据
  const handleExportHourlyData = () => {
    if (!project) return;

    try {
      exportProjectHourlyData(project);
      message.success(t('projects.exportSuccess'));
    } catch (error) {
      console.error('导出项目小时数据失败:', error);
      message.error(t('projects.exportFailed'));
    }
  };

  // 如果正在加载，显示加载中
  if (loading) {
    return <LoadingSpinner fullScreen />;
  }

  // 如果出错，显示错误信息
  if (error || !project || !project.analysisResults) {
    return (
      <div>
        <PageHeader
          title={t('analysis.title')}
          extra={
            <Button icon={<ArrowLeftOutlined />} onClick={handleBackToList}>
              {t('common.backToList')}
            </Button>
          }
        />
        <Card>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <WarningOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />
            <Title level={3}>{t('common.error')}</Title>
            <Paragraph>{error || t('analysis.noData')}</Paragraph>
            <Button type="primary" onClick={handleBackToList}>
              {t('common.backToList')}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // 项目基本信息
  const { basicInfo, analysisResults } = project;

  // 计算年度数据
  const hourlyData = analysisResults.hourlyData || [];

  // 计算总发电量
  const totalPvGeneration = hourlyData.reduce((sum, hour) => {
    const hourGeneration = Object.values(hour.pvGeneration || {}).reduce((s, v) => s + (v as number), 0);
    return sum + hourGeneration;
  }, 0);

  // 计算总用电量
  const totalElectricityConsumption = hourlyData.reduce((sum, hour) =>
    sum + (hour.electricityConsumption || 0), 0);

  // 计算总上网电量
  const totalGridExport = hourlyData.reduce((sum, hour) =>
    sum + (hour.gridExport || 0), 0);

  // 计算总购电量
  const gridImport = hourlyData.reduce((sum, hour) =>
    sum + (hour.gridImport || 0), 0);

  // 计算总收益 (假设电价为25日元/kWh，上网电价为17日元/kWh)
  const electricityPrice = 25; // 日元/kWh
  const gridFeedInPrice = 17; // 日元/kWh

  const selfUseAmount = Math.min(totalPvGeneration, totalElectricityConsumption);
  const selfUseBenefit = selfUseAmount * electricityPrice;
  const gridExportBenefit = totalGridExport * gridFeedInPrice;
  const totalBenefit = selfUseBenefit + gridExportBenefit;

  // 计算总投资成本
  const pvModulesCost = project.pvModules.reduce((sum, module) =>
    sum + module.price * module.quantity, 0);

  const energyStorageCost = project.energyStorage.reduce((sum, storage) =>
    sum + storage.price * storage.quantity, 0);

  const invertersCost = project.inverters.reduce((sum, inverter) =>
    sum + inverter.price * inverter.quantity, 0);

  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) =>
    sum + item.price, 0);

  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;

  // 计算投资回报率和回收期
  const roi = totalInvestment > 0 ? (totalBenefit / totalInvestment) * 100 : 0;
  const paybackPeriod = totalBenefit > 0 ? totalInvestment / totalBenefit : 0;

  // 现金流表格数据
  const cashFlowColumns = [
    {
      title: t('analysis.year'),
      dataIndex: 'year',
      key: 'year',
    },
    {
      title: t('analysis.investment'),
      dataIndex: 'investment',
      key: 'investment',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: t('analysis.annualBenefit'),
      dataIndex: 'annualBenefit',
      key: 'annualBenefit',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: t('analysis.cumulativeCashFlow'),
      dataIndex: 'cumulativeCashFlow',
      key: 'cumulativeCashFlow',
      render: (value: number) => (
        <span style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
          {formatCurrency(value)}
        </span>
      ),
    },
  ];

  // 生成20年现金流数据
  // 使用循环方式创建数组，避免在创建过程中访问未完成的数组
  const cashFlowData = [];
  let cumulativeCashFlow = 0;

  for (let i = 0; i < 20; i++) {
    const year = i + 1;
    const investment = year === 1 ? totalInvestment : 0;
    const annualBenefit = totalBenefit;

    // 更新累计现金流
    cumulativeCashFlow = cumulativeCashFlow - investment + annualBenefit;

    cashFlowData.push({
      key: year,
      year,
      investment,
      annualBenefit,
      cumulativeCashFlow,
    });
  }

  // 投资建议
  const getInvestmentAdvice = (roi: number, paybackPeriod: number): React.ReactNode => {
    if (roi >= 15 && paybackPeriod <= 7) {
      return (
        <div>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            {t('analysis.advice.excellent')}
          </Paragraph>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            {t('analysis.advice.highROI', { roi: formatNumber(roi, 1) })}
          </Paragraph>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            {t('analysis.advice.quickPayback', { years: formatNumber(paybackPeriod, 1) })}
          </Paragraph>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            {t('analysis.advice.recommendProceed')}
          </Paragraph>
        </div>
      );
    } else if (roi >= 8 && paybackPeriod <= 12) {
      return (
        <div>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            {t('analysis.advice.good')}
          </Paragraph>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            {t('analysis.advice.decentROI', { roi: formatNumber(roi, 1) })}
          </Paragraph>
          <Paragraph>
            <CheckCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            {t('analysis.advice.reasonablePayback', { years: formatNumber(paybackPeriod, 1) })}
          </Paragraph>
          <Paragraph>
            <InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
            {t('analysis.advice.considerProceed')}
          </Paragraph>
        </div>
      );
    } else {
      return (
        <div>
          <Paragraph>
            <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
            {t('analysis.advice.challenging')}
          </Paragraph>
          <Paragraph>
            <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
            {t('analysis.advice.lowROI', { roi: formatNumber(roi, 1) })}
          </Paragraph>
          <Paragraph>
            <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
            {t('analysis.advice.longPayback', { years: formatNumber(paybackPeriod, 1) })}
          </Paragraph>
          <Paragraph>
            <InfoCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
            {t('analysis.advice.reconsiderOptions')}
          </Paragraph>
        </div>
      );
    }
  };

  // 处理添加光伏组件
  const handleAddPVModule = () => {
    navigate(`/projects/edit/${id}?step=5`);
  };

  // 处理编辑光伏组件
  const handleEditPVModule = async (module: any) => {
    if (!project || !id) return;

    try {
      // 创建更新后的项目数据
      const updatedProject = { ...project };

      // 检查是否是新增组件还是编辑现有组件
      const existingModuleIndex = project.pvModules.findIndex(item => item.id === module.id);

      if (existingModuleIndex >= 0) {
        // 更新现有组件
        console.log('更新现有光伏组件:', module.id);
        updatedProject.pvModules = project.pvModules.map(item =>
          item.id === module.id ? module : item
        );
      } else {
        // 添加新组件
        console.log('添加新光伏组件:', module.id);
        updatedProject.pvModules = [...project.pvModules, module];
      }

      // 使用数据同步管理器更新项目
      const syncedProject = await handleDataSync(updatedProject, false);

      // 更新本地状态
      setProject(syncedProject);
      dispatch(updateReduxProject(syncedProject));
      // 标记配置已更改
      setIsConfigChanged(true);
    } catch (error) {
      console.error('更新光伏组件失败:', error);
      message.error(t('pvModules.updateFailed'));
    }
  };

  // 处理删除光伏组件
  const handleDeletePVModule = async (moduleId: string) => {
    if (!project || !id) return;

    try {
      // 创建更新后的项目数据
      const updatedProject = { ...project };

      // 从光伏组件列表中移除
      updatedProject.pvModules = project.pvModules.filter(item => item.id !== moduleId);

      // 使用数据同步管理器更新项目
      const syncedProject = await handleDataSync(updatedProject, false);

      // 更新本地状态
      setProject(syncedProject);
      dispatch(updateReduxProject(syncedProject));
      // 标记配置已更改
      setIsConfigChanged(true);
    } catch (error) {
      console.error('删除光伏组件失败:', error);
      message.error(t('pvModules.deleteFailed'));
    }
  };

  // 处理添加储能设备
  const handleAddStorageDevice = async (device: any) => {
    console.log('ProjectAnalysis - 开始添加储能设备:', device);
    if (!project || !id) {
      console.error('ProjectAnalysis - 添加储能设备失败: 项目或ID不存在');
      return;
    }

    try {
      // 创建更新后的项目数据
      const updatedProject = { ...project };

      // 添加新储能设备
      updatedProject.energyStorage = [...project.energyStorage, device];
      console.log('ProjectAnalysis - 更新后的储能设备数量:', updatedProject.energyStorage.length);

      // 更新项目
      console.log('ProjectAnalysis - 开始更新项目...');
      const success = await updateProject(id, updatedProject);

      if (success) {
        console.log('ProjectAnalysis - 项目更新成功');
        // 更新本地状态
        setProject(updatedProject);
        // 标记配置已更改
        setIsConfigChanged(true);
      } else {
        console.error('ProjectAnalysis - 项目更新失败');
      }
    } catch (error) {
      console.error('ProjectAnalysis - 添加储能设备失败:', error);
      message.error(t('energyStorage.addFailed'));
    }
  };

  // 处理编辑储能设备
  const handleEditStorageDevice = async (device: any) => {
    if (!project || !id) return;

    try {
      // 创建更新后的项目数据
      const updatedProject = { ...project };

      // 更新储能设备列表
      updatedProject.energyStorage = project.energyStorage.map(item =>
        item.id === device.id ? device : item
      );

      // 更新项目
      const success = await updateProject(id, updatedProject);

      if (success) {
        // 更新本地状态
        setProject(updatedProject);
        // 标记配置已更改
        setIsConfigChanged(true);
      }
    } catch (error) {
      console.error('更新储能设备失败:', error);
      message.error(t('energyStorage.updateFailed'));
    }
  };

  // 处理删除储能设备
  const handleDeleteStorageDevice = async (deviceId: string) => {
    if (!project || !id) return;

    try {
      // 创建更新后的项目数据
      const updatedProject = { ...project };

      // 从储能设备列表中移除
      updatedProject.energyStorage = project.energyStorage.filter(item => item.id !== deviceId);

      // 更新项目
      const success = await updateProject(id, updatedProject);

      if (success) {
        // 更新本地状态
        setProject(updatedProject);
        // 标记配置已更改
        setIsConfigChanged(true);
      }
    } catch (error) {
      console.error('删除储能设备失败:', error);
      message.error(t('energyStorage.deleteFailed'));
    }
  };

  // 处理重新分析
  const handleReanalyze = async () => {
    if (!project || !id) return;

    try {
      setAnalyzeModalVisible(true);
      setIsAnalyzing(true);
      setAnalyzeProgress(0);

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setAnalyzeProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 1000);

      // 执行分析
      const analyzedProject = await analyzeProject(project);

      // 清除进度更新定时器
      clearInterval(progressInterval);
      setAnalyzeProgress(100);

      // 更新项目数据
      console.log('重新分析完成，更新项目数据:', analyzedProject.id);
      setProject(analyzedProject);
      dispatch(updateReduxProject(analyzedProject));

      // 重置配置更改标记
      setIsConfigChanged(false);

      // 显示成功消息
      message.success(t('analysis.reanalyzeSuccess'));

      // 关闭模态框
      setTimeout(() => {
        setAnalyzeModalVisible(false);
        setIsAnalyzing(false);
      }, 1000);
    } catch (error) {
      console.error('重新分析项目失败:', error);
      message.error(t('analysis.reanalyzeFailed'));
      setIsAnalyzing(false);
      setAnalyzeModalVisible(false);
    }
  };

  // 处理取消分析
  const handleCancelAnalyze = () => {
    setAnalyzeModalVisible(false);
    setIsAnalyzing(false);
    message.info(t('analysis.reanalyzeCancelled'));
  };

  // 处理生成分析报告
  const handleGenerateReport = () => {
    if (!project) return;
    setReportModalVisible(true);
  };

  return (
    <div>
      <PageHeader
        title={t('analysis.title')}
        extra={
          <Button icon={<ArrowLeftOutlined />} onClick={handleBackToList}>
            {t('common.backToList')}
          </Button>
        }
      />

      {/* 项目基本信息 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={16}>
            <Title level={4}>{basicInfo.name}</Title>
            <Paragraph>
              {basicInfo.region} {basicInfo.prefecture} · {(() => {
                // 计算所有光伏组件的总功率 (W)
                const totalPowerW = project.pvModules.reduce(
                  (sum, module) => sum + module.power * module.quantity, 0
                );
                // 转换为kW
                return (totalPowerW / 1000).toFixed(1);
              })()} kW · {project.basicInfo.installationType} · {project.basicInfo.projectType}
            </Paragraph>
            {isConfigChanged && (
              <Alert
                message={t('analysis.configChangedWarning')}
                type="warning"
                showIcon
                style={{ marginTop: 8 }}
              />
            )}
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Space direction="vertical" style={{ width: '100%', alignItems: 'flex-end' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleExportHourlyData}
                >
                  {t('projects.exportHourlyData')}
                </Button>
                <Button
                  type="primary"
                  danger={isConfigChanged}
                  icon={<SyncOutlined />}
                  onClick={handleReanalyze}
                >
                  {t('analysis.reanalyze')}
                </Button>
                <Button
                  type="primary"
                  style={{ backgroundColor: '#ff4d4f', borderColor: '#ff4d4f' }}
                  icon={<FilePdfOutlined />}
                  onClick={handleGenerateReport}
                >
                  {t('analysis.generateReport')}
                </Button>
                {!isConfigChanged && (
                  <Tag color="green" style={{ marginLeft: 8 }}>{t('projects.completed')}</Tag>
                )}
              </Space>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析结果标签页 */}
      <Card>
        <Tabs defaultActiveKey="overview">
          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                {t('analysis.overview')}
              </span>
            }
            key="overview"
          >
            <ProjectOverviewTab project={project} />
          </TabPane>

          <TabPane
            tab={
              <span>
                <LineChartOutlined />
                {t('analysis.pvAnalysis')}
              </span>
            }
            key="pvAnalysis"
          >
            <PVAnalysisTab
              project={project}
              onAddModule={handleAddPVModule}
              onEditModule={handleEditPVModule}
              onDeleteModule={handleDeletePVModule}
              onProjectUpdate={(updatedProject) => {
                setProject(updatedProject);
                dispatch(updateReduxProject(updatedProject));
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <PieChartOutlined />
                {t('analysis.storageAnalysis')}
              </span>
            }
            key="storageAnalysis"
          >
            <StorageAnalysisTab
              project={project}
              onAddDevice={handleAddStorageDevice}
              onEditDevice={handleEditStorageDevice}
              onDeleteDevice={handleDeleteStorageDevice}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 分析进度模态框 */}
      <Modal
        title={t('analysis.analyzing')}
        open={analyzeModalVisible}
        footer={null}
        closable={!isAnalyzing}
        maskClosable={false}
        onCancel={handleCancelAnalyze}
        centered
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress
            type="circle"
            percent={analyzeProgress}
            status={isAnalyzing ? 'active' : 'success'}
          />
          <div style={{ marginTop: 16 }}>
            <p>{t('analysis.analyzingDescription')}</p>
            {isAnalyzing ? (
              <Button onClick={handleCancelAnalyze}>
                {t('common.cancel')}
              </Button>
            ) : (
              <Button type="primary" onClick={() => setAnalyzeModalVisible(false)}>
                {t('common.close')}
              </Button>
            )}
          </div>
        </div>
      </Modal>

      {/* 报告模态框 */}
      <ProjectReportModal
        project={project}
        visible={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        loading={isGeneratingReport}
      />

      {/* 数据冲突解决对话框 */}
      {conflictLocalData && conflictServerData && (
        <DataConflictDialog
          visible={conflictDialogVisible}
          localData={conflictLocalData}
          serverData={conflictServerData}
          onResolve={handleConflictResolve}
          onCancel={() => {
            setConflictDialogVisible(false);
            setConflictLocalData(null);
            setConflictServerData(null);
          }}
          loading={isResolvingConflict}
        />
      )}
    </div>
  );
};

export default ProjectAnalysis;
