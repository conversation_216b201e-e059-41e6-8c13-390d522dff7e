import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { setCurrentProject } from '../../store/slices/projectsSlice';
import NewProject from './NewProject';
import { LoadingSpinner } from '../../components/common';

/**
 * 项目编辑页面
 * 实际上是复用NewProject组件，只是预先加载项目数据
 */
const EditProject: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { projects, isLoading } = useAppSelector((state) => state.projects);
  
  // 加载项目数据
  useEffect(() => {
    if (id) {
      const projectToEdit = projects.find(project => project.id === id);
      if (projectToEdit) {
        dispatch(setCurrentProject(projectToEdit));
      } else {
        // 如果找不到项目，显示错误并返回列表页
        console.error(`找不到ID为${id}的项目`);
        navigate('/projects');
      }
    }
  }, [id, projects, dispatch, navigate]);
  
  if (isLoading) {
    return <LoadingSpinner fullScreen />;
  }
  
  // 复用NewProject组件
  return <NewProject />;
};

export default EditProject;
