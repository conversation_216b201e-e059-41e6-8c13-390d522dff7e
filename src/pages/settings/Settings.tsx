import React from 'react';
import { Tabs, Card } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  SaveOutlined,
  InfoCircleOutlined,
  DatabaseOutlined,
  FolderOutlined
} from '@ant-design/icons';
import { PageHeader } from '../../components/common';
import CacheManagementPanel from '../../components/settings/CacheManagementPanel';
import GeneralSettingsPanel from '../../components/settings/GeneralSettingsPanel';
import UserManagementPanel from '../../components/settings/UserManagementPanel';
import NotificationSettingsPanel from '../../components/settings/NotificationSettingsPanel';
import BackupSettingsPanel from '../../components/settings/BackupSettingsPanel';
import DirectoryManagementPanel from '../../components/settings/DirectoryManagementPanel';
import AboutPanel from '../../components/settings/AboutPanel';

/**
 * 系统设置页面
 */
const Settings: React.FC = () => {
  const { t } = useTranslation();

  // 定义 Tabs 的 items
  const tabItems = [
    {
      key: 'general',
      label: (
        <span>
          <SettingOutlined />
          {t('settings.general.title')}
        </span>
      ),
      children: <GeneralSettingsPanel />
    },
    {
      key: 'account',
      label: (
        <span>
          <UserOutlined />
          {t('settings.account.title')}
        </span>
      ),
      children: <UserManagementPanel />
    },
    {
      key: 'notifications',
      label: (
        <span>
          <BellOutlined />
          {t('settings.notifications.title')}
        </span>
      ),
      children: <NotificationSettingsPanel />
    },
    {
      key: 'backup',
      label: (
        <span>
          <SaveOutlined />
          {t('settings.backup.title')}
        </span>
      ),
      children: <BackupSettingsPanel />
    },
    {
      key: 'directory',
      label: (
        <span>
          <FolderOutlined />
          {t('settings.directory.title')}
        </span>
      ),
      children: <DirectoryManagementPanel />
    },
    {
      key: 'cacheManagement',
      label: (
        <span>
          <DatabaseOutlined />
          {t('settings.cache.title')}
        </span>
      ),
      children: <CacheManagementPanel />
    },
    {
      key: 'about',
      label: (
        <span>
          <InfoCircleOutlined />
          {t('settings.about.title')}
        </span>
      ),
      children: <AboutPanel />
    }
  ];

  return (
    <div>
      <PageHeader title={t('nav.settings')} />

      <Card>
        <Tabs defaultActiveKey="general" items={tabItems} />
      </Card>
    </div>
  );
};

export default Settings;
