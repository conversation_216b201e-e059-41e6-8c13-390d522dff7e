/**
 * 系统服务
 * 用于管理服务器状态和重启功能
 */
import { get, post } from './api';

// 系统状态接口
export interface SystemStatus {
  server: {
    status: 'running' | 'stopped';
    uptime: number;
    memory: {
      rss: number;
      heapTotal: number;
      heapUsed: number;
      external: number;
      arrayBuffers: number;
    };
    pid: number;
  };
  monitor: {
    status: 'running' | 'stopped';
    processes: string[];
  };
  timestamp: string;
}

// 重启响应接口
export interface RestartResponse {
  success: boolean;
  message: string;
  timestamp: string;
}

/**
 * 获取系统状态
 */
export const getSystemStatus = async (): Promise<SystemStatus> => {
  try {
    const response = await get<{ success: boolean; data: SystemStatus }>('/system/status');
    return response.data.data;
  } catch (error) {
    console.error('获取系统状态失败:', error);
    throw error;
  }
};

/**
 * 重启服务器
 */
export const restartServer = async (): Promise<RestartResponse> => {
  try {
    const response = await post<RestartResponse>('/system/restart');
    return response.data;
  } catch (error) {
    console.error('重启服务器失败:', error);
    throw error;
  }
};

/**
 * 检查服务器健康状态
 */
export const checkServerHealth = async (): Promise<boolean> => {
  try {
    const response = await get<{ status: string }>('/health');
    return response.data.status === 'ok';
  } catch (error) {
    console.error('健康检查失败:', error);
    return false;
  }
};

/**
 * 格式化内存使用量
 */
export const formatMemoryUsage = (bytes: number): string => {
  const mb = bytes / 1024 / 1024;
  return `${mb.toFixed(1)} MB`;
};

/**
 * 格式化运行时间
 */
export const formatUptime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`;
  } else {
    return `${secs}秒`;
  }
};
