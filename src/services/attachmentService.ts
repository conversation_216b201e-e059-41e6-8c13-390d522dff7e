/**
 * 附件服务
 * 用于处理设备附件的上传、下载和删除
 */
import { v4 as uuidv4 } from 'uuid';
import { message } from 'antd';
import { get, post, del } from './api';
import { EquipmentAttachment } from '../types/database';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

/**
 * 上传附件
 * @param file 文件对象
 * @param type 附件类型
 * @param equipmentId 设备ID
 * @returns Promise
 */
export const uploadAttachment = async (
  file: File,
  type: 'manual' | 'image' | 'datasheet' | 'other',
  equipmentId: string
): Promise<EquipmentAttachment> => {
  try {
    console.log('开始上传附件:', file.name, '类型:', type, '设备ID:', equipmentId);

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    formData.append('equipmentId', equipmentId);

    // 调用服务器API上传文件
    console.log('调用服务器API上传文件...');
    const response = await post<ApiResponse<EquipmentAttachment>>('/attachments/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    });

    if (!response.data.success) {
      throw new Error(response.data.message || '上传附件失败');
    }

    const attachment = response.data.data;
    console.log('服务器返回的附件数据:', attachment);

    // 确保URL是完整的URL
    if (attachment.url && !attachment.url.startsWith('http')) {
      // 添加服务器基础URL
      const baseUrl = window.location.origin;
      // 确保URL格式正确
      const url = attachment.url.startsWith('/') ? attachment.url : `/${attachment.url}`;
      attachment.url = `${baseUrl}${url}`;
      console.log('完整的附件URL:', attachment.url);
    }

    // 确保文件名正确显示（处理可能的编码问题）
    try {
      // 如果文件名看起来是乱码，尝试修复
      if (/[\ufffd\u00c3-\u00ff]/.test(attachment.name)) {
        console.log('检测到可能的文件名编码问题，尝试修复');
        // 尝试使用原始文件名
        const originalName = file.name;
        console.log('使用原始文件名:', originalName);
        attachment.name = originalName;
      }
    } catch (error) {
      console.error('修复文件名编码失败:', error);
    }

    return attachment;
  } catch (error) {
    console.error('上传附件失败:', error);
    throw error;
  }
};

/**
 * 获取附件列表
 * @param equipmentId 设备ID
 * @returns Promise
 */
export const getAttachments = async (equipmentId: string): Promise<EquipmentAttachment[]> => {
  try {
    console.log('获取附件列表, 设备ID:', equipmentId);

    // 从服务器获取附件列表
    const response = await get<ApiResponse<EquipmentAttachment[]>>(`/attachments?equipmentId=${equipmentId}`);

    if (!response.data.success) {
      console.warn('获取附件列表失败:', response.data.message);
      return [];
    }

    const attachments = response.data.data || [];

    // 确保所有URL都是完整的URL
    const baseUrl = window.location.origin;
    return attachments.map(attachment => {
      if (attachment.url && !attachment.url.startsWith('http')) {
        // 确保URL格式正确
        const url = attachment.url.startsWith('/') ? attachment.url : `/${attachment.url}`;
        return {
          ...attachment,
          url: `${baseUrl}${url}`
        };
      }
      return attachment;
    });
  } catch (error) {
    console.error('获取附件列表失败:', error);
    return [];
  }
};

/**
 * 删除附件
 * @param id 附件ID
 * @param equipmentId 设备ID
 * @returns Promise
 */
export const deleteAttachment = async (id: string, equipmentId: string): Promise<boolean> => {
  try {
    console.log('删除附件, ID:', id, '设备ID:', equipmentId);

    // 调用服务器API删除附件
    const response = await del<ApiResponse<any>>(`/attachments/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || '删除附件失败');
    }

    console.log('附件删除成功');
    return true;
  } catch (error) {
    console.error('删除附件失败:', error);
    throw error;
  }
};

/**
 * 下载附件
 * @param attachment 附件对象
 * @returns Promise
 */
export const downloadAttachment = async (attachment: EquipmentAttachment): Promise<void> => {
  try {
    console.log('下载附件:', attachment.name);

    // 确保URL是完整的
    let downloadUrl = attachment.url;
    if (!downloadUrl.startsWith('http')) {
      const baseUrl = window.location.origin;
      const url = downloadUrl.startsWith('/') ? downloadUrl : `/${downloadUrl}`;
      downloadUrl = `${baseUrl}${url}`;
    }

    console.log('下载URL:', downloadUrl);

    // 创建下载链接
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = attachment.name;
    a.target = '_blank'; // 在新窗口打开，避免跨域问题
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  } catch (error) {
    console.error('下载附件失败:', error);
    throw error;
  }
};
