/**
 * 项目服务
 * 用于处理项目数据的CRUD操作
 */
import { v4 as uuidv4 } from 'uuid';
import { message } from 'antd';
import { get, post, put, del } from './api';
import { Project } from '../types';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 项目列表响应类型
interface ProjectListResponse {
  items: Project[];
  total: number;
}

/**
 * 获取项目列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns Promise
 */
export const getProjectList = async (
  page: number = 1,
  pageSize: number = 10
): Promise<ProjectListResponse> => {
  try {
    console.log('开始获取项目列表');

    // 从服务器获取数据
    const response = await get<ApiResponse<ProjectListResponse>>('/projects', {
      page,
      pageSize
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取项目列表失败');
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);
    
    // 如果API调用失败，返回空数据
    return {
      items: [],
      total: 0
    };
  }
};

/**
 * 获取项目详情
 * @param id 项目ID
 * @returns Promise
 */
export const getProject = async (id: string): Promise<Project> => {
  try {
    console.log(`开始获取项目详情: ${id}`);

    // 从服务器获取数据
    const response = await get<ApiResponse<Project>>(`/projects/${id}`);

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取项目详情失败');
    }
  } catch (error) {
    console.error('获取项目详情失败:', error);
    throw error;
  }
};

/**
 * 创建项目
 * @param project 项目数据
 * @returns Promise
 */
export const createProject = async (project: Partial<Project>): Promise<Project> => {
  try {
    console.log('开始创建项目');

    // 生成ID和时间戳
    const newProject = {
      ...project,
      id: project.id || uuidv4(),
      createdAt: project.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 发送到服务器
    const response = await post<ApiResponse<Project>>('/projects', newProject);

    if (response.data.success) {
      message.success('项目创建成功');
      return response.data.data;
    } else {
      throw new Error(response.data.message || '创建项目失败');
    }
  } catch (error) {
    console.error('创建项目失败:', error);
    
    // 如果API调用失败，返回本地创建的项目数据
    // 这样即使服务器不可用，用户也能继续使用应用
    message.warning('服务器连接失败，项目将保存在本地');
    return project as Project;
  }
};

/**
 * 更新项目
 * @param id 项目ID
 * @param project 项目数据
 * @returns Promise
 */
export const updateProject = async (id: string, project: Partial<Project>): Promise<Project> => {
  try {
    console.log(`开始更新项目: ${id}`);

    // 更新时间戳
    const updatedProject = {
      ...project,
      updatedAt: new Date().toISOString(),
    };

    // 发送到服务器
    const response = await put<ApiResponse<Project>>(`/projects/${id}`, updatedProject);

    if (response.data.success) {
      message.success('项目更新成功');
      return response.data.data;
    } else {
      throw new Error(response.data.message || '更新项目失败');
    }
  } catch (error) {
    console.error('更新项目失败:', error);
    
    // 如果API调用失败，返回本地更新的项目数据
    message.warning('服务器连接失败，项目将保存在本地');
    return { ...project, id } as Project;
  }
};

/**
 * 删除项目
 * @param id 项目ID
 * @returns Promise
 */
export const deleteProject = async (id: string): Promise<boolean> => {
  try {
    console.log(`开始删除项目: ${id}`);

    // 发送到服务器
    const response = await del<ApiResponse<boolean>>(`/projects/${id}`);

    if (response.data.success) {
      message.success('项目删除成功');
      return true;
    } else {
      throw new Error(response.data.message || '删除项目失败');
    }
  } catch (error) {
    console.error('删除项目失败:', error);
    message.error('删除项目失败');
    return false;
  }
};

/**
 * 开始项目分析
 * @param id 项目ID
 * @returns Promise
 */
export const startProjectAnalysis = async (id: string): Promise<boolean> => {
  try {
    console.log(`开始项目分析: ${id}`);

    // 发送到服务器
    const response = await post<ApiResponse<boolean>>(`/projects/${id}/analyze`);

    if (response.data.success) {
      message.success('项目分析已开始');
      return true;
    } else {
      throw new Error(response.data.message || '开始项目分析失败');
    }
  } catch (error) {
    console.error('开始项目分析失败:', error);
    message.error('开始项目分析失败');
    return false;
  }
};

/**
 * 获取项目分析结果
 * @param id 项目ID
 * @returns Promise
 */
export const getProjectAnalysisResults = async (id: string): Promise<any> => {
  try {
    console.log(`获取项目分析结果: ${id}`);

    // 从服务器获取数据
    const response = await get<ApiResponse<any>>(`/projects/${id}/results`);

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取项目分析结果失败');
    }
  } catch (error) {
    console.error('获取项目分析结果失败:', error);
    throw error;
  }
};
