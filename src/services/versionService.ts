/**
 * 版本管理服务
 * 用于获取当前版本、检查更新和执行更新
 */
import { get, post } from './api';
import { message } from 'antd';

// 版本信息接口
export interface VersionInfo {
  current: string;
  latest: string;
  hasUpdate: boolean;
  releaseDate: string;
  changelog: string[];
  downloadUrl?: string;
}

// 更新状态接口
export interface UpdateStatus {
  status: 'idle' | 'checking' | 'downloading' | 'installing' | 'success' | 'error';
  progress: number;
  message: string;
  error?: string;
}

/**
 * 获取版本信息
 * @returns Promise<VersionInfo>
 */
export const getVersionInfo = async (): Promise<VersionInfo> => {
  try {
    const response = await get<{
      success: boolean;
      data: VersionInfo;
      message?: string;
    }>('/version/info');

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取版本信息失败');
    }
  } catch (error) {
    console.error('获取版本信息失败:', error);
    throw error;
  }
};

/**
 * 检查更新
 * @returns Promise<VersionInfo>
 */
export const checkForUpdates = async (): Promise<VersionInfo> => {
  try {
    const response = await get<{
      success: boolean;
      data: VersionInfo;
      message?: string;
    }>('/version/check');

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '检查更新失败');
    }
  } catch (error) {
    console.error('检查更新失败:', error);
    throw error;
  }
};

/**
 * 执行更新
 * @returns Promise<UpdateStatus>
 */
export const performUpdate = async (): Promise<UpdateStatus> => {
  try {
    console.log('Calling update API...');
    const response = await post<{
      success: boolean;
      data: UpdateStatus;
      message?: string;
    }>('/version/update');

    console.log('Update API response:', response.data);

    if (response.data.success) {
      message.success('更新已开始，请稍候...');
      return response.data.data;
    } else {
      throw new Error(response.data.message || '启动更新失败');
    }
  } catch (error) {
    console.error('执行更新失败:', error);
    message.error('更新失败，请稍后重试');
    throw error;
  }
};

/**
 * 获取更新状态
 * @returns Promise<UpdateStatus>
 */
export const getUpdateStatus = async (): Promise<UpdateStatus> => {
  try {
    const response = await get<{
      success: boolean;
      data: UpdateStatus;
      message?: string;
    }>('/version/status');

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取更新状态失败');
    }
  } catch (error) {
    console.error('获取更新状态失败:', error);
    throw error;
  }
};

/**
 * 重启应用
 * @returns Promise<void>
 */
export const restartApplication = async (): Promise<void> => {
  try {
    await post('/version/restart');
    message.success('应用正在重启...');
  } catch (error) {
    console.error('重启应用失败:', error);
    message.error('重启失败，请手动刷新页面');
    throw error;
  }
};

/**
 * 获取更新日志
 * @param version 版本号
 * @returns Promise<string[]>
 */
export const getChangelog = async (version: string): Promise<string[]> => {
  try {
    const response = await get<{
      success: boolean;
      data: string[];
      message?: string;
    }>(`/version/changelog/${version}`);

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取更新日志失败');
    }
  } catch (error) {
    console.error('获取更新日志失败:', error);
    throw error;
  }
};
