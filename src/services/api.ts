/**
 * API服务
 * 用于与后端API通信
 */
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../store';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || (import.meta.env.DEV ? 'http://localhost:3001/api' : '/api'), // API基础URL
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 添加请求拦截器，用于调试
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 发送${config.method?.toUpperCase()}请求到: ${config.baseURL}${config.url}`, config.params || config.data);
    return config;
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器，用于调试
api.interceptors.response.use(
  (response) => {
    console.log(`✅ 收到响应: ${response.config.url}`, response.status, response.data);
    return response;
  },
  (error) => {
    console.error(`❌ 响应错误: ${error.config?.url || '未知URL'}`, error);

    // 详细记录错误信息
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('📡 服务器响应:', {
        status: error.response.status,
        headers: error.response.headers,
        data: error.response.data
      });
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('📡 请求已发送但没有收到响应:', error.request);
      console.error('📡 请求配置:', error.config);

      // 检查服务器连接
      console.log('🔍 正在检查服务器连接...');
      const healthUrl = import.meta.env.DEV ? 'http://localhost:3001/api/health' : '/api/health';
      fetch(healthUrl)
        .then(response => {
          console.log('✅ 服务器连接测试成功:', response.status);
        })
        .catch(err => {
          console.error('❌ 服务器连接测试失败:', err);
          console.log('💡 提示: 请确保服务器正在运行');
        });
    } else {
      // 设置请求时发生错误
      console.error('📡 请求设置错误:', error.message);
    }

    return Promise.reject(error);
  }
);

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从Redux store获取token
    const { token } = store.getState().auth;

    // 如果有token，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理错误响应
    if (error.response) {
      // 服务器返回错误状态码
      const { status } = error.response;

      // 处理401未授权错误
      if (status === 401) {
        // 可以在这里处理登出逻辑
        console.error('未授权，请重新登录');
      }

      // 处理403禁止访问错误
      if (status === 403) {
        console.error('禁止访问');
      }

      // 处理404未找到错误
      if (status === 404) {
        console.error('请求的资源不存在');
      }

      // 处理500服务器错误
      if (status === 500) {
        console.error('服务器错误');
      }
    } else if (error.request) {
      // 请求已发送但未收到响应
      console.error('网络错误，无法连接到服务器');
    } else {
      // 请求配置出错
      console.error('请求配置错误:', error.message);
    }

    return Promise.reject(error);
  }
);

/**
 * GET请求
 * @param url 请求URL
 * @param params 请求参数
 * @param config 请求配置
 * @returns Promise
 */
export const get = <T>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.get(url, { params, ...config });
};

/**
 * POST请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 请求配置
 * @returns Promise
 */
export const post = <T>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.post(url, data, config);
};

/**
 * PUT请求
 * @param url 请求URL
 * @param data 请求数据
 * @param config 请求配置
 * @returns Promise
 */
export const put = <T>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.put(url, data, config);
};

/**
 * DELETE请求
 * @param url 请求URL
 * @param config 请求配置
 * @returns Promise
 */
export const del = <T>(
  url: string,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return api.delete(url, config);
};

/**
 * 上传文件
 * @param url 请求URL
 * @param file 文件
 * @param onProgress 进度回调
 * @returns Promise
 */
export const uploadFile = <T>(
  url: string,
  file: File,
  onProgress?: (progressEvent: any) => void
): Promise<AxiosResponse<T>> => {
  const formData = new FormData();
  formData.append('file', file);

  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: onProgress,
  });
};
