/**
 * 电价政策服务
 * 用于处理电价政策的API请求
 */
import { get, post, put, del } from './api';
import { ElectricityPrice } from '../types/database';
import { cacheManager } from '../utils/dataSynchronization';
import { v4 as uuidv4 } from 'uuid';
import { message } from 'antd';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 电价政策列表响应类型
interface ElectricityPriceListResponse {
  items: ElectricityPrice[];
  total: number;
}

/**
 * 获取电价政策列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns Promise
 */
export const getElectricityPriceList = async (
  page: number = 1,
  pageSize: number = 10
): Promise<ElectricityPriceListResponse> => {
  try {
    console.log('开始获取电价政策列表');

    // 从服务器获取数据
    const response = await get<ApiResponse<ElectricityPriceListResponse>>('/electricity-prices', {
      page,
      pageSize
    });

    const serverItems = response.data.data.items;
    console.log('从服务器获取到数据条数:', serverItems.length);

    // 处理同步状态
    const processedItems = serverItems.map(item => {
      // 检查本地是否有该数据
      const key = `pv_electricity_price_${item.id}`;
      const localData = localStorage.getItem(key);

      // 确定同步状态
      let syncStatus: 'synced' | 'local-only' | 'server-only' = 'server-only';

      if (localData) {
        syncStatus = 'synced';
        // 合并本地数据，确保policyType和seasonalPolicies字段存在
        try {
          const parsedLocalData = JSON.parse(localData);
          return {
            ...item,
            policyType: parsedLocalData.policyType || item.policyType || 'fixed',
            rules: parsedLocalData.rules || item.rules || [],
            seasonalPolicies: parsedLocalData.seasonalPolicies || item.seasonalPolicies || [],
            syncStatus
          };
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }

      // 确保policyType和seasonalPolicies字段存在
      return {
        ...item,
        policyType: item.policyType || 'fixed',
        rules: item.rules || [],
        seasonalPolicies: item.seasonalPolicies || [],
        syncStatus
      };
    });

    // 获取本地数据
    const localItems: ElectricityPrice[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_electricity_price_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '');
          // 检查是否已经包含在服务器数据中
          const isInServer = processedItems.some(item => item.id === data.id);
          if (!isInServer) {
            // 确保数据有正确的字段
            const completeData = {
              ...data,
              policyType: data.policyType || 'fixed',
              rules: data.rules || [],
              seasonalPolicies: data.seasonalPolicies || [],
              syncStatus: 'local-only'
            };
            localItems.push(completeData);
          }
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }
    });

    // 合并服务器和本地数据
    const allItems = [...processedItems, ...localItems];
    console.log('合并后的数据条数:', allItems.length);

    return {
      items: allItems,
      total: allItems.length
    };
  } catch (error) {
    console.error('获取电价政策列表失败:', error);

    // 如果服务器请求失败，尝试从本地获取数据
    const localItems: ElectricityPrice[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_electricity_price_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '');
          // 确保数据有正确的字段
          const completeData = {
            ...data,
            policyType: data.policyType || 'fixed',
            rules: data.rules || [],
            seasonalPolicies: data.seasonalPolicies || [],
            syncStatus: 'local-only'
          };
          localItems.push(completeData);
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }
    });

    console.log('从本地获取到数据条数:', localItems.length);

    return {
      items: localItems,
      total: localItems.length
    };
  }
};

/**
 * 获取电价政策详情
 * @param id 电价政策ID
 * @returns Promise
 */
export const getElectricityPriceDetail = async (id: string): Promise<ElectricityPrice> => {
  try {
    console.log('开始获取电价政策详情, ID:', id);

    // 检查本地是否有该数据
    const key = `pv_electricity_price_${id}`;
    const localData = localStorage.getItem(key);
    let isServerData = false;

    if (localData) {
      try {
        const parsedData = JSON.parse(localData);
        console.log('从本地获取到数据:', parsedData.name);

        // 如果本地数据有效，确保字段完整后返回
        if (parsedData) {
          // 确保数据有正确的字段
          const completeData = {
            ...parsedData,
            policyType: parsedData.policyType || 'fixed',
            rules: parsedData.rules || [],
            seasonalPolicies: parsedData.seasonalPolicies || [],
          };
          console.log('处理后的本地数据:', completeData);
          return completeData;
        }
      } catch (error) {
        console.error('解析本地数据失败:', error);
        isServerData = true;
      }
    } else {
      // 如果本地没有数据，则认为是服务器数据
      isServerData = true;
      console.log('本地没有数据，认为是服务器数据');
    }

    // 如果是服务器数据或本地数据无效，尝试从服务器获取
    if (isServerData || !localData) {
      console.log('尝试从服务器获取数据, ID:', id);
      try {
        // 从服务器获取数据
        const response = await get<ApiResponse<ElectricityPrice>>(`/electricity-prices/${id}`);
        const serverData = response.data.data;

        // 验证服务器数据的有效性
        if (serverData) {
          console.log('从服务器获取到数据:', serverData.name);

          // 确保数据有正确的字段
          const completeData = {
            ...serverData,
            policyType: serverData.policyType || 'fixed',
            rules: serverData.rules || [],
            seasonalPolicies: serverData.seasonalPolicies || [],
            syncStatus: 'synced'
          };

          console.log('处理后的服务器数据:', completeData);

          // 保存到本地
          localStorage.setItem(key, JSON.stringify(completeData));

          // 添加到缓存
          try {
            cacheManager.addItem(id, completeData);
          } catch (cacheError) {
            console.error('添加到缓存失败:', cacheError);
          }

          return completeData;
        } else {
          throw new Error('服务器数据无效');
        }
      } catch (serverError) {
        console.error('从服务器获取数据失败:', serverError);
        throw serverError;
      }
    }

    throw new Error('无法获取电价政策详情');
  } catch (error) {
    console.error('获取电价政策详情失败:', error);
    throw error;
  }
};

/**
 * 创建电价政策
 * @param data 电价政策数据
 * @returns Promise
 */
export const createElectricityPrice = async (data: Partial<ElectricityPrice>): Promise<ElectricityPrice> => {
  try {
    // 生成ID
    const id = uuidv4();
    const now = new Date().toISOString();

    // 创建新的电价政策
    console.log('创建电价政策，输入数据:', data);

    const newElectricityPrice: ElectricityPrice = {
      id,
      name: data.name || '',
      region: data.region || '',
      policyType: data.policyType || 'fixed',
      rules: data.rules || [],
      seasonalPolicies: data.seasonalPolicies || [],
      createdAt: now,
      updatedAt: now,
      syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
    };

    console.log('创建的新电价政策:', newElectricityPrice);

    // 保存到本地
    const key = `pv_electricity_price_${id}`;
    localStorage.setItem(key, JSON.stringify(newElectricityPrice));

    // 尝试同步到服务器
    try {
      const response = await post<ApiResponse<ElectricityPrice>>('/electricity-prices', newElectricityPrice);
      const serverData = response.data.data;

      // 更新同步状态
      newElectricityPrice.syncStatus = 'synced';
      localStorage.setItem(key, JSON.stringify(newElectricityPrice));

      // 添加到缓存
      try {
        cacheManager.addItem(id, newElectricityPrice);
      } catch (cacheError) {
        console.error('添加到缓存失败:', cacheError);
      }

      message.success('电价政策已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      message.warning('同步到服务器失败，数据将保存在本地');
    }

    return newElectricityPrice;
  } catch (error) {
    console.error('创建电价政策失败:', error);
    throw error;
  }
};

/**
 * 更新电价政策
 * @param id 电价政策ID
 * @param data 更新的数据
 * @returns Promise
 */
export const updateElectricityPrice = async (id: string, data: Partial<ElectricityPrice>): Promise<ElectricityPrice> => {
  try {
    // 获取现有数据
    const key = `pv_electricity_price_${id}`;
    const localData = localStorage.getItem(key);
    let existingData: ElectricityPrice;

    if (localData) {
      existingData = JSON.parse(localData);
    } else {
      // 如果本地没有数据，尝试从服务器获取
      const response = await get<ApiResponse<ElectricityPrice>>(`/electricity-prices/${id}`);
      existingData = response.data.data;
    }

    // 更新数据
    const updatedData: ElectricityPrice = {
      ...existingData,
      ...data,
      id, // 确保ID不变
      updatedAt: new Date().toISOString(),
      syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
    };

    // 保存到本地
    localStorage.setItem(key, JSON.stringify(updatedData));

    // 尝试同步到服务器
    try {
      const response = await put<ApiResponse<ElectricityPrice>>(`/electricity-prices/${id}`, updatedData);
      const serverData = response.data.data;

      // 更新同步状态
      updatedData.syncStatus = 'synced';
      localStorage.setItem(key, JSON.stringify(updatedData));

      // 更新缓存
      try {
        cacheManager.addItem(id, updatedData);
      } catch (cacheError) {
        console.error('更新缓存失败:', cacheError);
      }

      message.success('电价政策已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      message.warning('同步到服务器失败，数据将保存在本地');
    }

    return updatedData;
  } catch (error) {
    console.error('更新电价政策失败:', error);
    throw error;
  }
};

/**
 * 删除电价政策
 * @param id 电价政策ID
 * @returns Promise
 */
export const deleteElectricityPrice = async (id: string): Promise<boolean> => {
  try {
    // 从本地删除
    const key = `pv_electricity_price_${id}`;
    localStorage.removeItem(key);

    // 尝试从服务器删除
    try {
      await del<ApiResponse<any>>(`/electricity-prices/${id}`);
    } catch (serverError) {
      console.error('从服务器删除失败:', serverError);
      // 即使服务器删除失败，也认为删除成功，因为本地已删除
    }

    return true;
  } catch (error) {
    console.error('删除电价政策失败:', error);
    throw error;
  }
};
