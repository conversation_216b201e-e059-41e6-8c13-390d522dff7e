import { message } from 'antd';
import { ProjectData, DataVersion } from '../types/projectData';
import { updateProject, getProject } from './projectSyncService';

/**
 * 数据同步状态
 */
export type DataSyncStatus = 'synced' | 'pending' | 'conflict' | 'error';



/**
 * 同步结果
 */
export interface SyncResult {
  success: boolean;
  data?: ProjectData;
  conflict?: boolean;
  error?: string;
}

/**
 * 数据同步管理器
 * 负责前后端数据的一致性管理和冲突解决
 */
class DataSyncManager {
  private syncQueue: Map<string, ProjectData> = new Map();
  private syncInProgress: Set<string> = new Set();
  private syncTimers: Map<string, number> = new Map();

  // 同步延迟时间（毫秒）
  private readonly SYNC_DELAY = 1000;

  // 版本冲突检测阈值（毫秒）
  private readonly CONFLICT_THRESHOLD = 5000;

  /**
   * 立即同步项目数据到服务器
   * @param projectId 项目ID
   * @param projectData 项目数据
   * @param force 是否强制同步，忽略冲突
   */
  async syncImmediately(
    projectId: string,
    projectData: ProjectData,
    force: boolean = false
  ): Promise<SyncResult> {
    try {
      console.log(`🔄 开始立即同步项目数据: ${projectId}`);

      // 标记同步进行中
      this.syncInProgress.add(projectId);

      // 如果不是强制同步，先检查版本冲突
      if (!force) {
        const conflictCheck = await this.checkVersionConflict(projectId, projectData);
        if (conflictCheck.hasConflict) {
          console.warn(`⚠️ 检测到版本冲突: ${projectId}`);
          return {
            success: false,
            conflict: true,
            error: '检测到数据版本冲突，请选择保留哪个版本的数据'
          };
        }
      }

      // 更新数据版本信息
      const updatedData = this.updateDataVersion(projectData, 'frontend');

      // 同步到服务器
      const syncedData = await updateProject(projectId, updatedData);

      console.log(`✅ 项目数据同步成功: ${projectId}`);

      // 清理同步状态
      this.syncInProgress.delete(projectId);
      this.syncQueue.delete(projectId);

      return {
        success: true,
        data: syncedData as ProjectData
      };

    } catch (error) {
      console.error(`❌ 项目数据同步失败: ${projectId}`, error);
      this.syncInProgress.delete(projectId);

      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 延迟同步项目数据（防抖）
   * @param projectId 项目ID
   * @param projectData 项目数据
   */
  syncWithDelay(projectId: string, projectData: ProjectData): void {
    console.log(`⏰ 添加到延迟同步队列: ${projectId}`);

    // 添加到同步队列
    this.syncQueue.set(projectId, projectData);

    // 清除之前的定时器
    const existingTimer = this.syncTimers.get(projectId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 设置新的定时器
    const timer = window.setTimeout(async () => {
      const queuedData = this.syncQueue.get(projectId);
      if (queuedData && !this.syncInProgress.has(projectId)) {
        await this.syncImmediately(projectId, queuedData);
      }
      this.syncTimers.delete(projectId);
    }, this.SYNC_DELAY);

    this.syncTimers.set(projectId, timer);
  }

  /**
   * 检查版本冲突
   * @param projectId 项目ID
   * @param localData 本地数据
   */
  private async checkVersionConflict(
    projectId: string,
    localData: ProjectData
  ): Promise<{ hasConflict: boolean; serverData?: ProjectData }> {
    try {
      // 获取服务器数据
      const serverData = await getProject(projectId);

      if (!serverData || !serverData.updatedAt || !localData.updatedAt) {
        return { hasConflict: false };
      }

      const serverTime = new Date(serverData.updatedAt).getTime();
      const localTime = new Date(localData.updatedAt).getTime();

      // 如果服务器数据更新时间比本地数据晚，且超过冲突阈值，则认为有冲突
      const hasConflict = serverTime > localTime &&
                         (serverTime - localTime) > this.CONFLICT_THRESHOLD;

      return {
        hasConflict,
        serverData: hasConflict ? serverData : undefined
      };

    } catch (error) {
      console.error('检查版本冲突失败:', error);
      return { hasConflict: false };
    }
  }

  /**
   * 更新数据版本信息
   * @param data 项目数据
   * @param source 数据来源
   */
  private updateDataVersion(data: ProjectData, source: 'frontend' | 'backend'): ProjectData {
    const now = new Date().toISOString();
    const currentVersion = data.dataVersion?.version || 0;

    return {
      ...data,
      updatedAt: now,
      dataVersion: {
        timestamp: now,
        version: currentVersion + 1,
        source
      }
    };
  }

  /**
   * 解决数据冲突
   * @param projectId 项目ID
   * @param localData 本地数据
   * @param serverData 服务器数据
   * @param resolution 解决方案：'local' | 'server' | 'merge'
   */
  async resolveConflict(
    projectId: string,
    localData: ProjectData,
    serverData: ProjectData,
    resolution: 'local' | 'server' | 'merge'
  ): Promise<SyncResult> {
    try {
      let resolvedData: ProjectData;

      switch (resolution) {
        case 'local':
          // 使用本地数据覆盖服务器
          resolvedData = this.updateDataVersion(localData, 'frontend');
          break;

        case 'server':
          // 使用服务器数据覆盖本地
          resolvedData = this.updateDataVersion(serverData, 'backend');
          break;

        case 'merge':
          // 智能合并数据（优先保留最新的分析结果）
          resolvedData = this.mergeProjectData(localData, serverData);
          break;

        default:
          throw new Error('无效的冲突解决方案');
      }

      // 强制同步解决后的数据
      return await this.syncImmediately(projectId, resolvedData, true);

    } catch (error) {
      console.error('解决数据冲突失败:', error);
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 智能合并项目数据
   * @param localData 本地数据
   * @param serverData 服务器数据
   */
  private mergeProjectData(localData: ProjectData, serverData: ProjectData): ProjectData {
    // 比较分析结果的时间戳，保留最新的
    const localAnalysisTime = localData.analysisResults?.analysisDate;
    const serverAnalysisTime = serverData.analysisResults?.analysisDate;

    let analysisResults = serverData.analysisResults;

    if (localAnalysisTime && serverAnalysisTime) {
      if (new Date(localAnalysisTime) > new Date(serverAnalysisTime)) {
        analysisResults = localData.analysisResults;
      }
    } else if (localAnalysisTime && !serverAnalysisTime) {
      analysisResults = localData.analysisResults;
    }

    // 合并数据，优先使用最新的基础配置
    const localTime = new Date(localData.updatedAt || 0).getTime();
    const serverTime = new Date(serverData.updatedAt || 0).getTime();

    const baseData = localTime > serverTime ? localData : serverData;

    return this.updateDataVersion({
      ...baseData,
      analysisResults
    }, 'frontend');
  }

  /**
   * 获取同步状态
   * @param projectId 项目ID
   */
  getSyncStatus(projectId: string): DataSyncStatus {
    if (this.syncInProgress.has(projectId)) {
      return 'pending';
    }

    if (this.syncQueue.has(projectId)) {
      return 'pending';
    }

    return 'synced';
  }

  /**
   * 清理同步队列
   * @param projectId 项目ID（可选，不提供则清理所有）
   */
  clearSyncQueue(projectId?: string): void {
    if (projectId) {
      this.syncQueue.delete(projectId);
      const timer = this.syncTimers.get(projectId);
      if (timer) {
        clearTimeout(timer);
        this.syncTimers.delete(projectId);
      }
    } else {
      this.syncQueue.clear();
      this.syncTimers.forEach(timer => clearTimeout(timer));
      this.syncTimers.clear();
    }
  }
}

// 导出单例实例
export const dataSyncManager = new DataSyncManager();
