/**
 * 报告生成服务
 * 用于生成项目分析报告
 */
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { message } from 'antd';
import { ProjectData } from '../types/projectData';
import i18n from '../locales/i18n';

// PDF配置
const PDF_CONFIG = {
  format: 'a4',
  orientation: 'portrait', // 'portrait' 或 'landscape'
  unit: 'mm',
  marginLeft: 15,
  marginRight: 15,
  marginTop: 15,
  marginBottom: 15,
  fontSize: {
    title: 18,
    subtitle: 14,
    heading: 12,
    normal: 10,
    small: 8
  },
  lineHeight: 1.2,
  maxWidth: 180, // A4宽度减去左右边距
};

/**
 * 生成项目分析报告
 * @param project 项目数据
 * @param reportElement 报告HTML元素
 * @returns Promise
 */
export const generateProjectReport = async (
  project: ProjectData,
  reportElement: HTMLElement
): Promise<void> => {
  try {
    const { t } = i18n;
    console.log('开始生成项目分析报告:', project.name);

    // 检查项目数据是否有效
    if (!project.analysisResults) {
      console.warn('项目分析结果不存在，可能导致报告生成失败');
    } else {
      console.log('项目分析结果状态:', {
        analysisCompleted: project.analysisResults.analysisCompleted,
        hourlyDataCount: project.analysisResults.hourlyData?.length || 0
      });
    }

    // 检查报告元素是否有效
    if (!reportElement) {
      throw new Error('报告元素不存在');
    }

    // 检查报告元素中的图表
    const chartElements = reportElement.querySelectorAll('.echarts-for-react');
    console.log(`报告元素中包含 ${chartElements.length} 个图表元素`);

    // 检查图表内容
    chartElements.forEach((chart, index) => {
      const canvas = chart.querySelector('canvas');
      console.log(`图表 ${index + 1} ${canvas ? '包含' : '不包含'} canvas元素`);
    });

    // 显示加载消息
    const loadingMessage = message.loading(t('report.generating'), 0);

    // 确保所有图表已完全渲染
    console.log('等待图表渲染完成...');

    // 检查图表容器
    const chartContainers = reportElement.querySelectorAll('.chart-container');
    console.log(`找到 ${chartContainers.length} 个图表容器`);

    // 确保图表容器可见
    chartContainers.forEach((container, index) => {
      if (container instanceof HTMLElement) {
        console.log(`处理图表容器 ${index + 1}`);
        container.style.height = '500px';
        container.style.width = '100%';
        container.style.visibility = 'visible';
        container.style.display = 'block';

        // 获取图表实例并调整大小
        const chartElement = container.querySelector('.echarts-for-react');
        if (chartElement) {
          const chartInstance = (chartElement as any).__echarts_instance__;
          if (chartInstance) {
            console.log(`调整图表 ${index + 1} 大小`);
            chartInstance.resize();
          }
        }
      }
    });

    // 等待图表渲染完成
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 创建PDF文档
    const pdf = new jsPDF({
      orientation: PDF_CONFIG.orientation as any,
      unit: PDF_CONFIG.unit,
      format: PDF_CONFIG.format,
    });

    try {
      // 使用html2canvas将报告元素转换为图像
      const canvas = await html2canvas(reportElement, {
        scale: 3, // 提高清晰度
        useCORS: true, // 允许加载跨域图像
        logging: true, // 开启日志以便调试
        allowTaint: true, // 允许污染画布
        backgroundColor: '#ffffff', // 设置背景色为白色
        foreignObjectRendering: false, // 禁用foreignObject渲染，提高兼容性
        removeContainer: false, // 不移除临时容器，避免图表消失
        ignoreElements: (element) => {
          // 忽略隐藏元素
          return element.style.display === 'none' || element.style.visibility === 'hidden';
        },
        onclone: (clonedDoc) => {
          console.log('克隆文档进行处理...');

          // 在克隆的文档中查找所有图表容器
          const chartContainers = clonedDoc.querySelectorAll('.chart-container');
          console.log(`找到 ${chartContainers.length} 个图表容器`);

          // 确保图表容器可见
          chartContainers.forEach((container: Element, index: number) => {
            if (container instanceof HTMLElement) {
              console.log(`处理图表容器 ${index + 1}`);
              container.style.height = '500px';
              container.style.width = '100%';
              container.style.visibility = 'visible';
              container.style.display = 'block';
              container.style.overflow = 'visible';

              // 确保图表内部的echarts容器可见
              const echartsContainers = container.querySelectorAll('.echarts-for-react');
              echartsContainers.forEach((echarts: Element, echartsIndex: number) => {
                if (echarts instanceof HTMLElement) {
                  console.log(`处理图表容器 ${index + 1} 中的echarts容器 ${echartsIndex + 1}`);
                  echarts.style.height = '500px';
                  echarts.style.width = '100%';
                  echarts.style.visibility = 'visible';
                  echarts.style.display = 'block';
                  echarts.style.overflow = 'visible';
                }
              });

              // 确保图表内部的canvas元素也是可见的
              const canvasElements = container.querySelectorAll('canvas');
              canvasElements.forEach((canvas: Element, canvasIndex: number) => {
                if (canvas instanceof HTMLCanvasElement) {
                  console.log(`处理图表 ${index + 1} 中的canvas ${canvasIndex + 1}`);
                  canvas.style.visibility = 'visible';
                  canvas.style.display = 'block';
                  canvas.style.width = '100%';
                  canvas.style.height = '100%';
                }
              });
            }
          });

          // 处理所有的echarts容器（可能不在.chart-container中）
          const allEchartsContainers = clonedDoc.querySelectorAll('.echarts-for-react');
          console.log(`找到总共 ${allEchartsContainers.length} 个echarts容器`);

          allEchartsContainers.forEach((container: Element, index: number) => {
            if (container instanceof HTMLElement) {
              console.log(`处理echarts容器 ${index + 1}`);
              container.style.height = '500px';
              container.style.width = '100%';
              container.style.visibility = 'visible';
              container.style.display = 'block';
              container.style.overflow = 'visible';

              // 确保图表内部的canvas元素也是可见的
              const canvasElements = container.querySelectorAll('canvas');
              canvasElements.forEach((canvas: Element, canvasIndex: number) => {
                if (canvas instanceof HTMLCanvasElement) {
                  console.log(`处理echarts容器 ${index + 1} 中的canvas ${canvasIndex + 1}`);
                  canvas.style.visibility = 'visible';
                  canvas.style.display = 'block';
                  canvas.style.width = '100%';
                  canvas.style.height = '100%';
                }
              });
            }
          });

          return clonedDoc;
        }
      });

      // 获取画布数据
      const imgData = canvas.toDataURL('image/jpeg', 0.95);

      // 计算PDF页面尺寸
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // 计算图像尺寸以适应PDF页面
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const imgX = (pdfWidth - imgWidth * ratio) / 2;
      const imgY = 0;

      // 计算需要的页数
      const pageCount = Math.ceil(imgHeight * ratio / pdfHeight);

      // 添加图像到PDF
      for (let i = 0; i < pageCount; i++) {
        // 如果不是第一页，添加新页
        if (i > 0) {
          pdf.addPage();
        }

        // 计算当前页的图像部分
        const srcY = i * pdfHeight / ratio;
        const srcHeight = Math.min(pdfHeight / ratio, imgHeight - srcY);

        // 添加图像到PDF
        pdf.addImage(
          imgData,
          'JPEG',
          imgX,
          imgY,
          imgWidth * ratio,
          imgHeight * ratio,
          undefined,
          'FAST',
          0,
          {
            srcY: srcY,
            srcHeight: srcHeight
          }
        );
      }

      // 生成文件名
      const fileName = `${project.name || project.id}_分析报告_${new Date().toLocaleDateString().replace(/\//g, '-')}.pdf`;

      // 保存PDF
      pdf.save(fileName);

      // 关闭加载消息
      loadingMessage();

      // 显示成功消息
      message.success(t('report.generateSuccess'));

      console.log('项目分析报告生成成功:', fileName);
    } catch (canvasError) {
      console.error('生成Canvas失败:', canvasError);
      loadingMessage();
      message.error(t('report.generateFailed'));
      throw canvasError;
    }
  } catch (error) {
    console.error('生成项目分析报告失败:', error);
    message.error(t('report.generateFailed'));
    throw error;
  }
};

/**
 * 生成项目长图
 * @param project 项目数据
 * @param reportElement 报告HTML元素
 * @returns Promise
 */
export const generateProjectLongImage = async (
  project: ProjectData,
  reportElement: HTMLElement
): Promise<void> => {
  try {
    const { t } = i18n;
    console.log('开始生成项目长图:', project.name);

    // 检查项目数据是否有效
    if (!project.analysisResults) {
      console.warn('项目分析结果不存在，可能导致长图生成失败');
    }

    // 检查报告元素是否有效
    if (!reportElement) {
      throw new Error('报告元素不存在');
    }

    // 显示加载消息
    const loadingMessage = message.loading(t('report.generatingLongImage'), 0);

    // 确保所有图表已完全渲染
    console.log('等待图表渲染完成...');

    // 检查图表容器
    const chartContainers = reportElement.querySelectorAll('.chart-container');
    console.log(`找到 ${chartContainers.length} 个图表容器`);

    // 确保图表容器可见
    chartContainers.forEach((container, index) => {
      if (container instanceof HTMLElement) {
        console.log(`处理图表容器 ${index + 1}`);
        container.style.height = '500px';
        container.style.width = '100%';
        container.style.visibility = 'visible';
        container.style.display = 'block';

        // 获取图表实例并调整大小
        const chartElement = container.querySelector('.echarts-for-react');
        if (chartElement) {
          const chartInstance = (chartElement as any).__echarts_instance__;
          if (chartInstance) {
            console.log(`调整图表 ${index + 1} 大小`);
            chartInstance.resize();
          }
        }
      }
    });

    // 等待图表渲染完成
    await new Promise(resolve => setTimeout(resolve, 3000));

    try {
      // 使用html2canvas将报告元素转换为图像
      const canvas = await html2canvas(reportElement, {
        scale: 2, // 提高清晰度
        useCORS: true, // 允许加载跨域图像
        logging: true, // 开启日志以便调试
        allowTaint: true, // 允许污染画布
        backgroundColor: '#ffffff', // 设置背景色为白色
        foreignObjectRendering: false, // 禁用foreignObject渲染，提高兼容性
        removeContainer: false, // 不移除临时容器，避免图表消失
        ignoreElements: (element) => {
          // 忽略隐藏元素
          return element.style.display === 'none' || element.style.visibility === 'hidden';
        },
        onclone: (clonedDoc) => {
          console.log('克隆文档进行处理...');

          // 在克隆的文档中查找所有图表容器
          const chartContainers = clonedDoc.querySelectorAll('.chart-container');
          console.log(`找到 ${chartContainers.length} 个图表容器`);

          // 确保图表容器可见
          chartContainers.forEach((container: Element, index: number) => {
            if (container instanceof HTMLElement) {
              console.log(`处理图表容器 ${index + 1}`);
              container.style.height = '500px';
              container.style.width = '100%';
              container.style.visibility = 'visible';
              container.style.display = 'block';
              container.style.overflow = 'visible';

              // 确保图表内部的echarts容器可见
              const echartsContainers = container.querySelectorAll('.echarts-for-react');
              echartsContainers.forEach((echarts: Element, echartsIndex: number) => {
                if (echarts instanceof HTMLElement) {
                  console.log(`处理图表容器 ${index + 1} 中的echarts容器 ${echartsIndex + 1}`);
                  echarts.style.height = '500px';
                  echarts.style.width = '100%';
                  echarts.style.visibility = 'visible';
                  echarts.style.display = 'block';
                  echarts.style.overflow = 'visible';
                }
              });

              // 确保图表内部的canvas元素也是可见的
              const canvasElements = container.querySelectorAll('canvas');
              canvasElements.forEach((canvas: Element, canvasIndex: number) => {
                if (canvas instanceof HTMLCanvasElement) {
                  console.log(`处理图表 ${index + 1} 中的canvas ${canvasIndex + 1}`);
                  canvas.style.visibility = 'visible';
                  canvas.style.display = 'block';
                  canvas.style.width = '100%';
                  canvas.style.height = '100%';
                }
              });
            }
          });

          // 处理所有的echarts容器（可能不在.chart-container中）
          const allEchartsContainers = clonedDoc.querySelectorAll('.echarts-for-react');
          console.log(`找到总共 ${allEchartsContainers.length} 个echarts容器`);

          allEchartsContainers.forEach((container: Element, index: number) => {
            if (container instanceof HTMLElement) {
              console.log(`处理echarts容器 ${index + 1}`);
              container.style.height = '500px';
              container.style.width = '100%';
              container.style.visibility = 'visible';
              container.style.display = 'block';
              container.style.overflow = 'visible';

              // 确保图表内部的canvas元素也是可见的
              const canvasElements = container.querySelectorAll('canvas');
              canvasElements.forEach((canvas: Element, canvasIndex: number) => {
                if (canvas instanceof HTMLCanvasElement) {
                  console.log(`处理echarts容器 ${index + 1} 中的canvas ${canvasIndex + 1}`);
                  canvas.style.visibility = 'visible';
                  canvas.style.display = 'block';
                  canvas.style.width = '100%';
                  canvas.style.height = '100%';
                }
              });
            }
          });

          return clonedDoc;
        }
      });

      // 获取画布数据
      const imgData = canvas.toDataURL('image/png', 1.0);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = imgData;
      link.download = `${project.name || project.id}_长图_${new Date().toLocaleDateString().replace(/\//g, '-')}.png`;

      // 添加到文档并触发点击
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);

      // 关闭加载消息
      loadingMessage();

      // 显示成功消息
      message.success(t('report.generateLongImageSuccess'));

      console.log('项目长图生成成功');
    } catch (canvasError) {
      console.error('生成Canvas失败:', canvasError);
      loadingMessage();
      message.error(t('report.generateFailed'));
      throw canvasError;
    }
  } catch (error) {
    console.error('生成项目长图失败:', error);
    message.error(t('report.generateFailed'));
    throw error;
  }
};

/**
 * 生成项目分析报告（直接生成，不需要HTML元素）
 * @param project 项目数据
 * @returns Promise
 */
export const generateProjectReportDirect = async (project: ProjectData): Promise<void> => {
  try {
    const { t } = i18n;
    console.log('开始直接生成项目分析报告:', project.name);

    // 显示加载消息
    const loadingMessage = message.loading(t('report.generating'), 0);

    // 创建PDF文档
    const pdf = new jsPDF({
      orientation: PDF_CONFIG.orientation as any,
      unit: PDF_CONFIG.unit,
      format: PDF_CONFIG.format,
    });

    // 设置字体
    pdf.setFont('helvetica', 'normal');

    // 添加标题
    pdf.setFontSize(PDF_CONFIG.fontSize.title);
    pdf.text(project.name, PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 10);

    // 添加基本信息
    pdf.setFontSize(PDF_CONFIG.fontSize.subtitle);
    pdf.text(t('report.basicInfo'), PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 20);

    // 添加项目详情
    pdf.setFontSize(PDF_CONFIG.fontSize.normal);
    const basicInfo = project.basicInfo;
    pdf.text(`${t('projects.location')}: ${basicInfo.region} ${basicInfo.prefecture}`, PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 30);
    // 计算所有光伏组件的总功率 (W)
    const totalPowerW = project.pvModules.reduce(
      (sum, module) => sum + module.power * module.quantity, 0
    );
    // 转换为kW
    const totalPowerKW = (totalPowerW / 1000).toFixed(1);
    pdf.text(`${t('projects.capacity')}: ${totalPowerKW} kW`, PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 35);
    pdf.text(`${t('projects.installationType')}: ${basicInfo.installationType}`, PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 40);
    pdf.text(`${t('projects.projectType')}: ${basicInfo.projectType}`, PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 45);

    // 添加分析结果
    pdf.setFontSize(PDF_CONFIG.fontSize.subtitle);
    pdf.text(t('report.analysisResults'), PDF_CONFIG.marginLeft, PDF_CONFIG.marginTop + 60);

    // 保存PDF
    const fileName = `${project.name || project.id}_分析报告_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);

    // 关闭加载消息
    loadingMessage();

    // 显示成功消息
    message.success(t('report.generateSuccess'));

    console.log('项目分析报告生成成功:', fileName);
  } catch (error) {
    console.error('生成项目分析报告失败:', error);
    message.error(t('report.generateFailed'));
    throw error;
  }
};
