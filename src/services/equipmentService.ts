/**
 * 设备服务
 * 用于处理设备数据的CRUD操作
 */
import { v4 as uuidv4 } from 'uuid';
import { message } from 'antd';
import { get, post, put, del } from './api';
import { Equipment } from '../types/database';
import { cacheManager } from '../utils/dataSynchronization';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 设备列表响应类型
interface EquipmentListResponse {
  items: Equipment[];
  total: number;
}

/**
 * 获取设备列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns Promise
 */
export const getEquipmentList = async (
  page: number = 1,
  pageSize: number = 10
): Promise<EquipmentListResponse> => {
  try {
    console.log('开始获取设备列表');

    // 从服务器获取数据
    const response = await get<ApiResponse<EquipmentListResponse>>('/equipment', {
      page,
      pageSize
    });

    const serverItems = response.data.data.items;
    console.log('从服务器获取到数据条数:', serverItems.length);

    // 处理同步状态
    const processedItems = serverItems.map(item => {
      // 检查本地是否有该数据
      const key = `pv_equipment_${item.id}`;
      const localData = localStorage.getItem(key);

      // 确定同步状态
      let syncStatus: 'synced' | 'local-only' | 'server-only' = 'server-only';

      if (localData) {
        syncStatus = 'synced';
        // 合并本地数据
        try {
          const parsedLocalData = JSON.parse(localData);
          return {
            ...item,
            syncStatus
          };
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }

      // 如果没有本地数据或解析失败，使用服务器数据
      return {
        ...item,
        syncStatus
      };
    });

    // 获取本地数据
    const localItems: Equipment[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_equipment_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '');
          // 检查是否已经包含在服务器数据中
          const isInServer = processedItems.some(item => item.id === data.id);
          if (!isInServer) {
            // 确保数据有正确的字段
            const completeData = {
              ...data,
              syncStatus: 'local-only'
            };
            localItems.push(completeData);
          }
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }
    });

    // 合并服务器和本地数据
    const allItems = [...processedItems, ...localItems];
    console.log('合并后的数据条数:', allItems.length);

    return {
      items: allItems,
      total: allItems.length
    };
  } catch (error) {
    console.error('获取设备列表失败:', error);

    // 如果服务器请求失败，尝试从本地获取数据
    const localItems: Equipment[] = [];
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('pv_equipment_')) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '');
          // 确保数据有正确的字段
          const completeData = {
            ...data,
            syncStatus: 'local-only'
          };
          localItems.push(completeData);
        } catch (error) {
          console.error('解析本地数据失败:', error);
        }
      }
    });

    console.log('从本地获取到数据条数:', localItems.length);

    return {
      items: localItems,
      total: localItems.length
    };
  }
};

/**
 * 获取设备详情
 * @param id 设备ID
 * @returns Promise
 */
export const getEquipmentDetail = async (id: string): Promise<Equipment> => {
  try {
    console.log('开始获取设备详情, ID:', id);

    // 检查缓存
    try {
      const cachedData = cacheManager.getItem(id);
      if (cachedData) {
        console.log('从缓存获取到数据, ID:', id);
        // 将 IrradianceData 转换为 Equipment
        return {
          id: cachedData.id,
          name: cachedData.name,
          type: (cachedData as any).type || 'pv',
          manufacturer: (cachedData as any).manufacturer || '',
          model: (cachedData as any).model || '',
          specs: (cachedData as any).specs || {},
          price: (cachedData as any).price || 0,
          supplierId: (cachedData as any).supplierId,
          createdAt: cachedData.createdAt,
          updatedAt: cachedData.updatedAt,
          syncStatus: cachedData.syncStatus
        } as Equipment;
      }
    } catch (cacheError) {
      console.error('从缓存获取数据失败:', cacheError);
    }

    // 检查本地存储
    const key = `pv_equipment_${id}`;
    const localData = localStorage.getItem(key);
    let isServerData = false;

    // 如果本地有数据，检查是否是服务器数据
    if (localData) {
      try {
        const parsedData = JSON.parse(localData);
        if (parsedData.syncStatus === 'server-only') {
          isServerData = true;
          console.log('本地数据标记为服务器数据，需要从服务器获取');
        } else {
          console.log('从本地获取到数据, ID:', id, '同步状态:', parsedData.syncStatus);

          // 添加到缓存
          try {
            cacheManager.addItem(id, parsedData);
          } catch (cacheError) {
            console.error('添加到缓存失败:', cacheError);
          }

          return parsedData;
        }
      } catch (parseError) {
        console.error('解析本地数据失败:', parseError);
        isServerData = true;
      }
    } else {
      // 如果本地没有数据，则认为是服务器数据
      isServerData = true;
      console.log('本地没有数据，认为是服务器数据');
    }

    // 如果是服务器数据或本地数据无效，尝试从服务器获取
    if (isServerData || !localData) {
      console.log('尝试从服务器获取数据, ID:', id);
      try {
        // 从服务器获取数据
        const response = await get<ApiResponse<Equipment>>(`/equipment/${id}`);
        const serverData = response.data.data;

        // 验证服务器数据的有效性
        if (serverData) {
          console.log('从服务器获取到数据:', serverData.name);

          // 确保数据有正确的字段
          const completeData = {
            ...serverData,
            syncStatus: 'synced'
          };

          console.log('处理后的服务器数据:', completeData);

          // 保存到本地
          localStorage.setItem(key, JSON.stringify(completeData));

          // 添加到缓存
          try {
            // 将 Equipment 转换为 IrradianceData 以适配 cacheManager
            const irradianceData = {
              id: completeData.id,
              name: completeData.name,
              location: '',
              latitude: 0,
              longitude: 0,
              year: new Date().getFullYear(),
              dataSize: 0,
              syncStatus: completeData.syncStatus || 'synced',
              data: [],
              createdAt: completeData.createdAt,
              updatedAt: completeData.updatedAt,
              // 添加设备特有属性
              type: completeData.type,
              manufacturer: completeData.manufacturer,
              model: completeData.model,
              specs: completeData.specs,
              price: completeData.price,
              supplierId: completeData.supplierId
            };
            cacheManager.addItem(id, irradianceData);
          } catch (cacheError) {
            console.error('添加到缓存失败:', cacheError);
          }

          return completeData;
        } else {
          throw new Error('服务器数据无效');
        }
      } catch (serverError) {
        console.error('从服务器获取数据失败:', serverError);
        throw serverError;
      }
    }

    throw new Error('无法获取设备详情');
  } catch (error) {
    console.error('获取设备详情失败:', error);
    throw error;
  }
};

/**
 * 创建设备
 * @param data 设备数据
 * @returns Promise
 */
export const createEquipment = async (data: Partial<Equipment>): Promise<Equipment> => {
  try {
    // 生成ID
    const id = uuidv4();
    const now = new Date().toISOString();

    // 创建新的设备
    console.log('创建设备，输入数据:', data);

    const newEquipment: Equipment = {
      id,
      name: data.name || '',
      type: data.type || 'pv',
      manufacturer: data.manufacturer || '',
      model: data.model || '',
      specs: data.specs || {},
      price: data.price || 0,
      supplierId: data.supplierId,
      createdAt: now,
      updatedAt: now,
      syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
    };

    console.log('创建的新设备:', newEquipment);

    // 保存到本地
    const key = `pv_equipment_${id}`;
    localStorage.setItem(key, JSON.stringify(newEquipment));

    // 检查是否有临时附件需要关联
    const tempAttachmentsKey = 'pv_temp_attachments';
    const tempAttachmentsData = localStorage.getItem(tempAttachmentsKey);
    if (tempAttachmentsData) {
      try {
        const tempAttachments = JSON.parse(tempAttachmentsData);
        const now = new Date().getTime();
        const oneHourAgo = now - (60 * 60 * 1000); // 1小时前的时间戳

        // 过滤出最近1小时内上传的临时附件
        const recentAttachments = tempAttachments.filter((item: any) => {
          const timestamp = new Date(item.timestamp).getTime();
          return timestamp > oneHourAgo;
        });

        if (recentAttachments.length > 0) {
          console.log('找到临时附件，准备关联到新设备');

          // 获取附件数据
          const attachments = [];
          for (const item of recentAttachments) {
            const attachmentKey = `pv_attachment_${item.attachmentId}`;
            const attachmentData = localStorage.getItem(attachmentKey);
            if (attachmentData) {
              try {
                const attachment = JSON.parse(attachmentData);
                attachments.push(attachment);
              } catch (error) {
                console.error('解析附件数据失败:', error);
              }
            }
          }

          if (attachments.length > 0) {
            console.log('关联附件到新设备:', attachments);

            // 更新设备数据
            newEquipment.attachments = attachments;
            localStorage.setItem(key, JSON.stringify(newEquipment));

            // 更新临时附件列表，移除已关联的附件
            const updatedTempAttachments = tempAttachments.filter((item: any) => {
              return !recentAttachments.some((recent: any) => recent.attachmentId === item.attachmentId);
            });
            localStorage.setItem(tempAttachmentsKey, JSON.stringify(updatedTempAttachments));
          }
        }
      } catch (error) {
        console.error('处理临时附件失败:', error);
      }
    }

    // 尝试同步到服务器
    try {
      const response = await post<ApiResponse<Equipment>>('/equipment', newEquipment);
      const serverData = response.data.data;

      // 更新同步状态
      newEquipment.syncStatus = 'synced';
      localStorage.setItem(key, JSON.stringify(newEquipment));

      // 添加到缓存
      try {
        // 将 Equipment 转换为 IrradianceData 以适配 cacheManager
        const irradianceData = {
          id: newEquipment.id,
          name: newEquipment.name,
          location: '',
          latitude: 0,
          longitude: 0,
          year: new Date().getFullYear(),
          dataSize: 0,
          syncStatus: newEquipment.syncStatus || 'local-only',
          data: [],
          createdAt: newEquipment.createdAt,
          updatedAt: newEquipment.updatedAt,
          // 添加设备特有属性
          type: newEquipment.type,
          manufacturer: newEquipment.manufacturer,
          model: newEquipment.model,
          specs: newEquipment.specs,
          price: newEquipment.price,
          supplierId: newEquipment.supplierId
        };
        cacheManager.addItem(id, irradianceData);
      } catch (cacheError) {
        console.error('添加到缓存失败:', cacheError);
      }

      message.success('设备已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      message.warning('同步到服务器失败，数据将保存在本地');
    }

    return newEquipment;
  } catch (error) {
    console.error('创建设备失败:', error);
    throw error;
  }
};

/**
 * 更新设备
 * @param id 设备ID
 * @param data 更新的数据
 * @returns Promise
 */
export const updateEquipment = async (id: string, data: Partial<Equipment>): Promise<Equipment> => {
  try {
    // 获取现有数据
    const key = `pv_equipment_${id}`;
    const localData = localStorage.getItem(key);
    let existingData: Equipment;

    if (localData) {
      existingData = JSON.parse(localData);
    } else {
      // 如果本地没有数据，尝试从服务器获取
      const response = await get<ApiResponse<Equipment>>(`/equipment/${id}`);
      existingData = response.data.data;
    }

    console.log('更新设备，现有数据:', existingData);
    console.log('更新设备，新数据:', data);

    // 更新数据
    const updatedData: Equipment = {
      ...existingData,
      ...data,
      id, // 确保ID不变
      updatedAt: new Date().toISOString(),
      syncStatus: 'local-only' as 'synced' | 'local-only' | 'server-only',
    };

    console.log('更新后的数据:', updatedData);

    // 保存到本地
    localStorage.setItem(key, JSON.stringify(updatedData));

    // 尝试同步到服务器
    try {
      const response = await put<ApiResponse<Equipment>>(`/equipment/${id}`, updatedData);
      const serverData = response.data.data;

      // 更新同步状态
      updatedData.syncStatus = 'synced';
      localStorage.setItem(key, JSON.stringify(updatedData));

      // 更新缓存
      try {
        // 将 Equipment 转换为 IrradianceData 以适配 cacheManager
        const irradianceData = {
          id: updatedData.id,
          name: updatedData.name,
          location: '',
          latitude: 0,
          longitude: 0,
          year: new Date().getFullYear(),
          dataSize: 0,
          syncStatus: updatedData.syncStatus || 'synced',
          data: [],
          createdAt: updatedData.createdAt,
          updatedAt: updatedData.updatedAt,
          // 添加设备特有属性
          type: updatedData.type,
          manufacturer: updatedData.manufacturer,
          model: updatedData.model,
          specs: updatedData.specs,
          price: updatedData.price,
          supplierId: updatedData.supplierId
        };
        cacheManager.addItem(id, irradianceData);
      } catch (cacheError) {
        console.error('更新缓存失败:', cacheError);
      }

      message.success('设备已成功同步到服务器');
    } catch (uploadError) {
      console.error('同步到服务器失败，数据将保持本地状态:', uploadError);
      message.warning('同步到服务器失败，数据将保存在本地');
    }

    return updatedData;
  } catch (error) {
    console.error('更新设备失败:', error);
    throw error;
  }
};

/**
 * 删除设备
 * @param id 设备ID
 * @returns Promise
 */
export const deleteEquipment = async (id: string): Promise<boolean> => {
  try {
    console.log('开始删除设备, ID:', id);

    // 从本地删除
    const key = `pv_equipment_${id}`;
    localStorage.removeItem(key);

    // 从缓存删除
    try {
      cacheManager.removeItem(id);
    } catch (cacheError) {
      console.error('从缓存删除失败:', cacheError);
    }

    // 尝试从服务器删除
    try {
      await del<ApiResponse<boolean>>(`/equipment/${id}`);
      console.log('已从服务器删除设备, ID:', id);
      message.success('设备已成功删除');
    } catch (deleteError) {
      console.error('从服务器删除失败:', deleteError);
      message.warning('从服务器删除失败，但已从本地删除');
    }

    return true;
  } catch (error) {
    console.error('删除设备失败:', error);
    throw error;
  }
};
