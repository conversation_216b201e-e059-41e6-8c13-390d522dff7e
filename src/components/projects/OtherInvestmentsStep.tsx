import React, { useEffect, useState } from 'react';
import { Table, Button, Input, Select, Card, Form, InputNumber, Space, Alert, Modal, Row, Col } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { OtherInvestment } from '../../types';

const { Option } = Select;
const { TextArea } = Input;

interface OtherInvestmentsStepProps {
  data?: OtherInvestment[];
  onChange: (data: OtherInvestment[]) => void;
  onValidate: (valid: boolean) => void;
}

const OtherInvestmentsStep: React.FC<OtherInvestmentsStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  const [investments, setInvestments] = useState<OtherInvestment[]>(data || []);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingInvestment, setEditingInvestment] = useState<OtherInvestment | null>(null);
  const [searchText, setSearchText] = useState('');

  // 初始化数据
  useEffect(() => {
    if (data) {
      setInvestments(data);
    }
  }, [data]);

  // 验证数据
  useEffect(() => {
    // 其他投资是可选的，所以始终返回true
    onValidate(true);
  }, [investments, onValidate]);

  // 处理添加投资
  const handleAddInvestment = () => {
    setEditingInvestment(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑投资
  const handleEditInvestment = (investment: OtherInvestment) => {
    setEditingInvestment(investment);
    form.setFieldsValue(investment);
    setModalVisible(true);
  };

  // 处理删除投资
  const handleDeleteInvestment = (id: string) => {
    const newInvestments = investments.filter(investment => investment.id !== id);
    setInvestments(newInvestments);
    onChange(newInvestments);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingInvestment) {
        // 更新现有投资
        const newInvestments = investments.map(investment => 
          investment.id === editingInvestment.id ? { ...investment, ...values } : investment
        );
        setInvestments(newInvestments);
        onChange(newInvestments);
      } else {
        // 添加新投资
        const newInvestment: OtherInvestment = {
          id: Date.now().toString(),
          ...values,
        };
        const newInvestments = [...investments, newInvestment];
        setInvestments(newInvestments);
        onChange(newInvestments);
      }
      
      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 过滤投资
  const filteredInvestments = investments.filter(investment => 
    investment.name.toLowerCase().includes(searchText.toLowerCase()) ||
    investment.category.toLowerCase().includes(searchText.toLowerCase()) ||
    (investment.description && investment.description.toLowerCase().includes(searchText.toLowerCase()))
  );

  // 表格列定义
  const columns = [
    {
      title: t('otherInvestments.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('otherInvestments.category'),
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => t(`otherInvestments.categories.${category}`) || category,
    },
    {
      title: t('otherInvestments.price') + ' (JPY)',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => price.toLocaleString(),
    },
    {
      title: t('otherInvestments.description'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (text: string, record: OtherInvestment) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditInvestment(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteInvestment(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 投资类别选项
  const categoryOptions = [
    { value: 'installation', label: t('otherInvestments.categories.installation') },
    { value: 'design', label: t('otherInvestments.categories.design') },
    { value: 'permit', label: t('otherInvestments.categories.permit') },
    { value: 'construction', label: t('otherInvestments.categories.construction') },
    { value: 'maintenance', label: t('otherInvestments.categories.maintenance') },
    { value: 'other', label: t('otherInvestments.categories.other') },
  ];

  // 渲染投资表单
  const renderInvestmentForm = () => {
    return (
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={t('otherInvestments.name')}
              rules={[{ required: true, message: t('otherInvestments.nameRequired') }]}
            >
              <Input placeholder={t('otherInvestments.namePlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="category"
              label={t('otherInvestments.category')}
              rules={[{ required: true, message: t('otherInvestments.categoryRequired') }]}
            >
              <Select placeholder={t('otherInvestments.selectCategory')}>
                {categoryOptions.map(option => (
                  <Option key={option.value} value={option.value}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="price"
              label={t('otherInvestments.price') + ' (JPY)'}
              rules={[{ required: true, message: t('otherInvestments.priceRequired') }]}
            >
              <InputNumber
                min={0}
                step={1000}
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="description"
              label={t('otherInvestments.description')}
            >
              <TextArea
                rows={4}
                placeholder={t('otherInvestments.descriptionPlaceholder')}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  // 计算总投资
  const totalInvestment = investments.reduce((sum, investment) => sum + investment.price, 0);

  // 按类别分组投资
  const investmentsByCategory = investments.reduce((acc, investment) => {
    const category = investment.category;
    if (!acc[category]) {
      acc[category] = 0;
    }
    acc[category] += investment.price;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div>
      <Alert
        message={t('projectWizard.optional')}
        description={t('projectWizard.otherInvestmentsDescription')}
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Input
          placeholder={t('common.search')}
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: '300px' }}
        />
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddInvestment}
        >
          {t('otherInvestments.addInvestment')}
        </Button>
      </div>

      <Table
        dataSource={filteredInvestments}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 5 }}
        locale={{ emptyText: t('otherInvestments.noInvestments') }}
      />

      {investments.length > 0 && (
        <Card style={{ marginTop: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('otherInvestments.summary')}</div>
              <div>{t('otherInvestments.totalItems')}: {investments.length}</div>
              <div style={{ marginTop: '8px' }}>
                {Object.entries(investmentsByCategory).map(([category, amount]) => (
                  <div key={category}>
                    {t(`otherInvestments.categories.${category}`) || category}: {amount.toLocaleString()} JPY
                  </div>
                ))}
              </div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('otherInvestments.totalInvestment')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {totalInvestment.toLocaleString()} JPY
              </div>
            </div>
          </div>
        </Card>
      )}

      <Modal
        title={editingInvestment ? t('otherInvestments.editInvestment') : t('otherInvestments.addInvestment')}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        {renderInvestmentForm()}
      </Modal>
    </div>
  );
};

export default OtherInvestmentsStep;
