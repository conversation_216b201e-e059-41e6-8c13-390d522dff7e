import React, { useEffect, useState } from 'react';
import { Table, Button, Input, Select, Card, Form, InputNumber, Space, Modal, Row, Col, Divider, Alert } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { PVModule } from '../../types';
import { fetchDataStart, fetchEquipmentSuccess } from '../../store/slices/databasesSlice';
import { getEquipmentList } from '../../services/equipmentService';
import { StepAlert } from '../../components/common';

const { Option } = Select;

interface PVModulesStepProps {
  data?: PVModule[];
  onChange: (data: PVModule[]) => void;
  onValidate: (valid: boolean) => void;
}

const PVModulesStep: React.FC<PVModulesStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const { equipment } = useAppSelector((state) => state.databases);

  const [modules, setModules] = useState<PVModule[]>(data || []);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingModule, setEditingModule] = useState<PVModule | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedEquipment, setSelectedEquipment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 过滤设备列表，只显示光伏设备，并确保specs字段存在
  const pvEquipment = equipment
    .filter(item => item.type === 'pv')
    .map(item => {
      console.log('处理光伏设备:', item.name, '原始specs:', item.specs);
      return {
        ...item,
        specs: item.specs || {}
      };
    });

  console.log('过滤后的光伏设备列表:', pvEquipment);

  // 获取设备列表
  const fetchEquipmentData = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getEquipmentList();
      dispatch(fetchEquipmentSuccess(response.items));
      console.log('PVModulesStep: 成功获取设备列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('PVModulesStep: 获取设备列表失败:', error);
      dispatch(fetchEquipmentSuccess([]));
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    // 如果Redux store中没有数据，则从服务器获取
    if (equipment.length === 0) {
      console.log('PVModulesStep: Redux store中没有设备数据，从服务器获取');
      fetchEquipmentData();
    } else {
      console.log('PVModulesStep: Redux store中已有设备数据，数据条数:', equipment.length);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 初始化数据 - 只在组件挂载时执行一次
  useEffect(() => {
    console.log('PVModulesStep: 初始化模块数据', data);
    if (data) {
      setModules(data);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 验证数据
  useEffect(() => {
    onValidate(modules.length > 0);
  }, [modules, onValidate]);

  // 处理添加模块
  const handleAddModule = () => {
    setEditingModule(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑模块
  const handleEditModule = (module: PVModule) => {
    setEditingModule(module);
    form.setFieldsValue(module);
    setModalVisible(true);
  };

  // 处理删除模块
  const handleDeleteModule = (id: string) => {
    console.log('PVModulesStep: 删除模块', id, '当前模块数量:', modules.length);
    const newModules = modules.filter(module => module.id !== id);
    console.log('PVModulesStep: 删除后模块数量:', newModules.length);
    setModules(newModules);
    onChange(newModules);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('PVModulesStep: 表单提交，当前模块数量:', modules.length);

      if (editingModule) {
        // 更新现有模块
        console.log('PVModulesStep: 更新模块', editingModule.id);
        const newModules = modules.map(module =>
          module.id === editingModule.id ? { ...module, ...values } : module
        );
        console.log('PVModulesStep: 更新后模块数量:', newModules.length);
        setModules(newModules);
        onChange(newModules);
      } else {
        // 添加新模块
        const newModule: PVModule = {
          id: Date.now().toString(),
          ...values,
        };
        console.log('PVModulesStep: 添加新模块', newModule.id);
        const newModules = [...modules, newModule];
        console.log('PVModulesStep: 添加后模块数量:', newModules.length);
        setModules(newModules);
        onChange(newModules);
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理设备选择
  const handleEquipmentSelect = (equipmentId: string) => {
    const selected = equipment.find(item => item.id === equipmentId);
    if (selected) {
      // 确保specs字段存在
      const specs = selected.specs || {};
      setSelectedEquipment(selected);

      // 填充表单
      form.setFieldsValue({
        name: selected.name || '',
        manufacturer: selected.manufacturer || '',
        model: selected.model || '',
        power: specs.power || 0,
        efficiency: specs.efficiency || 0,
        area: specs.area || 0,
        price: selected.price || 0,
        quantity: 1,
        angle: 30,
        orientation: 'south',
        orientationAngle: 0, // 默认正南方向
      });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('pvModules.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('pvModules.manufacturer'),
      dataIndex: 'manufacturer',
      key: 'manufacturer',
    },
    {
      title: t('pvModules.model'),
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: t('pvModules.power') + ' (W)',
      dataIndex: 'power',
      key: 'power',
    },
    {
      title: t('pvModules.efficiency') + ' (%)',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: t('pvModules.area') + ' (m²)',
      dataIndex: 'area',
      key: 'area',
      render: (value: number) => value.toFixed(1),
    },
    {
      title: t('pvModules.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: t('pvModules.totalPower') + ' (kW)',
      key: 'totalPower',
      render: (text: string, record: PVModule) => ((record.power * record.quantity) / 1000).toFixed(1),
    },
    {
      title: t('pvModules.totalArea') + ' (m²)',
      key: 'totalArea',
      render: (text: string, record: PVModule) => (record.area * record.quantity).toFixed(1),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (text: string, record: PVModule) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditModule(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteModule(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 渲染模块表单
  const renderModuleForm = () => {
    return (
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="equipmentId"
              label={t('pvModules.selectEquipment')}
            >
              <Select
                placeholder={t('pvModules.selectEquipmentPlaceholder')}
                onChange={handleEquipmentSelect}
                showSearch
                filterOption={(input, option) =>
                  option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {pvEquipment.map(item => (
                  <Option key={item.id} value={item.id}>{item.name} - {item.manufacturer}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('pvModules.basicInfo')}</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={t('pvModules.name')}
              rules={[{ required: true, message: t('pvModules.nameRequired') }]}
            >
              <Input placeholder={t('pvModules.namePlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="manufacturer"
              label={t('pvModules.manufacturer')}
              rules={[{ required: true, message: t('pvModules.manufacturerRequired') }]}
            >
              <Input placeholder={t('pvModules.manufacturerPlaceholder')} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="model"
              label={t('pvModules.model')}
              rules={[{ required: true, message: t('pvModules.modelRequired') }]}
            >
              <Input placeholder={t('pvModules.modelPlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="quantity"
              label={t('pvModules.quantity')}
              rules={[{ required: true, message: t('pvModules.quantityRequired') }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('pvModules.specifications')}</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="power"
              label={t('pvModules.power') + ' (W)'}
              rules={[{ required: true, message: t('pvModules.powerRequired') }]}
            >
              <InputNumber min={0} step={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="efficiency"
              label={t('pvModules.efficiency') + ' (%)'}
              rules={[{ required: true, message: t('pvModules.efficiencyRequired') }]}
            >
              <InputNumber min={0} max={100} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="area"
              label={t('pvModules.area') + ' (m²)'}
              rules={[{ required: true, message: t('pvModules.areaRequired') }]}
            >
              <InputNumber min={0} step={0.01} precision={2} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="price"
              label={t('pvModules.price') + ' (JPY)'}
              rules={[{ required: true, message: t('pvModules.priceRequired') }]}
            >
              <InputNumber min={0} step={1000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="angle"
              label={t('pvModules.angle') + ' (°)'}
              rules={[{ required: true, message: t('pvModules.angleRequired') }]}
            >
              <InputNumber min={0} max={90} step={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="orientation"
              label={t('pvModules.orientation')}
              rules={[{ required: true, message: t('pvModules.orientationRequired') }]}
            >
              <Select
                placeholder={t('pvModules.selectOrientation')}
                onChange={(value) => {
                  // 如果选择自定义，则需要手动输入角度
                  if (value === 'custom') {
                    // 默认设置为0度（正南）
                    form.setFieldsValue({ orientationAngle: 0 });
                  } else {
                    // 根据选择的方向设置角度
                    let angle = 0;
                    switch(value) {
                      case 'north': angle = 180; break;
                      case 'northeast': angle = -135; break;
                      case 'east': angle = -90; break;
                      case 'southeast': angle = -45; break;
                      case 'south': angle = 0; break;
                      case 'southwest': angle = 45; break;
                      case 'west': angle = 90; break;
                      case 'northwest': angle = 135; break;
                      default: angle = 0;
                    }
                    form.setFieldsValue({ orientationAngle: angle });
                  }
                }}
              >
                <Option value="north">{t('pvModules.north')}</Option>
                <Option value="northeast">{t('pvModules.northeast')}</Option>
                <Option value="east">{t('pvModules.east')}</Option>
                <Option value="southeast">{t('pvModules.southeast')}</Option>
                <Option value="south">{t('pvModules.south')}</Option>
                <Option value="southwest">{t('pvModules.southwest')}</Option>
                <Option value="west">{t('pvModules.west')}</Option>
                <Option value="northwest">{t('pvModules.northwest')}</Option>
                <Option value="custom">{t('pvModules.custom')}</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="orientationAngle"
              label={t('pvModules.orientationAngle') + ' (°)'}
              tooltip={t('pvModules.orientationAngleHelp')}
              initialValue={0}
              rules={[{ required: true, message: t('pvModules.orientationRequired') }]}
            >
              <InputNumber
                min={-180}
                max={180}
                step={1}
                style={{ width: '100%' }}
                disabled={form.getFieldValue('orientation') !== 'custom'}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  // 获取未完成的必填字段
  const getIncompleteFields = () => {
    const incompleteFields: string[] = [];

    try {
      // 检查是否有任何设备
      const hasModules = Array.isArray(modules) && modules.length > 0;
      const hasData = Array.isArray(data) && data.length > 0;

      const hasAnyEquipment = hasModules || hasData;

      if (!hasAnyEquipment) {
        incompleteFields.push(t('pvModules.addModule'));
      }

      console.log('PVModulesStep - 未完成字段检查:', {
        hasModules,
        hasData,
        hasAnyEquipment,
        incompleteFields
      });
    } catch (error) {
      console.error('获取未完成字段时出错:', error);
    }

    return incompleteFields;
  };

  return (
    <div>
      <StepAlert
        message={t('projectWizard.required')}
        description={t('projectWizard.pvModulesDescription')}
        type="info"
        showIcon
        incompleteFields={getIncompleteFields()}
      />

      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Input
            placeholder={t('common.search')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '300px' }}
          />
          <Button
            type="text"
            icon={<SyncOutlined spin={isLoading} />}
            onClick={fetchEquipmentData}
            disabled={isLoading}
          >
            {t('common.refresh')}
          </Button>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddModule}
        >
          {t('pvModules.addModule')}
        </Button>
      </div>

      <Card
        title={t('pvModules.equipmentList')}
        style={{ marginBottom: '16px' }}
      >
        <Table
          dataSource={pvEquipment}
          columns={[
            {
              title: t('pvModules.name'),
              dataIndex: 'name',
              key: 'name',
              render: (text: string) => text || '-',
            },
            {
              title: t('pvModules.manufacturer'),
              dataIndex: 'manufacturer',
              key: 'manufacturer',
              render: (text: string) => text || '-',
            },
            {
              title: t('pvModules.model'),
              dataIndex: 'model',
              key: 'model',
              render: (text: string) => text || '-',
            },
            {
              title: t('pvModules.power') + ' (W)',
              key: 'power',
              render: (_: any, record: any) => {
                try {
                  return record.specs && typeof record.specs.power !== 'undefined'
                    ? record.specs.power
                    : '-';
                } catch (error) {
                  console.error('渲染功率时出错:', error, '记录:', record);
                  return '-';
                }
              },
            },
            {
              title: t('common.actions'),
              key: 'actions',
              render: (_: any, record: any) => {
                try {
                  return (
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => {
                        console.log('选择设备:', record);
                        setSelectedEquipment(record);
                        form.resetFields();

                        // 确保specs字段存在
                        const specs = record.specs || {};

                        form.setFieldsValue({
                          name: record.name || '',
                          manufacturer: record.manufacturer || '',
                          model: record.model || '',
                          power: specs.power || 0,
                          efficiency: specs.efficiency || 0,
                          area: specs.area || 0,
                          price: record.price || 0,
                          quantity: 1,
                          angle: 30,
                          orientation: 'south',
                          orientationAngle: 0, // 默认正南方向
                        });
                        setModalVisible(true);
                      }}
                    >
                      {t('common.select')}
                    </Button>
                  );
                } catch (error) {
                  console.error('渲染操作按钮时出错:', error, '记录:', record);
                  return <span>错误</span>;
                }
              },
            },
          ]}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          loading={isLoading}
          locale={{ emptyText: t('pvModules.noEquipment') }}
        />
      </Card>

      <Divider>{t('pvModules.selectedModules')}</Divider>

      <Table
        dataSource={modules}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 5 }}
        locale={{ emptyText: t('pvModules.noModules') }}
      />

      {modules.length > 0 && (
        <Card style={{ marginTop: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('pvModules.summary')}</div>
              <div>{t('pvModules.totalModules')}: {modules.length}</div>
              <div>{t('pvModules.totalQuantity')}: {modules.reduce((sum, module) => sum + module.quantity, 0)}</div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('pvModules.totalPower')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {(modules.reduce((sum, module) => sum + (module.power * module.quantity), 0) / 1000).toFixed(1)} kW
              </div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('pvModules.totalArea')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {modules.reduce((sum, module) => sum + (module.area * module.quantity), 0).toFixed(1)} m²
              </div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('pvModules.totalCost')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {modules.reduce((sum, module) => sum + (module.price * module.quantity), 0).toLocaleString()} JPY
              </div>
            </div>
          </div>
        </Card>
      )}

      {modules.length === 0 && (
        <Alert
          message={t('projectWizard.dataRequired')}
          description={t('projectWizard.pvModulesRequired')}
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}

      <Modal
        title={editingModule ? t('pvModules.editModule') : t('pvModules.addModule')}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        {renderModuleForm()}
      </Modal>
    </div>
  );
};

export default PVModulesStep;
