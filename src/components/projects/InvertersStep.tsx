import React, { useEffect, useState } from 'react';
import { Table, Button, Input, Select, Card, Form, InputNumber, Space, Alert, Modal, Row, Col, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../../store';
import { Inverter } from '../../types';
import { fetchDataStart, fetchEquipmentSuccess } from '../../store/slices/databasesSlice';
import { getEquipmentList } from '../../services/equipmentService';

const { Option } = Select;

interface InvertersStepProps {
  data?: Inverter[];
  onChange: (data: Inverter[]) => void;
  onValidate: (valid: boolean) => void;
}

const InvertersStep: React.FC<InvertersStepProps> = ({ data, onChange, onValidate }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const { equipment } = useAppSelector((state) => state.databases);

  const [inverters, setInverters] = useState<Inverter[]>(data || []);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingInverter, setEditingInverter] = useState<Inverter | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedEquipment, setSelectedEquipment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 过滤设备列表，只显示逆变器
  const inverterEquipment = equipment.filter(item => item.type === 'inverter');

  // 获取设备列表
  const fetchEquipmentData = async () => {
    try {
      setIsLoading(true);
      dispatch(fetchDataStart());
      const response = await getEquipmentList();
      dispatch(fetchEquipmentSuccess(response.items));
      console.log('InvertersStep: 成功获取设备列表，数据条数:', response.items.length);
    } catch (error) {
      console.error('InvertersStep: 获取设备列表失败:', error);
      dispatch(fetchEquipmentSuccess([]));
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    // 如果Redux store中没有数据，则从服务器获取
    if (equipment.length === 0) {
      console.log('InvertersStep: Redux store中没有设备数据，从服务器获取');
      fetchEquipmentData();
    } else {
      console.log('InvertersStep: Redux store中已有设备数据，数据条数:', equipment.length);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 初始化数据
  useEffect(() => {
    if (data) {
      setInverters(data);
    }
  }, [data]);

  // 验证数据
  useEffect(() => {
    onValidate(inverters.length > 0);
  }, [inverters, onValidate]);

  // 处理添加逆变器
  const handleAddInverter = () => {
    setEditingInverter(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑逆变器
  const handleEditInverter = (inverter: Inverter) => {
    setEditingInverter(inverter);
    form.setFieldsValue(inverter);
    setModalVisible(true);
  };

  // 处理删除逆变器
  const handleDeleteInverter = (id: string) => {
    const newInverters = inverters.filter(inverter => inverter.id !== id);
    setInverters(newInverters);
    onChange(newInverters);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingInverter) {
        // 更新现有逆变器
        const newInverters = inverters.map(inverter =>
          inverter.id === editingInverter.id ? { ...inverter, ...values } : inverter
        );
        setInverters(newInverters);
        onChange(newInverters);
      } else {
        // 添加新逆变器
        const newInverter: Inverter = {
          id: Date.now().toString(),
          ...values,
        };
        const newInverters = [...inverters, newInverter];
        setInverters(newInverters);
        onChange(newInverters);
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理设备选择
  const handleEquipmentSelect = (equipmentId: string) => {
    const selected = equipment.find(item => item.id === equipmentId);
    if (selected) {
      setSelectedEquipment(selected);

      // 填充表单
      form.setFieldsValue({
        name: selected.name,
        manufacturer: selected.manufacturer,
        model: selected.model,
        power: selected.specs.power,
        efficiency: selected.specs.efficiency,
        price: selected.price,
        quantity: 1,
      });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('inverters.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('inverters.manufacturer'),
      dataIndex: 'manufacturer',
      key: 'manufacturer',
    },
    {
      title: t('inverters.model'),
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: t('inverters.power') + ' (kW)',
      dataIndex: 'power',
      key: 'power',
    },
    {
      title: t('inverters.efficiency') + ' (%)',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (value: number) => `${value.toFixed(1)}%`,
    },
    {
      title: t('inverters.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: t('inverters.totalPower') + ' (kW)',
      key: 'totalPower',
      render: (text: string, record: Inverter) => (record.power * record.quantity).toFixed(1),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (text: string, record: Inverter) => (
        <Space>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditInverter(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteInverter(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 渲染逆变器表单
  const renderInverterForm = () => {
    return (
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="equipmentId"
              label={t('inverters.selectEquipment')}
            >
              <Select
                placeholder={t('inverters.selectEquipmentPlaceholder')}
                onChange={handleEquipmentSelect}
                showSearch
                filterOption={(input, option) =>
                  option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {inverterEquipment.map(item => (
                  <Option key={item.id} value={item.id}>{item.name} - {item.manufacturer}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('inverters.basicInfo')}</Divider>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={t('inverters.name')}
              rules={[{ required: true, message: t('inverters.nameRequired') }]}
            >
              <Input placeholder={t('inverters.namePlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="manufacturer"
              label={t('inverters.manufacturer')}
              rules={[{ required: true, message: t('inverters.manufacturerRequired') }]}
            >
              <Input placeholder={t('inverters.manufacturerPlaceholder')} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="model"
              label={t('inverters.model')}
              rules={[{ required: true, message: t('inverters.modelRequired') }]}
            >
              <Input placeholder={t('inverters.modelPlaceholder')} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="quantity"
              label={t('inverters.quantity')}
              rules={[{ required: true, message: t('inverters.quantityRequired') }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Divider>{t('inverters.specifications')}</Divider>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="power"
              label={t('inverters.power') + ' (kW)'}
              rules={[{ required: true, message: t('inverters.powerRequired') }]}
            >
              <InputNumber min={0} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="efficiency"
              label={t('inverters.efficiency') + ' (%)'}
              rules={[{ required: true, message: t('inverters.efficiencyRequired') }]}
            >
              <InputNumber min={0} max={100} step={0.1} precision={1} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="price"
              label={t('inverters.price') + ' (JPY)'}
              rules={[{ required: true, message: t('inverters.priceRequired') }]}
            >
              <InputNumber min={0} step={1000} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  return (
    <div>
      <Alert
        message={t('projectWizard.required')}
        description={t('projectWizard.invertersDescription')}
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Input
            placeholder={t('common.search')}
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '300px' }}
          />
          <Button
            type="text"
            icon={<SyncOutlined spin={isLoading} />}
            onClick={fetchEquipmentData}
            disabled={isLoading}
          >
            {t('common.refresh')}
          </Button>
        </Space>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddInverter}
        >
          {t('inverters.addInverter')}
        </Button>
      </div>

      <Card
        title={t('inverters.equipmentList')}
        style={{ marginBottom: '16px' }}
      >
        <Table
          dataSource={inverterEquipment}
          columns={[
            {
              title: t('inverters.name'),
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: t('inverters.manufacturer'),
              dataIndex: 'manufacturer',
              key: 'manufacturer',
            },
            {
              title: t('inverters.model'),
              dataIndex: 'model',
              key: 'model',
            },
            {
              title: t('inverters.power') + ' (kW)',
              dataIndex: 'specs.power',
              key: 'power',
              render: (text: string, record: any) => record.specs?.power || '-',
            },
            {
              title: t('common.actions'),
              key: 'actions',
              render: (text: string, record: any) => (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    setSelectedEquipment(record);
                    form.resetFields();
                    form.setFieldsValue({
                      name: record.name,
                      manufacturer: record.manufacturer,
                      model: record.model,
                      power: record.specs.power,
                      efficiency: record.specs.efficiency,
                      price: record.price,
                      quantity: 1,
                    });
                    setModalVisible(true);
                  }}
                >
                  {t('common.select')}
                </Button>
              ),
            },
          ]}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          loading={isLoading}
          locale={{ emptyText: t('inverters.noEquipment') }}
        />
      </Card>

      <Divider>{t('inverters.selectedInverters')}</Divider>

      <Table
        dataSource={inverters}
        columns={columns}
        rowKey="id"
        pagination={{ pageSize: 5 }}
        locale={{ emptyText: t('inverters.noInverters') }}
      />

      {inverters.length > 0 && (
        <Card style={{ marginTop: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('inverters.summary')}</div>
              <div>{t('inverters.totalInverters')}: {inverters.length}</div>
              <div>{t('inverters.totalQuantity')}: {inverters.reduce((sum, inverter) => sum + inverter.quantity, 0)}</div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('inverters.totalPower')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {inverters.reduce((sum, inverter) => sum + (inverter.power * inverter.quantity), 0).toFixed(1)} kW
              </div>
            </div>
            <div>
              <div style={{ fontSize: '16px', fontWeight: 'bold' }}>{t('inverters.totalCost')}</div>
              <div style={{ fontSize: '24px', color: '#1890ff' }}>
                {inverters.reduce((sum, inverter) => sum + (inverter.price * inverter.quantity), 0).toLocaleString()} JPY
              </div>
            </div>
          </div>
        </Card>
      )}

      {inverters.length === 0 && (
        <Alert
          message={t('projectWizard.dataRequired')}
          description={t('projectWizard.invertersRequired')}
          type="warning"
          showIcon
          style={{ marginTop: '16px' }}
        />
      )}

      <Modal
        title={editingInverter ? t('inverters.editInverter') : t('inverters.addInverter')}
        open={modalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
      >
        {renderInverterForm()}
      </Modal>
    </div>
  );
};

export default InvertersStep;
