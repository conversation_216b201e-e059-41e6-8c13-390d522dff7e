import React, { useState, useEffect } from 'react';
import { Card, Form, Switch, Button, Space, message, Divider, Typography, Select, Table, Tag, Progress, Input } from 'antd';
import { SaveOutlined, CloudUploadOutlined, CloudDownloadOutlined, HistoryOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { BackupSettings } from '../../types/settings';
import { ConfirmDialog } from '../common';

const { Title, Text } = Typography;
const { Option } = Select;

// 备份记录类型
interface BackupRecord {
  id: string;
  name: string;
  date: string;
  size: number;
  type: 'auto' | 'manual';
  status: 'success' | 'failed';
}

/**
 * 备份设置面板组件
 */
const BackupSettingsPanel: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [backupRecords, setBackupRecords] = useState<BackupRecord[]>([]);
  const [backupProgress, setBackupProgress] = useState(0);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<BackupRecord | null>(null);

  // 模拟初始备份设置
  const initialSettings: BackupSettings = {
    autoBackup: true,
    backupInterval: 'weekly',
    backupTime: '02:00',
    maxBackups: 5
  };

  // 模拟获取备份记录
  useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      const mockRecords: BackupRecord[] = [
        {
          id: '1',
          name: '自动备份',
          date: new Date(Date.now() - 86400000).toISOString(), // 1天前
          size: 1024 * 1024 * 5, // 5MB
          type: 'auto',
          status: 'success'
        },
        {
          id: '2',
          name: '手动备份',
          date: new Date(Date.now() - 604800000).toISOString(), // 1周前
          size: 1024 * 1024 * 4.5, // 4.5MB
          type: 'manual',
          status: 'success'
        },
        {
          id: '3',
          name: '自动备份',
          date: new Date(Date.now() - 1209600000).toISOString(), // 2周前
          size: 1024 * 1024 * 4.2, // 4.2MB
          type: 'auto',
          status: 'success'
        }
      ];
      setBackupRecords(mockRecords);
    }, 500);
  }, []);

  // 处理表单提交
  const handleSubmit = (values: BackupSettings) => {
    setIsLoading(true);

    // 模拟API调用
    setTimeout(() => {
      console.log('保存备份设置:', values);
      message.success(t('settings.backup.saveSuccess'));
      setIsLoading(false);
    }, 500);
  };

  // 立即备份
  const handleBackupNow = () => {
    setIsBackingUp(true);

    // 模拟备份进度
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setBackupProgress(progress);

      if (progress >= 100) {
        clearInterval(interval);
        setIsBackingUp(false);

        // 添加新的备份记录
        const newRecord: BackupRecord = {
          id: Date.now().toString(),
          name: '手动备份',
          date: new Date().toISOString(),
          size: 1024 * 1024 * (4 + Math.random()), // 4-5MB
          type: 'manual',
          status: 'success'
        };

        setBackupRecords([newRecord, ...backupRecords]);
        message.success(t('settings.backup.backupSuccess'));
      }
    }, 300);
  };

  // 处理恢复备份
  const handleRestore = (record: BackupRecord) => {
    message.info(t('settings.backup.restoreConfirm'));
    // 这里可以添加恢复备份的确认对话框
  };

  // 处理删除备份
  const handleDeleteBackup = (record: BackupRecord) => {
    setRecordToDelete(record);
    setDeleteModalVisible(true);
  };

  // 确认删除备份
  const confirmDeleteBackup = () => {
    if (recordToDelete) {
      // 模拟删除操作
      setBackupRecords(backupRecords.filter(record => record.id !== recordToDelete.id));
      message.success(t('settings.backup.deleteSuccess'));
      setDeleteModalVisible(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: t('settings.backup.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('settings.backup.date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => new Date(date).toLocaleString(),
      sorter: (a: BackupRecord, b: BackupRecord) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    },
    {
      title: t('settings.backup.size'),
      dataIndex: 'size',
      key: 'size',
      render: (size: number) => {
        const sizeInMB = size / 1024 / 1024;
        return `${sizeInMB.toFixed(2)} MB`;
      },
    },
    {
      title: t('settings.backup.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'auto' ? 'blue' : 'green'}>
          {type === 'auto' ? t('settings.backup.auto') : t('settings.backup.manual')}
        </Tag>
      ),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: BackupRecord) => (
        <Space>
          <Button
            icon={<CloudDownloadOutlined />}
            size="small"
            onClick={() => handleRestore(record)}
          >
            {t('settings.backup.restore')}
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDeleteBackup(record)}
          >
            {t('common.delete')}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={
        <Space>
          <SaveOutlined />
          {t('settings.backup.title')}
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialSettings}
        onFinish={handleSubmit}
      >
        <Title level={5}>{t('settings.backup.autoSettings')}</Title>

        {/* 自动备份 */}
        <Form.Item
          name="autoBackup"
          label={t('settings.backup.autoBackup')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        {/* 备份间隔 */}
        <Form.Item
          name="backupInterval"
          label={t('settings.backup.backupInterval')}
          dependencies={['autoBackup']}
          rules={[
            ({ getFieldValue }) => ({
              required: getFieldValue('autoBackup'),
              message: t('common.required')
            })
          ]}
        >
          <Select disabled={!form.getFieldValue('autoBackup')}>
            <Option value="daily">{t('settings.backup.daily')}</Option>
            <Option value="weekly">{t('settings.backup.weekly')}</Option>
            <Option value="monthly">{t('settings.backup.monthly')}</Option>
          </Select>
        </Form.Item>

        {/* 备份时间 */}
        <Form.Item
          name="backupTime"
          label={t('settings.backup.backupTime')}
          dependencies={['autoBackup']}
          rules={[
            ({ getFieldValue }) => ({
              required: getFieldValue('autoBackup'),
              message: t('common.required')
            })
          ]}
        >
          <Input
            placeholder="HH:mm"
            disabled={!form.getFieldValue('autoBackup')}
          />
        </Form.Item>

        {/* 最大备份数 */}
        <Form.Item
          name="maxBackups"
          label={t('settings.backup.maxBackups')}
          dependencies={['autoBackup']}
          rules={[
            ({ getFieldValue }) => ({
              required: getFieldValue('autoBackup'),
              message: t('common.required')
            })
          ]}
        >
          <Select disabled={!form.getFieldValue('autoBackup')}>
            <Option value={3}>3</Option>
            <Option value={5}>5</Option>
            <Option value={10}>10</Option>
            <Option value={20}>20</Option>
          </Select>
        </Form.Item>

        {/* 表单按钮 */}
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={isLoading}>
              {t('common.save')}
            </Button>
            <Button
              type="primary"
              icon={<CloudUploadOutlined />}
              onClick={handleBackupNow}
              loading={isBackingUp}
            >
              {t('settings.backup.backupNow')}
            </Button>
          </Space>
        </Form.Item>
      </Form>

      {isBackingUp && (
        <div style={{ marginBottom: '20px' }}>
          <Progress percent={backupProgress} status="active" />
        </div>
      )}

      <Divider />

      <Title level={5}>
        <Space>
          <HistoryOutlined />
          {t('settings.backup.backupHistory')}
        </Space>
      </Title>

      <Table
        columns={columns}
        dataSource={backupRecords}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />

      {/* 删除备份确认对话框 */}
      <ConfirmDialog
        title={t('settings.backup.deleteBackupTitle')}
        content={t('settings.backup.deleteBackupConfirm')}
        visible={deleteModalVisible}
        onConfirm={confirmDeleteBackup}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </Card>
  );
};

export default BackupSettingsPanel;
