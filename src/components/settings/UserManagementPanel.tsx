import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Form, Input, Select, Tag, message, Typography, Tooltip } from 'antd';
import { UserOutlined, PlusOutlined, EditOutlined, DeleteOutlined, LockOutlined, UnlockOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ConfirmDialog } from '../common';

const { Title, Text } = Typography;
const { Option } = Select;

// 用户类型定义
interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  status: 'active' | 'inactive';
  lastLogin: string;
}

/**
 * 用户管理面板组件
 */
const UserManagementPanel: React.FC = () => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [form] = Form.useForm();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // 模拟获取用户数据
  useEffect(() => {
    setIsLoading(true);
    // 模拟API调用
    setTimeout(() => {
      const mockUsers: User[] = [
        {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          lastLogin: new Date().toISOString()
        },
        {
          id: '2',
          username: 'user1',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          lastLogin: new Date(Date.now() - ********).toISOString() // 1天前
        },
        {
          id: '3',
          username: 'user2',
          email: '<EMAIL>',
          role: 'user',
          status: 'inactive',
          lastLogin: new Date(Date.now() - *********).toISOString() // 1周前
        }
      ];
      setUsers(mockUsers);
      setIsLoading(false);
    }, 500);
  }, []);

  // 处理添加用户
  const handleAddUser = () => {
    setModalTitle(t('settings.account.addUser'));
    setCurrentUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑用户
  const handleEditUser = (user: User) => {
    setModalTitle(t('settings.account.editUser'));
    setCurrentUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status
    });
    setModalVisible(true);
  };

  // 处理删除用户
  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setDeleteModalVisible(true);
  };

  // 确认删除用户
  const confirmDeleteUser = () => {
    if (userToDelete) {
      // 模拟删除操作
      setUsers(users.filter(user => user.id !== userToDelete.id));
      message.success(t('settings.account.userDeleted'));
      setDeleteModalVisible(false);
    }
  };

  // 处理表单提交
  const handleSubmit = (values: any) => {
    if (currentUser) {
      // 编辑现有用户
      const updatedUsers = users.map(user => 
        user.id === currentUser.id ? { ...user, ...values } : user
      );
      setUsers(updatedUsers);
      message.success(t('settings.account.userUpdated'));
    } else {
      // 添加新用户
      const newUser: User = {
        id: Date.now().toString(),
        username: values.username,
        email: values.email,
        role: values.role,
        status: values.status,
        lastLogin: '-'
      };
      setUsers([...users, newUser]);
      message.success(t('settings.account.userAdded'));
    }
    setModalVisible(false);
  };

  // 表格列定义
  const columns = [
    {
      title: t('settings.account.username'),
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: t('settings.account.email'),
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: t('settings.account.role'),
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={role === 'admin' ? 'red' : 'blue'}>
          {role === 'admin' ? t('settings.account.admin') : t('settings.account.user')}
        </Tag>
      ),
    },
    {
      title: t('settings.account.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'gray'}>
          {status === 'active' ? t('settings.account.active') : t('settings.account.inactive')}
        </Tag>
      ),
    },
    {
      title: t('settings.account.lastLogin'),
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (lastLogin: string) => lastLogin === '-' ? '-' : new Date(lastLogin).toLocaleString(),
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (_: any, record: User) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditUser(record)}
          >
            {t('common.edit')}
          </Button>
          {record.username !== 'admin' && (
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => handleDeleteUser(record)}
            >
              {t('common.delete')}
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={
        <Space>
          <UserOutlined />
          {t('settings.account.title')}
        </Space>
      }
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddUser}
        >
          {t('settings.account.addUser')}
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={users}
        rowKey="id"
        loading={isLoading}
        pagination={{ pageSize: 10 }}
      />

      {/* 用户表单模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ role: 'user', status: 'active' }}
        >
          <Form.Item
            name="username"
            label={t('settings.account.username')}
            rules={[{ required: true, message: t('common.required') }]}
          >
            <Input prefix={<UserOutlined />} />
          </Form.Item>

          <Form.Item
            name="email"
            label={t('settings.account.email')}
            rules={[
              { required: true, message: t('common.required') },
              { type: 'email', message: t('settings.account.invalidEmail') }
            ]}
          >
            <Input />
          </Form.Item>

          {!currentUser && (
            <Form.Item
              name="password"
              label={t('settings.account.password')}
              rules={[{ required: true, message: t('common.required') }]}
            >
              <Input.Password prefix={<LockOutlined />} />
            </Form.Item>
          )}

          <Form.Item
            name="role"
            label={t('settings.account.role')}
            rules={[{ required: true, message: t('common.required') }]}
          >
            <Select>
              <Option value="admin">{t('settings.account.admin')}</Option>
              <Option value="user">{t('settings.account.user')}</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label={t('settings.account.status')}
            rules={[{ required: true, message: t('common.required') }]}
          >
            <Select>
              <Option value="active">{t('settings.account.active')}</Option>
              <Option value="inactive">{t('settings.account.inactive')}</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common.save')}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除用户确认对话框 */}
      <ConfirmDialog
        title={t('settings.account.deleteUserTitle')}
        content={t('settings.account.deleteUserConfirm')}
        visible={deleteModalVisible}
        onConfirm={confirmDeleteUser}
        onCancel={() => setDeleteModalVisible(false)}
      />
    </Card>
  );
};

export default UserManagementPanel;
