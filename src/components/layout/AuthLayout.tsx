import React from 'react';
import { Layout, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import { Outlet } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { setLanguage } from '../../store/slices/settingsSlice';

const { Header, Content, Footer } = Layout;
const { Option } = Select;

const AuthLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { siteName, siteLogo } = useAppSelector((state) => state.settings);

  // 处理语言切换
  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
    dispatch(setLanguage(value));
  };

  return (
    <Layout style={{ minHeight: '100vh', background: 'var(--background-color)' }}>
      <Header style={{
        background: 'var(--background-color)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '0 16px',
        borderBottom: '1px solid var(--border-color)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {siteLogo && (
            <img
              src={siteLogo}
              alt="Logo"
              style={{ height: '32px', marginRight: '8px' }}
              onError={(e) => {
                console.error('Logo加载失败，尝试修复路径', e);
                // 如果加载失败且路径是相对路径，尝试转换为绝对路径
                const imgElement = e.target as HTMLImageElement;
                if (!imgElement.src.startsWith('http') && !imgElement.src.startsWith('data:')) {
                  const baseUrl = window.location.origin;
                  const url = imgElement.src.startsWith('/') ? imgElement.src : `/${imgElement.src}`;
                  imgElement.src = `${baseUrl}${url}`;
                }
              }}
            />
          )}
          <div style={{ fontSize: '1.25rem', fontWeight: 'bold', color: 'var(--primary-color)' }}>
            {siteName || t('app.title')}
          </div>
        </div>
        <Select
          defaultValue={i18n.language}
          onChange={handleLanguageChange}
          style={{ width: '96px' }}
        >
          <Option value="zh">中文</Option>
          <Option value="en">English</Option>
          <Option value="ja">日本語</Option>
        </Select>
      </Header>
      <Content style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '16px',
        background: 'var(--background-color)'
      }}>
        <div style={{ width: '100%', maxWidth: '448px' }}>
          <Outlet />
        </div>
      </Content>
      <Footer style={{
        textAlign: 'center',
        color: 'var(--text-color)',
        background: 'var(--background-color)',
        borderTop: '1px solid var(--border-color)'
      }}>
        &copy; {new Date().getFullYear()} {siteName || t('app.title')}
      </Footer>
    </Layout>
  );
};

export default AuthLayout;
