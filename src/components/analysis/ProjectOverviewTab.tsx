import React from 'react';
import { Card, Row, Col, Table, Typography, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { ProjectData } from '../../types/projectData';
import { formatCurrency } from '../../utils';
import StatisticWithTooltip from '../../components/common/StatisticWithTooltip';

const { Text } = Typography;

interface ProjectOverviewTabProps {
  project: ProjectData;
}

/**
 * 项目概览标签页组件
 */
const ProjectOverviewTab: React.FC<ProjectOverviewTabProps> = ({ project }) => {
  const { t } = useTranslation();

  console.log('ProjectOverviewTab - 开始渲染，项目ID:', project.id);

  // 项目基本信息
  const { analysisResults } = project;
  console.log('ProjectOverviewTab - 分析结果:', analysisResults ? '已完成' : '未完成');

  // 使用分析结果中的年度数据，而不是重新计算
  const yearlyData = project.analysisResults?.yearlyData || {
    pvGeneration: 0,
    electricityConsumption: 0,
    gridExport: 0,
    gridImport: 0,
    gridExportIncome: 0,
    pvBenefit: 0,
    storageBenefit: 0,
    totalBenefit: 0,
    roi: 0,
    paybackPeriod: 0
  };

  // 获取年度数据
  const totalPvGeneration = yearlyData.pvGeneration;
  console.log('ProjectOverviewTab - 总发电量(从yearlyData):', totalPvGeneration, 'kWh');

  // 总用电量
  const totalElectricityConsumption = yearlyData.electricityConsumption;
  console.log('ProjectOverviewTab - 总用电量(从yearlyData):', totalElectricityConsumption, 'kWh');

  // 总上网电量
  const totalGridExport = yearlyData.gridExport;
  console.log('ProjectOverviewTab - 总上网电量(从yearlyData):', totalGridExport, 'kWh');

  // 总购电量
  const gridImport = yearlyData.gridImport;
  console.log('ProjectOverviewTab - 总购电量(从yearlyData):', gridImport, 'kWh');

  // 这些函数已不再需要，因为我们直接使用分析结果中的年度数据
  // 如果将来需要重新计算，可以恢复这些函数

  // 使用分析结果中的年度数据，而不是重新计算
  const pvBenefit = yearlyData.pvBenefit;
  console.log('ProjectOverviewTab - 光伏收益(从yearlyData):', pvBenefit, '日元');

  // 使用分析结果中的年度数据，而不是重新计算
  const storageBenefit = yearlyData.storageBenefit;
  console.log('ProjectOverviewTab - 储能收益(从yearlyData):', storageBenefit, '日元');

  // 使用分析结果中的年度数据，而不是重新计算
  const totalBenefit = yearlyData.totalBenefit;
  console.log('ProjectOverviewTab - 总收益(从yearlyData):', totalBenefit, '日元');

  // 计算自用电量（仅用于显示）
  const selfUseAmount = Math.min(totalPvGeneration, totalElectricityConsumption);
  console.log('ProjectOverviewTab - 自用电量计算结果:', selfUseAmount, 'kWh');

  // 计算总投资成本
  const pvModulesCost = project.pvModules.reduce((sum, module) =>
    sum + module.price * module.quantity, 0);
  console.log('ProjectOverviewTab - 光伏组件总成本:', pvModulesCost, '日元');

  const energyStorageCost = project.energyStorage.reduce((sum, storage) =>
    sum + storage.price * storage.quantity, 0);
  console.log('ProjectOverviewTab - 储能设备总成本:', energyStorageCost, '日元');

  const invertersCost = project.inverters.reduce((sum, inverter) =>
    sum + inverter.price * inverter.quantity, 0);
  console.log('ProjectOverviewTab - 逆变器总成本:', invertersCost, '日元');

  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) =>
    sum + item.price, 0);
  console.log('ProjectOverviewTab - 其他投资总成本:', otherInvestmentsCost, '日元');

  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;
  console.log('ProjectOverviewTab - 总投资成本:', totalInvestment, '日元');

  // 使用分析结果中的年度数据，而不是重新计算
  const roi = yearlyData.roi;
  console.log('ProjectOverviewTab - ROI(从yearlyData):', roi, '%');

  // 使用分析结果中的年度数据，而不是重新计算
  const paybackPeriod = yearlyData.paybackPeriod;
  console.log('ProjectOverviewTab - 回收期(从yearlyData):', paybackPeriod, '年');

  // 现金流表格数据
  const cashFlowColumns = [
    {
      title: t('analysis.year'),
      dataIndex: 'year',
      key: 'year',
    },
    {
      title: t('analysis.investment'),
      dataIndex: 'investment',
      key: 'investment',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: t('analysis.annualBenefit'),
      dataIndex: 'annualBenefit',
      key: 'annualBenefit',
      render: (value: number) => formatCurrency(value),
    },
    {
      title: t('analysis.cumulativeCashFlow'),
      dataIndex: 'cumulativeCashFlow',
      key: 'cumulativeCashFlow',
      render: (value: number) => (
        <span style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
          {formatCurrency(value)}
        </span>
      ),
    },
  ];

  // 生成20年现金流数据
  const cashFlowData = [];
  let cumulativeCashFlow = 0;

  for (let i = 0; i < 20; i++) {
    const year = i + 1;
    const investment = year === 1 ? totalInvestment : 0;
    const annualBenefit = yearlyData.totalBenefit; // 使用分析结果中的年度数据

    // 更新累计现金流
    cumulativeCashFlow = cumulativeCashFlow - investment + annualBenefit;

    cashFlowData.push({
      key: year,
      year,
      investment,
      annualBenefit,
      cumulativeCashFlow,
    });
  }

  // 获取投资建议
  const getInvestmentAdvice = (roi: number, paybackPeriod: number) => {
    const items = [];

    // ROI建议
    if (roi < 0) {
      items.push(
        <div key="roi-negative">
          <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
          <Text>{t('analysis.roiNegative')}</Text>
        </div>
      );
    } else if (roi < 5) {
      items.push(
        <div key="roi-low">
          <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
          <Text>{t('analysis.roiLow')}</Text>
        </div>
      );
    } else {
      items.push(
        <div key="roi-good">
          <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
          <Text>{t('analysis.roiGood')}</Text>
        </div>
      );
    }

    // 回收期建议
    if (paybackPeriod > 15) {
      items.push(
        <div key="payback-long">
          <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
          <Text>{t('analysis.paybackLong')}</Text>
        </div>
      );
    } else if (paybackPeriod > 10) {
      items.push(
        <div key="payback-medium">
          <InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
          <Text>{t('analysis.paybackMedium')}</Text>
        </div>
      );
    } else {
      items.push(
        <div key="payback-short">
          <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
          <Text>{t('analysis.paybackShort')}</Text>
        </div>
      );
    }

    // 总体建议
    items.push(
      <div key="overall-advice" style={{ marginTop: 16 }}>
        <InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
        <Text>{t('analysis.overallAdvice')}</Text>
      </div>
    );

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {items}
      </Space>
    );
  };

  return (
    <div>
      {/* 关键指标卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <StatisticWithTooltip
              title={t('analysis.totalBenefit')}
              tooltip={t('analysis.tooltips.totalBenefit')}
              value={totalBenefit}
              precision={1}
              valueStyle={{ color: '#3f8600' }}
              prefix="JPY "
              //suffix={t('analysis.perYear')}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <StatisticWithTooltip
              title={t('analysis.roi')}
              tooltip={t('analysis.tooltips.roi')}
              value={roi}
              precision={1}
              valueStyle={{ color: roi >= 10 ? '#3f8600' : roi >= 5 ? '#1890ff' : '#cf1322' }}
              suffix="%"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <StatisticWithTooltip
              title={t('analysis.paybackPeriod')}
              tooltip={t('analysis.tooltips.paybackPeriod')}
              value={paybackPeriod}
              precision={1}
              valueStyle={{ color: paybackPeriod <= 8 ? '#3f8600' : paybackPeriod <= 12 ? '#1890ff' : '#cf1322' }}
              suffix={t('analysis.years')}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <StatisticWithTooltip
              title={t('analysis.selfConsumptionRate')}
              tooltip={t('analysis.tooltips.selfConsumptionRate')}
              value={totalPvGeneration ?
                ((totalPvGeneration - totalGridExport) / totalPvGeneration) * 100 : 0}
              precision={1}
              valueStyle={{ color: '#1890ff' }}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* 财务数据表格 */}
      <Card title={t('analysis.financialSummary')} style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <StatisticWithTooltip
              title={t('analysis.annualPVGeneration')}
              tooltip={t('analysis.tooltips.annualPVGeneration')}
              value={totalPvGeneration}
              precision={1}
              suffix="kWh"
              style={{ marginBottom: 16 }}
            />
            <StatisticWithTooltip
              title={t('analysis.annualConsumption')}
              tooltip={t('analysis.tooltips.annualConsumption')}
              value={totalElectricityConsumption}
              precision={1}
              suffix="kWh"
              style={{ marginBottom: 16 }}
            />
            <StatisticWithTooltip
              title={t('analysis.annualGridExport')}
              tooltip={t('analysis.tooltips.annualGridExport')}
              value={totalGridExport}
              precision={1}
              suffix="kWh"
              style={{ marginBottom: 16 }}
            />
            <StatisticWithTooltip
              title={t('analysis.annualGridImport')}
              tooltip={t('analysis.tooltips.annualGridImport')}
              value={gridImport}
              precision={1}
              suffix="kWh"
            />
          </Col>
          <Col span={12}>
            <StatisticWithTooltip
              title={t('analysis.pvBenefit')}
              tooltip={t('analysis.tooltips.pvBenefit')}
              value={pvBenefit}
              precision={1}
              prefix="JPY "
              style={{ marginBottom: 16 }}
            />
            <StatisticWithTooltip
              title={t('analysis.storageBenefit')}
              tooltip={t('analysis.tooltips.storageBenefit')}
              value={storageBenefit}
              precision={1}
              prefix="JPY "
              style={{ marginBottom: 16 }}
            />
            <StatisticWithTooltip
              title={t('analysis.gridExportIncome')}
              tooltip={t('analysis.tooltips.gridExportIncome')}
              value={yearlyData.gridExportIncome} // 使用分析结果中的年度数据
              precision={1}
              prefix="JPY "
              style={{ marginBottom: 16 }}
            />
            <StatisticWithTooltip
              title={t('analysis.totalInvestment')}
              tooltip={t('analysis.tooltips.totalInvestment')}
              value={totalInvestment}
              precision={1}
              prefix="JPY "
            />
          </Col>
        </Row>
      </Card>

      {/* 投资建议 */}
      <Card title={t('analysis.investmentAdvice')} style={{ marginBottom: 16 }}>
        {getInvestmentAdvice(roi, paybackPeriod)}
      </Card>

      {/* 年度现金流表格 */}
      <Card title={t('analysis.annualCashFlow')}>
        <Table
          columns={cashFlowColumns}
          dataSource={cashFlowData}
          pagination={false}
          size="small"
          scroll={{ y: 300 }}
        />
      </Card>
    </div>
  );
};

export default ProjectOverviewTab;
