import React from 'react';
import { Alert, Space, Tag } from 'antd';
import { useTranslation } from 'react-i18next';

interface StepAlertProps {
  message: string;
  description: string;
  type: 'info' | 'warning' | 'success' | 'error';
  showIcon?: boolean;
  style?: React.CSSProperties;
  incompleteFields?: string[];
}

const StepAlert: React.FC<StepAlertProps> = ({
  message,
  description,
  type,
  showIcon = true,
  style,
  incompleteFields = []
}) => {
  const { t } = useTranslation();

  // 渲染未完成字段
  const renderIncompleteFields = () => {
    // 确保incompleteFields是数组且不为空
    if (!incompleteFields || !Array.isArray(incompleteFields) || incompleteFields.length === 0) {
      return null;
    }

    return (
      <div style={{ marginTop: '8px', color: '#ff4d4f' }}>
        <span>{t('projectWizard.incompleteFields')}: </span>
        <Space size={[0, 8]} wrap>
          {incompleteFields.map((field, index) => (
            <Tag key={index} color="error">{field || ''}</Tag>
          ))}
        </Space>
      </div>
    );
  };

  return (
    <Alert
      message={message}
      description={
        <>
          <div>{description}</div>
          {renderIncompleteFields()}
        </>
      }
      type={type}
      showIcon={showIcon}
      style={{ marginBottom: '24px', ...style }}
    />
  );
};

export default StepAlert;
