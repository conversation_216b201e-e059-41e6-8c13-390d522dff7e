import React from 'react';
import { Card, Row, Col, Select, Button, Space, Input, DatePicker } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { RangePicker } = DatePicker;

export interface FilterOption {
  key: string;
  label: string;
  type: 'select' | 'input' | 'date' | 'dateRange';
  options?: Array<{
    value: string;
    label: string;
  }>;
  placeholder?: string;
}

interface FilterFormProps {
  title?: string;
  filters: Record<string, any>;
  filterOptions: FilterOption[];
  onFilterChange: (field: string, value: any) => void;
  onSearch: () => void;
  onReset: () => void;
  colSpan?: number;
}

/**
 * 通用筛选条件表单组件
 * 用于在数据库页面中提供筛选功能
 */
const FilterForm: React.FC<FilterFormProps> = ({
  title,
  filters,
  filterOptions,
  onFilterChange,
  onSearch,
  onReset,
  colSpan = 6
}) => {
  const { t } = useTranslation();

  // 渲染筛选字段
  const renderFilterField = (option: FilterOption) => {
    const { key, label, type, options, placeholder } = option;

    switch (type) {
      case 'select':
        return (
          <Select
            style={{ width: '100%' }}
            value={filters[key]}
            onChange={(value) => onFilterChange(key, value)}
            placeholder={placeholder || t('common.pleaseSelect')}
            allowClear
          >
            {options?.map(opt => (
              <Option key={opt.value} value={opt.value}>{opt.label}</Option>
            ))}
          </Select>
        );
      case 'input':
        return (
          <Input
            value={filters[key]}
            onChange={(e) => onFilterChange(key, e.target.value)}
            placeholder={placeholder || t('common.pleaseInput')}
            allowClear
          />
        );
      case 'date':
        return (
          <DatePicker
            style={{ width: '100%' }}
            value={filters[key]}
            onChange={(date) => onFilterChange(key, date)}
            placeholder={placeholder || t('common.pleaseSelect')}
          />
        );
      case 'dateRange':
        return (
          <RangePicker
            style={{ width: '100%' }}
            value={filters[key]}
            onChange={(dates) => onFilterChange(key, dates)}
            placeholder={[
              placeholder ? placeholder[0] : t('common.startDate'),
              placeholder ? placeholder[1] : t('common.endDate')
            ]}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Card title={title || t('common.filter')}>
      <Row gutter={[16, 16]}>
        {filterOptions.map(option => (
          <Col span={colSpan} key={option.key}>
            <div>{option.label}</div>
            {renderFilterField(option)}
          </Col>
        ))}
      </Row>
      <Row justify="end" style={{ marginTop: '16px' }}>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={onReset}>
            {t('common.reset')}
          </Button>
          <Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
            {t('common.search')}
          </Button>
        </Space>
      </Row>
    </Card>
  );
};

export default FilterForm;
