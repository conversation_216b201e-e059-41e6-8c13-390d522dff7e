import React from 'react';
import { StarFilled, StarOutlined } from '@ant-design/icons';

interface RatingStarsProps {
  rating: string | number;
  maxRating?: number;
}

/**
 * 星级评分组件
 * 用于显示供应商评级
 */
const RatingStars: React.FC<RatingStarsProps> = ({ rating, maxRating = 5 }) => {
  // 将评级转换为数字
  const ratingNumber = Number(rating) || 0;
  
  return (
    <div className="rating-stars">
      {Array.from({ length: maxRating }).map((_, index) => (
        index < ratingNumber ? (
          <StarFilled key={index} style={{ color: '#fadb14', fontSize: '16px', marginRight: '2px' }} />
        ) : (
          <StarOutlined key={index} style={{ color: '#d9d9d9', fontSize: '16px', marginRight: '2px' }} />
        )
      ))}
    </div>
  );
};

export default RatingStars;
