import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * 错误边界组件
 * 用于捕获子组件中的 JavaScript 错误，并显示备用 UI
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state，下次渲染时显示备用 UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 记录错误信息
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  // 重置错误状态
  handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback，则使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误 UI
      return (
        <Card>
          <Alert
            message="组件加载错误"
            description="该组件加载过程中发生错误，请尝试刷新页面或联系管理员。"
            type="error"
            showIcon
          />
          <div style={{ marginTop: 16 }}>
            <Button type="primary" onClick={this.handleReset}>
              尝试恢复
            </Button>
            <Button 
              style={{ marginLeft: 8 }} 
              onClick={() => window.location.reload()}
            >
              刷新页面
            </Button>
          </div>
          {process.env.NODE_ENV !== 'production' && (
            <div style={{ marginTop: 16, overflow: 'auto', maxHeight: '300px' }}>
              <h4>错误详情：</h4>
              <p>{this.state.error?.toString()}</p>
              <pre>{this.state.errorInfo?.componentStack}</pre>
            </div>
          )}
        </Card>
      );
    }

    // 正常情况下渲染子组件
    return this.props.children;
  }
}

export default ErrorBoundary;
