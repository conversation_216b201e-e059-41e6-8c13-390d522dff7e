import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PriceRule } from './PriceRuleForm';

interface TimeRangeSelectorProps {
  rules: PriceRule[];
  onRangeSelected: (startHour: number, endHour: number) => void;
}

/**
 * 时间范围选择器组件
 * 用于可视化展示24小时电价规则，并支持拖拽选择时间范围
 */
const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({ rules, onRangeSelected }) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 拖拽状态
  const [isDragging, setIsDragging] = useState(false);
  const [startHour, setStartHour] = useState<number | null>(null);
  const [endHour, setEndHour] = useState<number | null>(null);
  const [currentHour, setCurrentHour] = useState<number | null>(null);

  // 颜色配置
  const colors = {
    'peak': '#ff4d4f',       // 峰时 - 红色
    'normal': '#1677ff',     // 平时 - 蓝色
    'valley': '#52c41a',     // 谷时 - 绿色
    'super-peak': '#ff8800', // 尖峰 - 橙色
    selection: 'rgba(24, 144, 255, 0.3)', // 选择区域 - 半透明蓝色
    grid: '#e8e8e8',         // 网格线 - 浅灰色
    text: '#333333',         // 文本 - 深灰色
    background: '#ffffff'    // 背景 - 白色
  };

  // 绘制图表
  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const hourWidth = width / 24;
    const chartHeight = height - 50; // 留出底部空间显示小时数和Y轴标签
    const topMargin = 20; // 顶部留出空间

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 绘制背景
    ctx.fillStyle = colors.background;
    ctx.fillRect(0, 0, width, height);

    // 找出最大电价，用于计算比例
    let maxPrice = 0;
    rules.forEach(rule => {
      if (rule.price > maxPrice) {
        maxPrice = rule.price;
      }
    });

    // 确保最大价格至少为1，避免除以0
    maxPrice = Math.max(maxPrice, 1);
    // 向上取整到最接近的整数，并增加一点余量
    maxPrice = Math.ceil(maxPrice * 1.2);

    // 绘制Y轴刻度
    ctx.strokeStyle = colors.grid;
    ctx.fillStyle = colors.text;
    ctx.textAlign = 'right';
    ctx.font = '10px Arial';

    const yAxisSteps = 5; // Y轴刻度数量
    for (let i = 0; i <= yAxisSteps; i++) {
      const y = topMargin + chartHeight - (i * chartHeight / yAxisSteps);
      const price = (i * maxPrice / yAxisSteps).toFixed(1);

      // 绘制水平网格线
      ctx.beginPath();
      ctx.moveTo(30, y); // 从Y轴标签后开始
      ctx.lineTo(width, y);
      ctx.stroke();

      // 绘制价格标签
      ctx.fillText(price, 25, y + 4);
    }

    // 绘制Y轴标题
    ctx.save();
    ctx.translate(10, topMargin + chartHeight / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.textAlign = 'center';
    ctx.fillText(t('electricityPrice.price'), 0, 0);
    ctx.restore();

    // 绘制网格线和小时标签
    ctx.strokeStyle = colors.grid;
    ctx.fillStyle = colors.text;
    ctx.textAlign = 'center';
    ctx.font = '12px Arial';

    for (let i = 0; i <= 24; i++) {
      const x = i * hourWidth;

      // 绘制垂直网格线
      ctx.beginPath();
      ctx.moveTo(x, topMargin);
      ctx.lineTo(x, topMargin + chartHeight);
      ctx.stroke();

      // 绘制小时标签
      if (i < 24) {
        ctx.fillText(i.toString(), x + hourWidth / 2, height - 10);
      }
    }

    // 绘制X轴标题
    ctx.fillText(t('electricityPrice.hour'), width / 2, height - 5);

    // 绘制已有规则
    rules.forEach(rule => {
      // 解析时间
      const startTimeParts = rule.startTime.split(':').map(Number);
      const endTimeParts = rule.endTime.split(':').map(Number);

      let startHour = startTimeParts[0] + startTimeParts[1] / 60;
      let endHour = endTimeParts[0] + endTimeParts[1] / 60;

      // 处理跨天的情况
      if (endHour <= startHour) {
        endHour += 24;
      }

      // 设置颜色
      ctx.fillStyle = colors[rule.type] || colors.normal;

      // 计算柱状图高度（根据价格比例）
      const barHeightRatio = rule.price / maxPrice;
      const barHeight = chartHeight * barHeightRatio;

      // 绘制规则区域（柱状图）
      const startX = startHour * hourWidth;
      const endX = endHour * hourWidth;
      const barY = topMargin + chartHeight - barHeight;

      ctx.fillRect(startX, barY, endX - startX, barHeight);

      // 绘制价格标签
      ctx.fillStyle = '#ffffff';
      ctx.textAlign = 'center';
      const labelX = startX + (endX - startX) / 2;
      const labelY = barY + barHeight / 2;

      // 只有当区域足够宽时才显示价格
      if (endX - startX > 40 && barHeight > 15) {
        ctx.fillText(`${rule.price.toFixed(1)}`, labelX, labelY);
      }
    });

    // 绘制当前选择区域
    if (startHour !== null && currentHour !== null) {
      const selStartHour = Math.min(startHour, currentHour);
      const selEndHour = Math.max(startHour, currentHour);

      // 计算选择区域的宽度（精确到小时）
      const selWidth = (selEndHour - selStartHour + 1) * hourWidth;

      // 绘制选择区域
      ctx.fillStyle = colors.selection;
      ctx.fillRect(
        selStartHour * hourWidth,
        0,
        selWidth,
        chartHeight
      );

      // 绘制选择区域的边框
      ctx.strokeStyle = '#1890ff';
      ctx.lineWidth = 2;
      ctx.strokeRect(
        selStartHour * hourWidth,
        0,
        selWidth,
        chartHeight
      );

      // 绘制选择区域的时间范围文本
      ctx.fillStyle = '#1890ff';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      const labelX = selStartHour * hourWidth + selWidth / 2;
      const labelY = chartHeight / 2;

      // 只有当区域足够宽时才显示时间范围
      if (selWidth > 60) {
        const startText = selStartHour < 10 ? `0${selStartHour}:00` : `${selStartHour}:00`;
        const endHourDisplay = (selEndHour + 1) % 24; // 显示的结束时间需要加1，因为我们要包含整个小时
        const endText = endHourDisplay < 10 ? `0${endHourDisplay}:00` : `${endHourDisplay}:00`;
        ctx.fillText(`${startText} - ${endText}`, labelX, labelY);
      }

      ctx.lineWidth = 1;
    }
  };

  // 获取鼠标位置对应的小时
  const getHourFromEvent = (e: React.MouseEvent<HTMLCanvasElement> | MouseEvent): number => {
    if (!canvasRef.current || !containerRef.current) return 0;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const hourWidth = canvasRef.current.width / 24;

    // 计算小时，并确保在0-24范围内
    let hour = Math.floor(x / hourWidth);
    hour = Math.max(0, Math.min(23, hour));

    return hour;
  };

  // 鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const hour = getHourFromEvent(e);
    setStartHour(hour);
    setCurrentHour(hour);
    setIsDragging(true);
  };

  // 鼠标移动事件
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const hour = getHourFromEvent(e);
    setCurrentHour(hour);
    drawChart();
  };

  // 鼠标释放事件
  const handleMouseUp = () => {
    if (isDragging && startHour !== null && currentHour !== null) {
      const selStartHour = Math.min(startHour, currentHour);
      const selEndHour = Math.max(startHour, currentHour);

      // 通知父组件选择的时间范围
      // 注意：我们传递的是确切的小时，不再加1
      onRangeSelected(selStartHour, selEndHour + 1);

      console.log(`选择了时间范围: ${selStartHour}:00 - ${selEndHour + 1}:00`);
    }

    setIsDragging(false);
  };

  // 鼠标离开事件
  const handleMouseLeave = () => {
    if (isDragging) {
      handleMouseUp();
    }
  };

  // 组件挂载和更新时重绘图表
  useEffect(() => {
    drawChart();
  }, [rules, startHour, currentHour, isDragging]);

  // 添加和移除全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, startHour, currentHour]);

  return (
    <div
      ref={containerRef}
      style={{
        border: '1px solid #d9d9d9',
        borderRadius: '4px',
        padding: '8px',
        marginBottom: '16px'
      }}
    >
      <div style={{ marginBottom: '8px', fontSize: '14px', color: '#666' }}>
        {t('electricityPrice.dragToSelect', '拖拽选择时间范围')}
      </div>
      <canvas
        ref={canvasRef}
        width={600}
        height={200}
        onMouseDown={handleMouseDown}
        onMouseLeave={handleMouseLeave}
        style={{ width: '100%', height: 'auto', cursor: isDragging ? 'grabbing' : 'grab' }}
      />
      <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
        <div>
          <span style={{ display: 'inline-block', width: '12px', height: '12px', backgroundColor: colors['super-peak'], marginRight: '4px' }}></span>
          <span style={{ fontSize: '12px', marginRight: '12px' }}>{t('electricityPrice.superPeak', '尖峰')}</span>

          <span style={{ display: 'inline-block', width: '12px', height: '12px', backgroundColor: colors.peak, marginRight: '4px' }}></span>
          <span style={{ fontSize: '12px', marginRight: '12px' }}>{t('electricityPrice.peak')}</span>

          <span style={{ display: 'inline-block', width: '12px', height: '12px', backgroundColor: colors.normal, marginRight: '4px' }}></span>
          <span style={{ fontSize: '12px', marginRight: '12px' }}>{t('electricityPrice.normal')}</span>

          <span style={{ display: 'inline-block', width: '12px', height: '12px', backgroundColor: colors.valley, marginRight: '4px' }}></span>
          <span style={{ fontSize: '12px' }}>{t('electricityPrice.valley')}</span>
        </div>
        <div style={{ fontSize: '12px', color: '#666' }}>
          {t('electricityPrice.priceUnit', '单位: 元/kWh')}
        </div>
      </div>
    </div>
  );
};

export default TimeRangeSelector;
