import React from 'react';
import { Modal, Progress, Button, Space } from 'antd';
import { useTranslation } from 'react-i18next';

interface ProgressDialogProps {
  visible: boolean;
  title: string;
  progress: number;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmDisabled?: boolean;
}

const ProgressDialog: React.FC<ProgressDialogProps> = ({
  visible,
  title,
  progress,
  message,
  onConfirm,
  onCancel,
  confirmDisabled = false
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      footer={
        <Space>
          <Button onClick={onCancel}>
            {t('common.cancel')}
          </Button>
          <Button 
            type="primary" 
            onClick={onConfirm}
            disabled={confirmDisabled}
          >
            {t('common.confirm')}
          </Button>
        </Space>
      }
      closable={false}
      maskClosable={false}
    >
      <div style={{ padding: '20px 0' }}>
        <p style={{ marginBottom: 16, fontSize: 14 }}>
          {message}
        </p>
        <Progress 
          percent={Math.round(progress)} 
          status={progress >= 100 ? 'success' : 'active'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
      </div>
    </Modal>
  );
};

export default ProgressDialog;
