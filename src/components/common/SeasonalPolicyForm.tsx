import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Space, Card, Tabs, Checkbox, Tag, Divider } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { PriceRuleForm } from './index';
import { PriceRule } from './PriceRuleForm';
import { SeasonalPricePolicy } from '../../types/database';

const { Option } = Select;

interface SeasonalPolicyFormProps {
  value?: SeasonalPricePolicy[];
  onChange?: (policies: SeasonalPricePolicy[]) => void;
}

/**
 * 季节性电价政策表单组件
 * 用于添加、编辑、删除季节性电价政策
 */
const SeasonalPolicyForm: React.FC<SeasonalPolicyFormProps> = ({ value = [], onChange }) => {
  const { t } = useTranslation();
  const [policies, setPolicies] = useState<SeasonalPricePolicy[]>(value);
  const [activeKey, setActiveKey] = useState<string>('');
  const [editMode, setEditMode] = useState<boolean>(false);
  const [form] = Form.useForm();

  // 当外部value变化时更新内部状态
  useEffect(() => {
    const policiesWithIds = value.map(policy => ({
      ...policy,
      id: policy.id || uuidv4(),
    }));
    setPolicies(policiesWithIds);
    if (policiesWithIds.length > 0 && !activeKey) {
      setActiveKey(policiesWithIds[0].id);
    }
  }, [value, activeKey]);

  // 添加新的季节政策
  const handleAddPolicy = () => {
    form.resetFields();
    const newPolicy: SeasonalPricePolicy = {
      id: uuidv4(),
      name: '',
      months: [],
      rules: [],
    };

    const newPolicies = [...policies, newPolicy];
    setPolicies(newPolicies);
    setActiveKey(newPolicy.id);
    setEditMode(true);

    if (onChange) {
      onChange(newPolicies);
    }
  };

  // 删除季节政策
  const handleDeletePolicy = (policyId: string) => {
    const newPolicies = policies.filter(policy => policy.id !== policyId);
    setPolicies(newPolicies);

    if (newPolicies.length > 0) {
      setActiveKey(newPolicies[0].id);
    } else {
      setActiveKey('');
    }

    if (onChange) {
      onChange(newPolicies);
    }
  };

  // 编辑季节政策
  const handleEditPolicy = (policy: SeasonalPricePolicy) => {
    form.setFieldsValue({
      name: policy.name,
      months: policy.months,
      rules: policy.rules,
    });
    setActiveKey(policy.id);
    setEditMode(true);
  };

  // 保存季节政策
  const handleSavePolicy = async () => {
    try {
      const values = await form.validateFields();
      const updatedPolicies = policies.map(policy => {
        if (policy.id === activeKey) {
          return {
            ...policy,
            name: values.name,
            months: values.months,
            rules: values.rules || [],
          };
        }
        return policy;
      });

      setPolicies(updatedPolicies);
      setEditMode(false);

      if (onChange) {
        onChange(updatedPolicies);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditMode(false);
    form.resetFields();
  };

  // 切换标签页
  const handleTabChange = (key: string) => {
    if (editMode) {
      // 如果正在编辑，提示保存
      if (window.confirm(t('electricityPrice.saveChangesPrompt'))) {
        handleSavePolicy();
      } else {
        handleCancelEdit();
      }
    }

    setActiveKey(key);
    const policy = policies.find(p => p.id === key);
    if (policy) {
      form.setFieldsValue({
        name: policy.name,
        months: policy.months,
        rules: policy.rules,
      });
    }
  };

  // 渲染月份选择器
  const renderMonthSelector = () => {
    const months = [
      { value: 1, label: t('common.january') },
      { value: 2, label: t('common.february') },
      { value: 3, label: t('common.march') },
      { value: 4, label: t('common.april') },
      { value: 5, label: t('common.may') },
      { value: 6, label: t('common.june') },
      { value: 7, label: t('common.july') },
      { value: 8, label: t('common.august') },
      { value: 9, label: t('common.september') },
      { value: 10, label: t('common.october') },
      { value: 11, label: t('common.november') },
      { value: 12, label: t('common.december') },
    ];

    return (
      <Form.Item
        name="months"
        label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.applicableMonths')}</span>}
        rules={[{ required: true, message: t('electricityPrice.monthsRequired') }]}
      >
        <Select
          mode="multiple"
          placeholder={t('electricityPrice.selectMonths')}
          style={{ width: '100%' }}
        >
          {months.map(month => (
            <Option key={month.value} value={month.value}>
              {month.label}
            </Option>
          ))}
        </Select>
      </Form.Item>
    );
  };

  // 渲染标签页
  const renderTabs = () => {
    const items = policies.map(policy => ({
      key: policy.id,
      label: (
        <span>
          {policy.name || t('electricityPrice.newSeason')}
          {policy.months && policy.months.length > 0 && (
            <Tag color="blue" style={{ marginLeft: 8 }}>
              {policy.months.length} {t('electricityPrice.months')}
            </Tag>
          )}
        </span>
      ),
      children: activeKey === policy.id ? renderPolicyForm(policy) : null
    }));

    return (
      <Tabs
        type="card"
        activeKey={activeKey}
        onChange={handleTabChange}
        items={items}
        tabBarExtraContent={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddPolicy}
          >
            {t('electricityPrice.addSeason')}
          </Button>
        }
      />
    );
  };

  // 渲染政策表单
  const renderPolicyForm = (policy: SeasonalPricePolicy) => {
    return (
      <div>
        {editMode ? (
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              name: policy.name,
              months: policy.months,
              rules: policy.rules,
            }}
          >
            <Form.Item
              name="name"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.seasonName')}</span>}
              rules={[{ required: true, message: t('electricityPrice.seasonNameRequired') }]}
            >
              <Input placeholder={t('electricityPrice.seasonNamePlaceholder')} />
            </Form.Item>

            {renderMonthSelector()}

            <Form.Item
              name="rules"
              label={<span style={{ color: '#ff4d4f' }}>* {t('electricityPrice.rules')}</span>}
              rules={[{ required: true, message: t('electricityPrice.rulesRequired') }]}
            >
              <PriceRuleForm />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" onClick={handleSavePolicy}>
                  {t('common.save')}
                </Button>
                <Button onClick={handleCancelEdit}>
                  {t('common.cancel')}
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeletePolicy(policy.id)}
                >
                  {t('common.delete')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        ) : (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
              <h3>{policy.name}</h3>
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => handleEditPolicy(policy)}
              >
                {t('common.edit')}
              </Button>
            </div>

            <Divider orientation="left">{t('electricityPrice.applicableMonths')}</Divider>
            <div style={{ marginBottom: 16 }}>
              {policy.months && policy.months.length > 0 ? (
                <Space wrap>
                  {policy.months.map(month => (
                    <Tag key={month} color="blue">
                      {t(`common.month${month}`)}
                    </Tag>
                  ))}
                </Space>
              ) : (
                <span>{t('electricityPrice.noMonthsSelected')}</span>
              )}
            </div>

            <Divider orientation="left">{t('electricityPrice.rules')}</Divider>
            <div>
              {policy.rules && policy.rules.length > 0 ? (
                <PriceRuleForm value={policy.rules} />
              ) : (
                <span>{t('electricityPrice.noRules')}</span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="seasonal-policy-form">
      {policies.length > 0 ? (
        renderTabs()
      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <p>{t('electricityPrice.noSeasons')}</p>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddPolicy}
            >
              {t('electricityPrice.addSeason')}
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SeasonalPolicyForm;
