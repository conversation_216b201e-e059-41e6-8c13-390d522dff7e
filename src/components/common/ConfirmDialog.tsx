import React from 'react';
import { Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface ConfirmDialogProps {
  title: string;
  content: string;
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  okText?: string;
  cancelText?: string;
}

/**
 * 通用确认对话框组件
 * 用于显示确认操作的对话框
 */
const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  title,
  content,
  visible,
  onConfirm,
  onCancel,
  okText,
  cancelText
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={title}
      open={visible}
      onOk={onConfirm}
      onCancel={onCancel}
      okText={okText || t('common.confirm')}
      cancelText={cancelText || t('common.cancel')}
      okButtonProps={{ danger: true }}
    >
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
        <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: 22, marginRight: 16 }} />
        <p>{content}</p>
      </div>
    </Modal>
  );
};

export default ConfirmDialog;
