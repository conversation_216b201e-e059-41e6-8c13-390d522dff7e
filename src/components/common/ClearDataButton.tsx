import React, { useState } from 'react';
import { Button, Modal, message } from 'antd';
import { DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { clearAllData } from '../../utils/clearData';

const { confirm } = Modal;

interface ClearDataButtonProps {
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  danger?: boolean;
  size?: 'large' | 'middle' | 'small';
  style?: React.CSSProperties;
}

/**
 * 清除数据按钮组件
 * 用于在页面中提供快速清除数据的功能
 */
const ClearDataButton: React.FC<ClearDataButtonProps> = ({
  type = 'default',
  danger = true,
  size = 'middle',
  style,
}) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);

  // 处理清除所有数据
  const handleClearAllData = () => {
    confirm({
      title: t('settings.clearAllDataTitle'),
      icon: <ExclamationCircleOutlined />,
      content: t('settings.clearAllDataConfirm'),
      okText: t('common.confirm'),
      okType: 'danger',
      cancelText: t('common.cancel'),
      onOk: async () => {
        try {
          setIsLoading(true);
          clearAllData();
          message.success(t('settings.dataCleared'));
        } catch (error) {
          console.error('清除所有数据失败:', error);
          message.error(t('common.error'));
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  return (
    <Button
      type={type}
      danger={danger}
      icon={<DeleteOutlined />}
      onClick={handleClearAllData}
      loading={isLoading}
      size={size}
      style={style}
    >
      {t('settings.clearAllData')}
    </Button>
  );
};

export default ClearDataButton;
