import React from 'react';
import { Modal, Button, Space, Typography, Card, Descriptions, Tag } from 'antd';
import { ExclamationCircleOutlined, ClockCircleOutlined, UserOutlined, DatabaseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { ProjectData } from '../../types/projectData';

const { Title, Text } = Typography;

interface DataConflictDialogProps {
  visible: boolean;
  localData: ProjectData;
  serverData: ProjectData;
  onResolve: (resolution: 'local' | 'server' | 'merge') => void;
  onCancel: () => void;
  loading?: boolean;
}

/**
 * 数据冲突解决对话框
 */
const DataConflictDialog: React.FC<DataConflictDialogProps> = ({
  visible,
  localData,
  serverData,
  onResolve,
  onCancel,
  loading = false
}) => {
  const { t } = useTranslation();

  const formatTime = (timeStr: string) => {
    return new Date(timeStr).toLocaleString('zh-CN');
  };

  const getDataSource = (data: ProjectData) => {
    return data.dataVersion?.source === 'frontend' ? '前端' : '后端';
  };

  const getAnalysisStatus = (data: ProjectData) => {
    if (data.analysisResults?.analysisCompleted) {
      return (
        <Tag color="green">
          已完成 ({formatTime(data.analysisResults.analysisDate)})
        </Tag>
      );
    }
    return <Tag color="orange">未完成</Tag>;
  };

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#faad14' }} />
          <span>检测到数据冲突</span>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button 
            type="primary" 
            danger
            loading={loading}
            onClick={() => onResolve('local')}
          >
            <UserOutlined />
            使用本地数据
          </Button>
          <Button 
            type="primary"
            loading={loading}
            onClick={() => onResolve('server')}
          >
            <DatabaseOutlined />
            使用服务器数据
          </Button>
          <Button 
            type="primary"
            style={{ backgroundColor: '#52c41a' }}
            loading={loading}
            onClick={() => onResolve('merge')}
          >
            智能合并
          </Button>
        </Space>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          检测到本地数据与服务器数据存在冲突，请选择要保留的数据版本：
        </Text>
      </div>

      <div style={{ display: 'flex', gap: 16 }}>
        {/* 本地数据 */}
        <Card 
          title={
            <Space>
              <UserOutlined />
              <span>本地数据</span>
            </Space>
          }
          style={{ flex: 1 }}
          size="small"
        >
          <Descriptions column={1} size="small">
            <Descriptions.Item label="项目名称">
              {localData.name}
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              <Space>
                <ClockCircleOutlined />
                {formatTime(localData.updatedAt)}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="数据来源">
              {getDataSource(localData)}
            </Descriptions.Item>
            <Descriptions.Item label="版本号">
              {localData.dataVersion?.version || 0}
            </Descriptions.Item>
            <Descriptions.Item label="分析状态">
              {getAnalysisStatus(localData)}
            </Descriptions.Item>
            <Descriptions.Item label="光伏组件">
              {localData.pvModules.length} 个
            </Descriptions.Item>
            <Descriptions.Item label="储能设备">
              {localData.energyStorage.length} 个
            </Descriptions.Item>
            <Descriptions.Item label="逆变器">
              {localData.inverters.length} 个
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 服务器数据 */}
        <Card 
          title={
            <Space>
              <DatabaseOutlined />
              <span>服务器数据</span>
            </Space>
          }
          style={{ flex: 1 }}
          size="small"
        >
          <Descriptions column={1} size="small">
            <Descriptions.Item label="项目名称">
              {serverData.name}
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              <Space>
                <ClockCircleOutlined />
                {formatTime(serverData.updatedAt)}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="数据来源">
              {getDataSource(serverData)}
            </Descriptions.Item>
            <Descriptions.Item label="版本号">
              {serverData.dataVersion?.version || 0}
            </Descriptions.Item>
            <Descriptions.Item label="分析状态">
              {getAnalysisStatus(serverData)}
            </Descriptions.Item>
            <Descriptions.Item label="光伏组件">
              {serverData.pvModules.length} 个
            </Descriptions.Item>
            <Descriptions.Item label="储能设备">
              {serverData.energyStorage.length} 个
            </Descriptions.Item>
            <Descriptions.Item label="逆变器">
              {serverData.inverters.length} 个
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>

      <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
        <Title level={5} style={{ margin: 0, marginBottom: 8, color: '#52c41a' }}>
          💡 解决方案说明
        </Title>
        <div style={{ fontSize: 12, color: '#666' }}>
          <div><strong>使用本地数据：</strong> 将本地的修改覆盖到服务器，丢弃服务器上的更改</div>
          <div><strong>使用服务器数据：</strong> 用服务器数据覆盖本地，丢弃本地的修改</div>
          <div><strong>智能合并：</strong> 保留最新的分析结果，合并其他配置信息（推荐）</div>
        </div>
      </div>
    </Modal>
  );
};

export default DataConflictDialog;
