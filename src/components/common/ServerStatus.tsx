import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Badge, Modal, Descriptions, message, Space } from 'antd';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import {
  getSystemStatus,
  restartServer,
  checkServerHealth,
  formatMemoryUsage,
  formatUptime,
  SystemStatus
} from '../../services/systemService';

interface ServerStatusProps {
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 检查间隔（毫秒） */
  checkInterval?: number;
}

const ServerStatus: React.FC<ServerStatusProps> = ({
  showDetails = false,
  checkInterval = 30000 // 30秒检查一次
}) => {
  const { t } = useTranslation();
  const [serverOnline, setServerOnline] = useState<boolean>(true);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [restarting, setRestarting] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date>(new Date());

  // 检查服务器状态
  const checkStatus = async (showMessage = false) => {
    try {
      setLoading(true);
      const isHealthy = await checkServerHealth();
      setServerOnline(isHealthy);
      setLastCheck(new Date());

      if (isHealthy && showDetails) {
        // 如果服务器在线且需要详细信息，获取系统状态
        try {
          const status = await getSystemStatus();
          setSystemStatus(status);
        } catch (error) {
          console.warn('获取系统状态失败，但服务器在线:', error);
        }
      }

      if (showMessage) {
        if (isHealthy) {
          message.success(t('system.serverRunning'));
        } else {
          message.error(t('system.serverConnectionFailed'));
        }
      }
    } catch (error) {
      console.error('检查服务器状态失败:', error);
      setServerOnline(false);
      setLastCheck(new Date());

      if (showMessage) {
        message.error(t('system.cannotConnectToServer'));
      }
    } finally {
      setLoading(false);
    }
  };

  // 重启服务器
  const handleRestart = async () => {
    Modal.confirm({
      title: t('system.restartConfirm'),
      content: t('system.restartConfirmMessage'),
      icon: <WarningOutlined />,
      okText: t('system.restartServer'),
      cancelText: t('common.cancel'),
      okType: 'danger',
      onOk: async () => {
        try {
          setRestarting(true);
          message.loading(t('system.restarting'), 0);

          await restartServer();

          // 等待服务器重启
          setTimeout(() => {
            message.destroy();
            message.success(t('system.restartSuccess'));
            setRestarting(false);
            checkStatus();
          }, 5000);

        } catch (error) {
          console.error('重启服务器失败:', error);
          message.destroy();
          message.error(t('system.restartFailed'));
          setRestarting(false);
        }
      }
    });
  };

  // 显示状态详情
  const showStatusDetails = async () => {
    try {
      setLoading(true);
      const status = await getSystemStatus();
      setSystemStatus(status);
      setShowStatusModal(true);
    } catch (error) {
      console.error('获取系统状态失败:', error);
      message.error(t('system.getSystemStatusFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 定期检查服务器状态
  useEffect(() => {
    checkStatus();

    const interval = setInterval(() => {
      checkStatus();
    }, checkInterval);

    return () => clearInterval(interval);
  }, [checkInterval]);

  // 获取状态图标和颜色
  const getStatusBadge = () => {
    if (restarting) {
      return <Badge status="processing" text={t('system.restarting')} />;
    }

    if (serverOnline) {
      return <Badge status="success" text={t('system.online')} />;
    } else {
      return <Badge status="error" text={t('system.offline')} />;
    }
  };

  // 获取状态图标
  const getStatusIcon = () => {
    if (restarting) {
      return <ReloadOutlined spin />;
    }

    if (serverOnline) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    } else {
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    }
  };

  return (
    <>
      <Space>
        <Tooltip title={`${t('system.serverStatus')} - ${t('system.lastCheck')}: ${lastCheck.toLocaleTimeString()}`}>
          <Button
            type="text"
            icon={getStatusIcon()}
            loading={loading}
            onClick={() => checkStatus(true)}
            size="small"
          >
            {getStatusBadge()}
          </Button>
        </Tooltip>

        {!serverOnline && (
          <Tooltip title={t('system.restartServer')}>
            <Button
              type="primary"
              danger
              icon={<ReloadOutlined />}
              loading={restarting}
              onClick={handleRestart}
              size="small"
            >
              {t('system.restartServer')}
            </Button>
          </Tooltip>
        )}

        {showDetails && serverOnline && (
          <Tooltip title={t('system.statusDetails')}>
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              onClick={showStatusDetails}
              size="small"
            >
              {t('common.details')}
            </Button>
          </Tooltip>
        )}
      </Space>

      {/* 状态详情模态框 */}
      <Modal
        title={t('system.systemStatusDetails')}
        open={showStatusModal}
        onCancel={() => setShowStatusModal(false)}
        footer={[
          <Button key="refresh" icon={<ReloadOutlined />} onClick={showStatusDetails}>
            {t('common.refresh')}
          </Button>,
          <Button key="close" onClick={() => setShowStatusModal(false)}>
            {t('common.close')}
          </Button>
        ]}
        width={600}
      >
        {systemStatus && (
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label={t('system.serverStatus')}>
              <Badge status="success" text={t('system.running')} />
            </Descriptions.Item>
            <Descriptions.Item label={t('system.uptime')}>
              {formatUptime(systemStatus.server.uptime)}
            </Descriptions.Item>
            <Descriptions.Item label={t('system.processId')}>
              {systemStatus.server.pid}
            </Descriptions.Item>
            <Descriptions.Item label={t('system.memoryUsage')}>
              <Space direction="vertical" size="small">
                <div>RSS: {formatMemoryUsage(systemStatus.server.memory.rss)}</div>
                <div>堆内存: {formatMemoryUsage(systemStatus.server.memory.heapUsed)} / {formatMemoryUsage(systemStatus.server.memory.heapTotal)}</div>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label={t('system.monitorProcess')}>
              <Badge
                status={systemStatus.monitor.status === 'running' ? 'success' : 'error'}
                text={systemStatus.monitor.status === 'running' ? t('system.running') : t('system.stopped')}
              />
            </Descriptions.Item>
            <Descriptions.Item label={t('system.lastUpdate')}>
              {new Date(systemStatus.timestamp).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </>
  );
};

export default ServerStatus;
