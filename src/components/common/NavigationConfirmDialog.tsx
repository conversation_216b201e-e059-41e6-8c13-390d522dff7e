import React from 'react';
import { Modal, Button, Space } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface NavigationConfirmDialogProps {
  visible: boolean;
  onSaveDraft: () => void;
  onDiscard: () => void;
  onCancel: () => void;
}

/**
 * 导航确认对话框组件
 * 用于在用户尝试离开编辑页面时显示确认对话框
 */
const NavigationConfirmDialog: React.FC<NavigationConfirmDialogProps> = ({
  visible,
  onSaveDraft,
  onDiscard,
  onCancel
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={t('common.navigationConfirmTitle')}
      open={visible}
      onCancel={onCancel}
      footer={null}
      closable={true}
      maskClosable={false}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 16 }}>
        <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: 22, marginRight: 16, marginTop: 4 }} />
        <div>
          <p>{t('common.navigationConfirmContent')}</p>
        </div>
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Space>
          <Button onClick={onCancel}>
            {t('common.stayOnPage')}
          </Button>
          <Button danger onClick={onDiscard}>
            {t('common.discardChanges')}
          </Button>
          <Button type="primary" onClick={onSaveDraft}>
            {t('projects.saveDraft')}
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default NavigationConfirmDialog;
