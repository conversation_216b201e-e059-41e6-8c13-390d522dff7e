import React from 'react';
import { Empty, Button } from 'antd';
import { useTranslation } from 'react-i18next';

interface EmptyStateProps {
  title?: string;
  description?: string;
  image?: string;
  actionText?: string;
  onAction?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  image,
  actionText,
  onAction,
}) => {
  const { t } = useTranslation();

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      paddingTop: '48px',
      paddingBottom: '48px'
    }}>
      <Empty
        image={image || Empty.PRESENTED_IMAGE_DEFAULT}
        description={
          <div style={{ textAlign: 'center' }}>
            {title && <h3 style={{ fontSize: '1.125rem', fontWeight: 500, marginBottom: '8px' }}>{title}</h3>}
            {description && <p style={{ color: '#8c8c8c' }}>{description}</p>}
          </div>
        }
      />
      {actionText && onAction && (
        <Button type="primary" onClick={onAction} style={{ marginTop: '16px' }}>
          {actionText}
        </Button>
      )}
    </div>
  );
};

export default EmptyState;
