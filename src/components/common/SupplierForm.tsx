import React, { useEffect } from 'react';
import { Form, Input, Select, Tag } from 'antd';
import { useTranslation } from 'react-i18next';
import { Supplier } from '../../types/database';

const { Option } = Select;
const { TextArea } = Input;

interface SupplierFormProps {
  initialValues?: Partial<Supplier>;
  form: any;
  mode?: 'create' | 'edit';
}

/**
 * 供应商表单组件
 * 用于创建和编辑供应商信息
 */
const SupplierForm: React.FC<SupplierFormProps> = ({
  initialValues,
  form,
  mode = 'create'
}) => {
  const { t } = useTranslation();

  // 当初始值变化时，重置表单
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
        products: initialValues.products?.join(', ')
      });
    } else {
      form.resetFields();
    }
  }, [initialValues, form]);

  // 供应商类型选项
  const supplierTypeOptions = [
    { value: 'pv', label: t('databases.suppliers.pvSupplier') },
    { value: 'storage', label: t('databases.suppliers.storageSupplier') },
    { value: 'inverter', label: t('databases.suppliers.inverterSupplier') },
    { value: 'other', label: t('databases.suppliers.otherSupplier') }
  ];

  // 地区选项
  const regionOptions = [
    { value: 'japan', label: t('databases.suppliers.japan') },
    { value: 'china', label: t('databases.suppliers.china') },
    { value: 'other', label: t('databases.suppliers.other') }
  ];

  // 合作状态选项
  const cooperationStatusOptions = [
    { value: 'cooperating', label: t('databases.suppliers.cooperating') },
    { value: 'negotiating', label: t('databases.suppliers.negotiating') }
  ];

  // 评级选项
  const ratingOptions = [
    { value: '5', label: t('databases.suppliers.rating5') },
    { value: '4', label: t('databases.suppliers.rating4') },
    { value: '3', label: t('databases.suppliers.rating3') }
  ];

  return (
    <Form form={form} layout="vertical">
      <Form.Item
        name="name"
        label={t('databases.suppliers.name')}
        rules={[{ required: true, message: t('databases.suppliers.nameRequired') }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="contact"
        label={t('databases.suppliers.contact')}
        rules={[{ required: true, message: t('databases.suppliers.contactRequired') }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="phone"
        label={t('databases.suppliers.phone')}
        rules={[{ required: true, message: t('databases.suppliers.phoneRequired') }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="email"
        label={t('databases.suppliers.email')}
        rules={[
          { required: true, message: t('databases.suppliers.emailRequired') },
          { type: 'email', message: t('databases.suppliers.emailInvalid') }
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name="address"
        label={t('databases.suppliers.address')}
      >
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item
        name="products"
        label={t('databases.suppliers.products')}
        help={t('databases.suppliers.productsHelp')}
      >
        <TextArea rows={2} />
      </Form.Item>
      <Form.Item
        name="supplierType"
        label={t('databases.suppliers.supplierType')}
      >
        <Select placeholder={t('common.pleaseSelect')} allowClear>
          {supplierTypeOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item
        name="region"
        label={t('databases.suppliers.region')}
      >
        <Select placeholder={t('common.pleaseSelect')} allowClear>
          {regionOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item
        name="cooperationStatus"
        label={t('databases.suppliers.cooperationStatus')}
      >
        <Select placeholder={t('common.pleaseSelect')} allowClear>
          {cooperationStatusOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item
        name="rating"
        label={t('databases.suppliers.rating')}
      >
        <Select placeholder={t('common.pleaseSelect')} allowClear>
          {ratingOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
      </Form.Item>
    </Form>
  );
};

export default SupplierForm;
