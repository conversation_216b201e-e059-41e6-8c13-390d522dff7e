import React from 'react';
import { Tag, Tooltip } from 'antd';
import {
  CheckCircleOutlined,
  CloudOutlined,
  LaptopOutlined,
  SyncOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface SyncStatusProps {
  status: 'synced' | 'local-only' | 'server-only' | 'invalid' | undefined;
  syncing?: boolean;
  isValid?: boolean;
}

const SyncStatus: React.FC<SyncStatusProps> = ({ status, syncing = false, isValid = true }) => {
  const { t } = useTranslation();

  if (syncing) {
    return (
      <Tag icon={<SyncOutlined spin />} color="processing">
        {t('common.syncing')}
      </Tag>
    );
  }

  // 如果数据无效，显示警告标签
  if (!isValid) {
    return (
      <Tooltip title={t('common.invalidDataTooltip', '数据无效或不完整')}>
        <Tag icon={<ExclamationCircleOutlined />} color="error">
          {t('common.invalidData', '数据无效')}
        </Tag>
      </Tooltip>
    );
  }

  // 如果状态未定义，显示未知状态
  if (!status) {
    return (
      <Tooltip title={t('common.unknownStatusTooltip', '同步状态未知')}>
        <Tag icon={<QuestionCircleOutlined />} color="default">
          {t('common.unknown', '未知')}
        </Tag>
      </Tooltip>
    );
  }

  switch (status) {
    case 'synced':
      return (
        <Tooltip title={t('common.syncedTooltip', '本地和服务器都有完整数据')}>
          <Tag icon={<CheckCircleOutlined />} color="success">
            {t('common.synced', '已同步')}
          </Tag>
        </Tooltip>
      );
    case 'local-only':
      return (
        <Tooltip title={t('common.localOnlyTooltip', '仅本地有完整数据，待上传到服务器')}>
          <Tag icon={<LaptopOutlined />} color="warning">
            {t('common.localOnly', '仅本地')}
          </Tag>
        </Tooltip>
      );
    case 'server-only':
      return (
        <Tooltip title={t('common.serverOnlyTooltip', '仅服务器有完整数据，点击下载')}>
          <Tag icon={<CloudOutlined />} color="default">
            {t('common.serverOnly', '仅服务器')}
          </Tag>
        </Tooltip>
      );
    case 'invalid':
      return (
        <Tooltip title={t('common.invalidDataTooltip', '数据无效或不完整')}>
          <Tag icon={<ExclamationCircleOutlined />} color="error">
            {t('common.invalidData', '数据无效')}
          </Tag>
        </Tooltip>
      );
    default:
      return (
        <Tooltip title={t('common.unknownStatusTooltip', '同步状态未知')}>
          <Tag icon={<QuestionCircleOutlined />} color="default">
            {t('common.unknown', '未知')}
          </Tag>
        </Tooltip>
      );
  }
};

export default SyncStatus;
