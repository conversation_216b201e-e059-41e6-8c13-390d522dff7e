import React from 'react';
import { Form, InputNumber, Tooltip, Row, Col, Divider } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { StorageSpecs } from '../../../types/database';

interface StorageSpecsFormProps {
  form?: any; // 设为可选参数
  initialValues?: StorageSpecs;
}

/**
 * 储能设备规格表单组件
 */
const StorageSpecsForm: React.FC<StorageSpecsFormProps> = ({
  initialValues
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'capacity']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.capacity')}
                <Tooltip title={t('databases.equipment.storageSpecs.capacityTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.storageSpecs.capacityRequired') }]}
            initialValue={initialValues?.capacity}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={1}
              addonAfter="kWh"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['specs', 'power']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.power')}
                <Tooltip title={t('databases.equipment.storageSpecs.powerTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.storageSpecs.powerRequired') }]}
            initialValue={initialValues?.power}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={1}
              addonAfter="kW"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'efficiency']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.efficiency')}
                <Tooltip title={t('databases.equipment.storageSpecs.efficiencyTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.storageSpecs.efficiencyRequired') }]}
            initialValue={initialValues?.efficiency}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={100}
              precision={1}
              addonAfter="%"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['specs', 'cycles']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.cycles')}
                <Tooltip title={t('databases.equipment.storageSpecs.cyclesTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.storageSpecs.cyclesRequired') }]}
            initialValue={initialValues?.cycles}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['specs', 'warranty']}
            label={t('databases.equipment.storageSpecs.warranty')}
            rules={[{ required: true, message: t('databases.equipment.storageSpecs.warrantyRequired') }]}
            initialValue={initialValues?.warranty}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter={t('databases.equipment.storageSpecs.years')}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['specs', 'depthOfDischarge']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.depthOfDischarge')}
                <Tooltip title={t('databases.equipment.storageSpecs.depthOfDischargeTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: t('databases.equipment.storageSpecs.depthOfDischargeRequired') }]}
            initialValue={initialValues?.depthOfDischarge}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={100}
              precision={1}
              addonAfter="%"
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('databases.equipment.storageSpecs.dimensions')}</Divider>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            name={['specs', 'length']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.length')}
                <Tooltip title={t('databases.equipment.storageSpecs.lengthTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.length}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name={['specs', 'width']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.width')}
                <Tooltip title={t('databases.equipment.storageSpecs.widthTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.width}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name={['specs', 'height']}
            label={
              <span>
                {t('databases.equipment.storageSpecs.height')}
                <Tooltip title={t('databases.equipment.storageSpecs.heightTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            initialValue={initialValues?.height}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={0}
              addonAfter="mm"
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default StorageSpecsForm;
