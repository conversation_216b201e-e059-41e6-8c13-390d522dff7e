import React, { useEffect, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { Card, Empty, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import { IrradianceData, IrradianceHourlyData, IrradianceVisualizationOptions } from '../../types/database';

interface IrradianceChartProps {
  data: IrradianceData | null;
  options: IrradianceVisualizationOptions;
}

const IrradianceChart: React.FC<IrradianceChartProps> = ({ data, options }) => {
  const { t } = useTranslation();
  const [chartOptions, setChartOptions] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  // 添加一个状态来跟踪当前图表类型，用于强制重新渲染
  const [chartKey, setChartKey] = useState<string>('');

  // 保存上一次的指标和视图模式，用于判断是否需要完全重载图表
  const prevMetricRef = React.useRef(options.metric);
  const prevViewModeRef = React.useRef(options.viewMode);

  useEffect(() => {
    // 重置错误状态
    setError(null);

    // 验证数据
    if (!data) {
      setChartOptions(null);
      return;
    }

    if (!data.data) {
      setError('数据结构不完整，缺少data字段');
      setChartOptions(null);
      return;
    }

    if (data.data.length === 0) {
      setError('数据集为空，没有可显示的数据');
      setChartOptions(null);
      return;
    }

    // 数据有效，生成图表
    console.log('生成图表，数据条数:', data.data.length);

    // 判断是否需要完全重载图表（只有在指标或视图模式改变时才需要）
    const needFullReload = options.metric !== prevMetricRef.current ||
                          options.viewMode !== prevViewModeRef.current;

    // 更新引用值
    prevMetricRef.current = options.metric;
    prevViewModeRef.current = options.viewMode;

    if (needFullReload) {
      // 如果需要完全重载，先清空之前的图表数据
      setChartOptions(null);

      // 生成新的图表key，强制重新渲染整个图表容器
      // 这会导致ReactECharts组件完全重新创建，从而清除所有旧的图表数据
      const newChartKey = `${options.metric}-${options.viewMode}-${Date.now()}`;
      console.log('完全重载图表，key:', newChartKey);

      // 延迟一帧再生成新图表，确保旧图表已被清除
      setTimeout(() => {
        setChartKey(newChartKey);
        generateChartOptions();
      }, 10);
    } else {
      // 如果只是月份或日期变化，直接生成新的图表选项
      console.log('更新图表数据，不重载图表');
      generateChartOptions();
    }
  }, [data, options]);

  const generateChartOptions = () => {
    if (!data || !data.data || data.data.length === 0) return;

    const { metric, viewMode, month, day } = options;
    console.log(`生成图表: 指标=${metric}, 视图模式=${viewMode}, 月=${month}, 日=${day}`);

    switch (metric) {
      case 'G_Gh':
        generateGlobalHorizontalIrradianceOptions(viewMode, month, day);
        break;
      case 'Ta':
        generateTemperatureOptions(viewMode, month, day);
        break;
      case 'Sd':
        generateDirectTimeOptions(viewMode, month, day);
        break;
      default:
        setChartOptions(null);
    }
  };

  // 生成水平辐射强度图表选项
  const generateGlobalHorizontalIrradianceOptions = (
    viewMode: string,
    month?: number,
    day?: number
  ) => {
    if (!data || !data.data || data.data.length === 0) return;

    switch (viewMode) {
      case 'hourly':
        generateHourlyGlobalHorizontalIrradianceOptions(month || 1, day || 1);
        break;
      case 'daily':
        generateDailyGlobalHorizontalIrradianceOptions(month || 0);
        break;
      case 'monthly':
        generateMonthlyGlobalHorizontalIrradianceOptions();
        break;
      default:
        setChartOptions(null);
    }
  };

  // 生成小时水平辐射强度图表选项
  const generateHourlyGlobalHorizontalIrradianceOptions = (month: number, day: number) => {
    if (!data || !data.data || data.data.length === 0) return;

    // 筛选指定日期的数据
    const filteredData = month === 0
      ? data.data.filter(item => item.day === day)
      : data.data.filter(item => item.month === month && item.day === day);

    if (filteredData.length === 0) {
      setChartOptions(null);
      return;
    }

    // 按小时排序
    filteredData.sort((a, b) => a.hour - b.hour);

    // 准备图表数据
    const hours = filteredData.map(item => item.hour);
    const values = filteredData.map(item => item.globalHorizontalIrradiance);

    // 判断是否显示全年数据
    const isShowingFullYear = month === 0 || filteredData.some(item => item.month !== month);
    const monthDisplay = isShowingFullYear ? '全年' : month + '月';

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - ${monthDisplay}${day}日 水平辐射强度 (W/m²)`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}时: {c} W/m²'
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('chart.hour')
      },
      yAxis: {
        type: 'value',
        name: t('chart.irradiance'),
        axisLabel: {
          formatter: '{value} W/m²'
        }
      },
      series: [
        {
          name: t('chart.avgIrradiance'),
          data: values,
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成每日水平辐射强度图表选项
  const generateDailyGlobalHorizontalIrradianceOptions = (month: number) => {
    if (!data || !data.data || data.data.length === 0) return;

    // 准备数据
    const dailyData: { [key: string]: IrradianceHourlyData[] } = {};

    // 如果month为0，表示全年
    const filteredData = month === 0
      ? data.data
      : data.data.filter(item => item.month === month);

    // 按日期分组
    filteredData.forEach(item => {
      const key = `${item.month}-${item.day}`;
      if (!dailyData[key]) {
        dailyData[key] = [];
      }
      dailyData[key].push(item);
    });

    // 计算每日平均值和总量
    const days: string[] = [];
    const avgValues: number[] = [];
    const totalValues: number[] = [];
    let cumulativeTotal = 0; // 累计辐射量

    Object.entries(dailyData).sort((a, b) => {
      const [aMonth, aDay] = a[0].split('-').map(Number);
      const [bMonth, bDay] = b[0].split('-').map(Number);
      return aMonth === bMonth ? aDay - bDay : aMonth - bMonth;
    }).forEach(([key, dayData]) => {
      const [monthStr, dayStr] = key.split('-');
      days.push(month === 0 ? `${monthStr}月${dayStr}日` : dayStr);

      // 计算平均值 (W/m²)
      const sum = dayData.reduce((acc, item) => acc + item.globalHorizontalIrradiance, 0);
      const avg = sum / 24;
      avgValues.push(avg);

      // 计算总量 (kWh/m²)
      const dailyTotal = sum / 1000;
      cumulativeTotal += dailyTotal; // 累加每日的辐射量
      totalValues.push(cumulativeTotal); // 存储累计值
    });

    // 判断是否显示全年数据
    const isShowingFullYear = month === 0 || Object.keys(dailyData).some(key => {
      const [monthStr] = key.split('-');
      return parseInt(monthStr) !== month;
    });

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - ${isShowingFullYear ? '全年' : month + '月'} 每日平均辐射强度与辐射量`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const day = params[0].name;
          const avgValue = params[0].value;
          const totalValue = params[1].value;
          return `${day}<br/>${t('chart.avgIrradiance')}: ${avgValue.toFixed(1)} W/m²<br/>${t('chart.totalIrradiance')}: ${totalValue.toFixed(1)} kWh/m²`;
        }
      },
      legend: {
        data: [t('chart.avgIrradiance'), t('chart.totalIrradiance')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('chart.day')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.avgIrradiance'),
          position: 'left',
          axisLabel: {
            formatter: '{value} W/m²'
          }
        },
        {
          type: 'value',
          name: t('chart.totalIrradiance'),
          position: 'right',
          axisLabel: {
            formatter: '{value} kWh/m²'
          }
        }
      ],
      series: [
        {
          name: t('chart.avgIrradiance'),
          type: 'line',
          data: avgValues,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: t('chart.totalIrradiance'),
          type: 'line',
          yAxisIndex: 1,
          data: totalValues,
          smooth: true,
          itemStyle: {
            color: '#faad14'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成月度水平辐射强度图表选项
  const generateMonthlyGlobalHorizontalIrradianceOptions = () => {
    if (!data || !data.data || data.data.length === 0) return;

    // 准备数据
    const monthlyData: { [key: number]: IrradianceHourlyData[] } = {};

    // 按月份分组
    data.data.forEach(item => {
      if (!monthlyData[item.month]) {
        monthlyData[item.month] = [];
      }
      monthlyData[item.month].push(item);
    });

    // 计算每月平均值和总量
    const months: string[] = [];
    const avgValues: number[] = [];
    const totalValues: number[] = [];
    let cumulativeTotal = 0; // 累计辐射量

    for (let month = 1; month <= 12; month++) {
      months.push(`${month}月`);

      if (monthlyData[month] && monthlyData[month].length > 0) {
        // 计算平均值 (W/m²)
        const sum = monthlyData[month].reduce((acc, item) => acc + item.globalHorizontalIrradiance, 0);
        const daysInMonth = new Set(monthlyData[month].map(item => `${item.day}`)).size;
        const avg = sum / 24 / daysInMonth;
        avgValues.push(avg);

        // 计算总量 (kWh/m²)
        const monthlyTotal = sum / 1000;
        cumulativeTotal += monthlyTotal; // 累加每月的辐射量
        totalValues.push(cumulativeTotal); // 存储累计值
      } else {
        avgValues.push(0);
        totalValues.push(cumulativeTotal); // 如果没有数据，保持上个月的累计值
      }
    }

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - 全年各月平均辐射强度与辐射量`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const month = params[0].name;
          const avgValue = params[0].value;
          const totalValue = params[1].value;
          return `${month}<br/>${t('chart.avgIrradiance')}: ${avgValue.toFixed(1)} W/m²<br/>${t('chart.totalIrradiance')}: ${totalValue.toFixed(1)} kWh/m²`;
        }
      },
      legend: {
        data: [t('chart.avgIrradiance'), t('chart.totalIrradiance')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: months,
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.avgIrradiance'),
          position: 'left',
          axisLabel: {
            formatter: '{value} W/m²'
          }
        },
        {
          type: 'value',
          name: t('chart.totalIrradiance'),
          position: 'right',
          axisLabel: {
            formatter: '{value} kWh/m²'
          }
        }
      ],
      series: [
        {
          name: t('chart.avgIrradiance'),
          type: 'line',
          data: avgValues,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: t('chart.totalIrradiance'),
          type: 'line',
          yAxisIndex: 1,
          data: totalValues,
          smooth: true,
          itemStyle: {
            color: '#faad14'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成温度图表选项
  const generateTemperatureOptions = (
    viewMode: string,
    month?: number,
    day?: number
  ) => {
    if (!data || !data.data || data.data.length === 0) return;

    switch (viewMode) {
      case 'hourly':
        generateHourlyTemperatureOptions(month || 1, day || 1);
        break;
      case 'daily':
        generateDailyTemperatureOptions(month || 0);
        break;
      case 'monthly':
        generateMonthlyTemperatureOptions();
        break;
      default:
        setChartOptions(null);
    }
  };

  // 生成小时温度图表选项
  const generateHourlyTemperatureOptions = (month: number, day: number) => {
    if (!data || !data.data || data.data.length === 0) return;

    // 筛选指定日期的数据
    const filteredData = month === 0
      ? data.data.filter(item => item.day === day)
      : data.data.filter(item => item.month === month && item.day === day);

    if (filteredData.length === 0) {
      setChartOptions(null);
      return;
    }

    // 按小时排序
    filteredData.sort((a, b) => a.hour - b.hour);

    // 准备图表数据
    const hours = filteredData.map(item => item.hour);
    const values = filteredData.map(item => item.temperature);

    // 判断是否显示全年数据
    const isShowingFullYear = month === 0 || filteredData.some(item => item.month !== month);
    const monthDisplay = isShowingFullYear ? '全年' : month + '月';

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - ${monthDisplay}${day}日 温度 (℃)`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}时: {c} ℃'
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('chart.hour')
      },
      yAxis: {
        type: 'value',
        name: t('chart.temperature'),
        axisLabel: {
          formatter: '{value} ℃'
        }
      },
      series: [
        {
          name: t('chart.hourlyTemperature'),
          data: values,
          type: 'line',
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          lineStyle: {
            width: 2
          },
          itemStyle: {
            color: '#ff4d4f'  // 使用红色表示温度
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成每日温度图表选项
  const generateDailyTemperatureOptions = (month: number) => {
    if (!data || !data.data || data.data.length === 0) return;

    // 准备数据
    const dailyData: { [key: string]: IrradianceHourlyData[] } = {};

    // 如果month为0，表示全年
    const filteredData = month === 0
      ? data.data
      : data.data.filter(item => item.month === month);

    // 按日期分组
    filteredData.forEach(item => {
      const key = `${item.month}-${item.day}`;
      if (!dailyData[key]) {
        dailyData[key] = [];
      }
      dailyData[key].push(item);
    });

    // 计算每日平均值和最高/最低温度
    const days: string[] = [];
    const avgValues: number[] = [];
    const maxValues: number[] = [];
    const minValues: number[] = [];

    Object.entries(dailyData).sort((a, b) => {
      const [aMonth, aDay] = a[0].split('-').map(Number);
      const [bMonth, bDay] = b[0].split('-').map(Number);
      return aMonth === bMonth ? aDay - bDay : aMonth - bMonth;
    }).forEach(([key, dayData]) => {
      const [monthStr, dayStr] = key.split('-');
      days.push(month === 0 ? `${monthStr}月${dayStr}日` : dayStr);

      // 计算平均温度 (℃)
      const sum = dayData.reduce((acc, item) => acc + item.temperature, 0);
      const avg = sum / dayData.length;
      avgValues.push(avg);

      // 计算最高温度和最低温度
      const temperatures = dayData.map(item => item.temperature);
      const max = Math.max(...temperatures);
      const min = Math.min(...temperatures);
      maxValues.push(max);
      minValues.push(min);
    });

    // 判断是否显示全年数据
    const isShowingFullYear = month === 0 || Object.keys(dailyData).some(key => {
      const [monthStr] = key.split('-');
      return parseInt(monthStr) !== month;
    });

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - ${isShowingFullYear ? '全年' : month + '月'} 每日温度统计`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const day = params[0].name;
          const avgValue = params[0].value;
          const maxValue = params[1].value;
          const minValue = params[2].value;
          return `${day}<br/>${t('chart.avgTemperature')}: ${avgValue.toFixed(1)} ℃<br/>${t('chart.maxTemperature')}: ${maxValue.toFixed(1)} ℃<br/>${t('chart.minTemperature')}: ${minValue.toFixed(1)} ℃`;
        }
      },
      legend: {
        data: [t('chart.avgTemperature'), t('chart.maxTemperature'), t('chart.minTemperature')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('chart.day')
      },
      yAxis: {
        type: 'value',
        name: t('chart.temperature'),
        axisLabel: {
          formatter: '{value} ℃'
        }
      },
      series: [
        {
          name: t('chart.avgTemperature'),
          type: 'line',
          data: avgValues,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#ff7a45'
          }
        },
        {
          name: t('chart.maxTemperature'),
          type: 'line',
          data: maxValues,
          smooth: true,
          itemStyle: {
            color: '#ff4d4f'
          }
        },
        {
          name: t('chart.minTemperature'),
          type: 'line',
          data: minValues,
          smooth: true,
          itemStyle: {
            color: '#69c0ff'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成月度温度图表选项
  const generateMonthlyTemperatureOptions = () => {
    if (!data || !data.data || data.data.length === 0) return;

    // 准备数据
    const monthlyData: { [key: number]: IrradianceHourlyData[] } = {};

    // 按月份分组
    data.data.forEach(item => {
      if (!monthlyData[item.month]) {
        monthlyData[item.month] = [];
      }
      monthlyData[item.month].push(item);
    });

    // 计算每月平均值和最高/最低温度
    const months: string[] = [];
    const avgValues: number[] = [];
    const maxValues: number[] = [];
    const minValues: number[] = [];

    for (let month = 1; month <= 12; month++) {
      months.push(`${month}月`);

      if (monthlyData[month] && monthlyData[month].length > 0) {
        // 计算平均温度 (℃)
        const sum = monthlyData[month].reduce((acc, item) => acc + item.temperature, 0);
        const avg = sum / monthlyData[month].length;
        avgValues.push(avg);

        // 计算最高温度和最低温度
        const temperatures = monthlyData[month].map(item => item.temperature);
        const max = Math.max(...temperatures);
        const min = Math.min(...temperatures);
        maxValues.push(max);
        minValues.push(min);
      } else {
        avgValues.push(0);
        maxValues.push(0);
        minValues.push(0);
      }
    }

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - 全年各月温度统计`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const month = params[0].name;
          const avgValue = params[0].value;
          const maxValue = params[1].value;
          const minValue = params[2].value;
          return `${month}<br/>${t('chart.avgTemperature')}: ${avgValue.toFixed(1)} ℃<br/>${t('chart.maxTemperature')}: ${maxValue.toFixed(1)} ℃<br/>${t('chart.minTemperature')}: ${minValue.toFixed(1)} ℃`;
        }
      },
      legend: {
        data: [t('chart.avgTemperature'), t('chart.maxTemperature'), t('chart.minTemperature')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: months,
        name: t('chart.month')
      },
      yAxis: {
        type: 'value',
        name: t('chart.temperature'),
        axisLabel: {
          formatter: '{value} ℃'
        }
      },
      series: [
        {
          name: t('chart.avgTemperature'),
          type: 'line',
          data: avgValues,
          smooth: true,
          areaStyle: {
            opacity: 0.3
          },
          itemStyle: {
            color: '#ff7a45'
          }
        },
        {
          name: t('chart.maxTemperature'),
          type: 'line',
          data: maxValues,
          smooth: true,
          itemStyle: {
            color: '#ff4d4f'
          }
        },
        {
          name: t('chart.minTemperature'),
          type: 'line',
          data: minValues,
          smooth: true,
          itemStyle: {
            color: '#69c0ff'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成直射时间图表选项
  const generateDirectTimeOptions = (
    viewMode: string,
    month?: number,
    day?: number
  ) => {
    if (!data || !data.data || data.data.length === 0) return;

    switch (viewMode) {
      case 'hourly':
        generateHourlyDirectTimeOptions(month || 1, day || 1);
        break;
      case 'daily':
        generateDailyDirectTimeOptions(month || 0);
        break;
      case 'monthly':
        generateMonthlyDirectTimeOptions();
        break;
      default:
        setChartOptions(null);
    }
  };

  // 生成小时直射时间图表选项
  const generateHourlyDirectTimeOptions = (month: number, day: number) => {
    if (!data || !data.data || data.data.length === 0) return;

    // 筛选指定日期的数据
    const filteredData = month === 0
      ? data.data.filter(item => item.day === day)
      : data.data.filter(item => item.month === month && item.day === day);

    if (filteredData.length === 0) {
      setChartOptions(null);
      return;
    }

    // 按小时排序
    filteredData.sort((a, b) => a.hour - b.hour);

    // 准备图表数据
    const hours = filteredData.map(item => item.hour);
    const values = filteredData.map(item => item.directTime);

    // 判断是否显示全年数据
    const isShowingFullYear = month === 0 || filteredData.some(item => item.month !== month);
    const monthDisplay = isShowingFullYear ? '全年' : month + '月';

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - ${monthDisplay}${day}日 直射时间 (分钟)`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}时: {c} 分钟'
      },
      xAxis: {
        type: 'category',
        data: hours,
        name: t('chart.hour')
      },
      yAxis: {
        type: 'value',
        name: t('chart.directTime'),
        axisLabel: {
          formatter: '{value} 分钟'
        }
      },
      series: [
        {
          data: values,
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: '#ffc53d'  // 使用黄色表示直射时间
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成每日直射时间图表选项
  const generateDailyDirectTimeOptions = (month: number) => {
    if (!data || !data.data || data.data.length === 0) return;

    // 准备数据
    const dailyData: { [key: string]: IrradianceHourlyData[] } = {};

    // 如果month为0，表示全年
    const filteredData = month === 0
      ? data.data
      : data.data.filter(item => item.month === month);

    // 按日期分组
    filteredData.forEach(item => {
      const key = `${item.month}-${item.day}`;
      if (!dailyData[key]) {
        dailyData[key] = [];
      }
      dailyData[key].push(item);
    });

    // 计算每日总直射时间
    const days: string[] = [];
    const totalValues: number[] = [];
    const cumulativeValues: number[] = [];
    let cumulativeTotal = 0; // 累计直射时间

    Object.entries(dailyData).sort((a, b) => {
      const [aMonth, aDay] = a[0].split('-').map(Number);
      const [bMonth, bDay] = b[0].split('-').map(Number);
      return aMonth === bMonth ? aDay - bDay : aMonth - bMonth;
    }).forEach(([key, dayData]) => {
      const [monthStr, dayStr] = key.split('-');
      days.push(month === 0 ? `${monthStr}月${dayStr}日` : dayStr);

      // 计算总直射时间 (分钟)
      const sum = dayData.reduce((acc, item) => acc + item.directTime, 0);
      totalValues.push(sum);

      // 累计直射时间（小时）
      cumulativeTotal += sum / 60;
      cumulativeValues.push(cumulativeTotal);
    });

    // 判断是否显示全年数据
    const isShowingFullYear = month === 0 || Object.keys(dailyData).some(key => {
      const [monthStr] = key.split('-');
      return parseInt(monthStr) !== month;
    });

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - ${isShowingFullYear ? '全年' : month + '月'} 每日直射时间`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const day = params[0].name;
          const totalValue = params[0].value;
          const cumulativeValue = params[1] ? params[1].value : 0;
          const hours = Math.floor(totalValue / 60);
          const minutes = totalValue % 60;
          return `${day}<br/>${t('chart.directTime')}: ${hours}小时${minutes}分钟<br/>${t('chart.cumulativeDirectTime')}: ${cumulativeValue.toFixed(1)} 小时`;
        }
      },
      legend: {
        data: [t('chart.directTime'), t('chart.cumulativeDirectTime')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('chart.day')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.directTime'),
          position: 'left',
          axisLabel: {
            formatter: '{value} 分钟'
          }
        },
        {
          type: 'value',
          name: t('chart.cumulativeDirectTime'),
          position: 'right',
          axisLabel: {
            formatter: '{value} 小时'
          }
        }
      ],
      series: [
        {
          name: t('chart.directTime'),
          type: 'bar',
          data: totalValues,
          barWidth: '60%',
          itemStyle: {
            color: '#ffc53d'
          }
        },
        {
          name: t('chart.cumulativeDirectTime'),
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeValues,
          smooth: true,
          itemStyle: {
            color: '#fa8c16'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 生成月度直射时间图表选项
  const generateMonthlyDirectTimeOptions = () => {
    if (!data || !data.data || data.data.length === 0) return;

    // 准备数据
    const monthlyData: { [key: number]: IrradianceHourlyData[] } = {};

    // 按月份分组
    data.data.forEach(item => {
      if (!monthlyData[item.month]) {
        monthlyData[item.month] = [];
      }
      monthlyData[item.month].push(item);
    });

    // 计算每月总直射时间和累计直射时间
    const months: string[] = [];
    const totalValues: number[] = [];
    const cumulativeValues: number[] = [];
    let cumulativeTotal = 0;

    for (let month = 1; month <= 12; month++) {
      months.push(`${month}月`);

      if (monthlyData[month] && monthlyData[month].length > 0) {
        // 计算总直射时间 (小时)
        const sum = monthlyData[month].reduce((acc, item) => acc + item.directTime, 0);
        const totalHours = sum / 60; // 转换为小时
        totalValues.push(totalHours);

        // 累计直射时间（小时）
        cumulativeTotal += totalHours;
        cumulativeValues.push(cumulativeTotal);
      } else {
        totalValues.push(0);
        cumulativeValues.push(cumulativeTotal);
      }
    }

    // 设置图表选项
    const options = {
      title: {
        text: `${data.location} - 全年各月直射时间统计`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          const month = params[0].name;
          const totalValue = params[0].value;
          const cumulativeValue = params[1].value;
          return `${month}<br/>${t('chart.monthlyDirectTime')}: ${totalValue.toFixed(1)} 小时<br/>${t('chart.cumulativeDirectTime')}: ${cumulativeValue.toFixed(1)} 小时`;
        }
      },
      legend: {
        data: [t('chart.monthlyDirectTime'), t('chart.cumulativeDirectTime')],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: months,
        name: t('chart.month')
      },
      yAxis: [
        {
          type: 'value',
          name: t('chart.monthlyDirectTime'),
          position: 'left',
          axisLabel: {
            formatter: '{value} 小时'
          }
        },
        {
          type: 'value',
          name: t('chart.cumulativeDirectTime'),
          position: 'right',
          axisLabel: {
            formatter: '{value} 小时'
          }
        }
      ],
      series: [
        {
          name: t('chart.monthlyDirectTime'),
          type: 'bar',
          data: totalValues,
          barWidth: '60%',
          itemStyle: {
            color: '#ffc53d'
          }
        },
        {
          name: t('chart.cumulativeDirectTime'),
          type: 'line',
          yAxisIndex: 1,
          data: cumulativeValues,
          smooth: true,
          itemStyle: {
            color: '#fa8c16'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      }
    };

    setChartOptions(options);
  };

  // 显示错误信息或空数据提示
  if (!chartOptions) {
    return (
      <Card>
        {error ? (
          <Alert
            message="数据错误"
            description={error}
            type="error"
            showIcon
          />
        ) : (
          <Empty description={t('chart.noData')} />
        )}
      </Card>
    );
  }

  // 显示图表
  return (
    <Card>
      <div key={chartKey} style={{ position: 'relative', width: '100%', height: '400px' }}>
        <ReactECharts
          option={chartOptions}
          style={{ height: '400px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
          notMerge={true} // 不合并之前的图表选项，完全替换
          lazyUpdate={false} // 立即更新
          theme="light" // 使用浅色主题
        />
      </div>
    </Card>
  );
};

export default IrradianceChart;
