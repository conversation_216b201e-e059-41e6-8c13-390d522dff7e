import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Project, ProjectStatus } from '../../types';

// 定义项目状态类型
export interface ProjectsState {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: ProjectsState = {
  projects: [],
  currentProject: null,
  isLoading: false,
  error: null,
};

// 创建项目切片
export const projectsSlice = createSlice({
  name: 'projects',
  initialState,
  reducers: {
    // 获取项目列表开始
    fetchProjectsStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    // 获取项目列表成功
    fetchProjectsSuccess: (state, action: PayloadAction<Project[]>) => {
      state.isLoading = false;
      state.projects = action.payload;
      state.error = null;
    },
    // 获取项目列表失败
    fetchProjectsFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    // 设置当前项目
    setCurrentProject: (state, action: PayloadAction<Project | null>) => {
      state.currentProject = action.payload;
    },
    // 添加新项目
    addProject: (state, action: PayloadAction<Project>) => {
      state.projects.push(action.payload);
    },
    // 更新项目
    updateProject: (state, action: PayloadAction<Project>) => {
      const index = state.projects.findIndex(project => project.id === action.payload.id);
      if (index !== -1) {
        state.projects[index] = action.payload;
        if (state.currentProject && state.currentProject.id === action.payload.id) {
          state.currentProject = action.payload;
        }
      }
    },
    // 删除项目
    deleteProject: (state, action: PayloadAction<string>) => {
      state.projects = state.projects.filter(project => project.id !== action.payload);
      if (state.currentProject && state.currentProject.id === action.payload) {
        state.currentProject = null;
      }
    },
    // 开始项目分析
    startAnalysis: (state, action: PayloadAction<string>) => {
      const index = state.projects.findIndex(project => project.id === action.payload);
      if (index !== -1) {
        state.projects[index].status = 'analyzing';
        if (state.currentProject && state.currentProject.id === action.payload) {
          state.currentProject.status = 'analyzing';
        }
      }
    },
    // 完成项目分析
    completeAnalysis: (state, action: PayloadAction<{ id: string; results: Record<string, any> }>) => {
      const index = state.projects.findIndex(project => project.id === action.payload.id);
      if (index !== -1) {
        state.projects[index].status = 'completed';
        state.projects[index].analysisResults = action.payload.results;
        if (state.currentProject && state.currentProject.id === action.payload.id) {
          state.currentProject.status = 'completed';
          state.currentProject.analysisResults = action.payload.results;
        }
      }
    },
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
});

// 导出actions
export const {
  fetchProjectsStart,
  fetchProjectsSuccess,
  fetchProjectsFailure,
  setCurrentProject,
  addProject,
  updateProject,
  deleteProject,
  startAnalysis,
  completeAnalysis,
  clearError,
} = projectsSlice.actions;

// 导出reducer
export default projectsSlice.reducer;
