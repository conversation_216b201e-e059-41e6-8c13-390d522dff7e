import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 定义设置状态的类型
export interface SettingsState {
  language: string;
  currency: string;
  timezone: string;
  theme: 'light' | 'dark' | 'system';
  siteName: string;
  siteLogo: string;
}

// 初始状态
const initialState: SettingsState = {
  language: 'zh', // 默认语言为中文
  currency: 'JPY', // 默认货币为日元
  timezone: 'Asia/Tokyo', // 默认时区为东京
  theme: 'light', // 默认主题为浅色
  siteName: '光储投资分析', // 默认网站名称
  siteLogo: '', // 默认网站logo
};

// 创建设置切片
export const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // 设置语言
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
    // 设置货币
    setCurrency: (state, action: PayloadAction<string>) => {
      state.currency = action.payload;
    },
    // 设置时区
    setTimezone: (state, action: PayloadAction<string>) => {
      state.timezone = action.payload;
    },
    // 设置主题
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    // 设置网站名称
    setSiteName: (state, action: PayloadAction<string>) => {
      state.siteName = action.payload;
    },
    // 设置网站logo
    setSiteLogo: (state, action: PayloadAction<string>) => {
      state.siteLogo = action.payload;
    },
  },
});

// 导出actions
export const {
  setLanguage,
  setCurrency,
  setTimezone,
  setTheme,
  setSiteName,
  setSiteLogo
} = settingsSlice.actions;

// 导出reducer
export default settingsSlice.reducer;
