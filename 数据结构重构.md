现在的项目数据结构太乱了，尤其是小时数据，我要重新设计：
{
  "id": "23ec59c2-1d13-4455-bc0f-0ea60581bea9",
  "status": "completed",
  "createdAt": "2025-05-09T08:03:47.919Z",
  "updatedAt": "2025-05-10T15:33:16.761Z",
  "basicInfo": {
    "regionCode": "kanto",
    "prefectureCode": "saitama",
    "name": "高总家",
    "capacity": 15,
    "region": "关东地方",
    "prefecture": "埼玉县",
    "installationType": "rooftop",
    "projectType": "residential",
    "connectionType": "hybrid",
    "description": ""
  },
  "pvModules": [
    {
      "id": "1746786723034",
      "name": "北向屋顶",
      "manufacturer": "晶科",
      "model": "JKM605N-78HL4-BDV",
      "quantity": 14,
      "power": 605,
      "efficiency": 21.6,
      "area": 2.79,
      "price": 12000,
      "angle": 31,
      "orientation": "north",
      "orientationAngle": 180,
      "equipmentId": "2247571d-9faf-48bf-9494-9b1189931e11"
    },
    {
      "id": "1746857551990",
      "equipmentId": "2247571d-9faf-48bf-9494-9b1189931e11",
      "name": "南向屋顶",
      "manufacturer": "晶科",
      "model": "JKM605N-78HL4-BDV",
      "quantity": 14,
      "power": 605,
      "efficiency": 21.6,
      "area": 2.79,
      "price": 12000,
      "angle": 31,
      "orientation": "south",
      "orientationAngle": 0
    }
  ],
  "energyStorage": [
    {
      "id": "1746786739541",
      "name": "16度电储能包",
      "manufacturer": "星翼",
      "model": "XY-16.1H",
      "quantity": 1,
      "capacity": 16.1,
      "power": 8,
      "efficiency": 93,
      "cycles": 6000,
      "price": 200000
    }
  ],
  "inverters": [
    {
      "id": "1746786745087",
      "name": "古瑞瓦特12KW逆变器",
      "manufacturer": "古瑞瓦特",
      "model": "SPE12000ES",
      "quantity": 1,
      "power": 12,
      "efficiency": 95.5,
      "price": 35000
    }
  ],
  "otherInvestments": [],
  "name": "高总家",
  "location": "",
  "capacity": 15,
  "irradianceDataId": "dd7099a9-e9d3-48d0-abed-172f6af2aec9",
  "electricityPriceId": "a8b13f69-a657-4319-aed6-6080c6754836",
  "electricityUsage": {
    "type": "sameEveryday",
    "data": [
      {
        "hour": 0,
        "value": 1
      },
      {
        "hour": 1,
        "value": 2
      },
      {
        "hour": 2,
        "value": 3
      },
      {
        "hour": 3,
        "value": 4
      },
      {
        "hour": 4,
        "value": 5
      },
      {
        "hour": 5,
        "value": 6
      },
      {
        "hour": 6,
        "value": 7
      },
      {
        "hour": 7,
        "value": 8
      },
      {
        "hour": 8,
        "value": 7
      },
      {
        "hour": 9,
        "value": 6
      },
      {
        "hour": 10,
        "value": 5
      },
      {
        "hour": 11,
        "value": 4
      },
      {
        "hour": 12,
        "value": 5
      },
      {
        "hour": 13,
        "value": 6
      },
      {
        "hour": 14,
        "value": 7
      },
      {
        "hour": 15,
        "value": 8
      },
      {
        "hour": 16,
        "value": 9
      },
      {
        "hour": 17,
        "value": 7
      },
      {
        "hour": 18,
        "value": 5
      },
      {
        "hour": 19,
        "value": 6
      },
      {
        "hour": 20,
        "value": 4
      },
      {
        "hour": 21,
        "value": 3
      },
      {
        "hour": 22,
        "value": 2
      },
      {
        "hour": 23,
        "value": 1
      }
    ]
  },
以上部分为项目的"基础数据"，这次不作调整，这一部分也将成为index.json里面对每个项目的摘要，这次要改变的是之后的分析结果，分析结果将只有小时数据，一共包含8760个小时数据，没有其他内容，前端页面需要的结论都由这8760个小时数据即时运算的到：
  "analysisResults": {
    "hourlyData": [
      {
        "hour": 一天的第几小时,
        "day": 一个月的第几天,
        "month": 一年的第几月,
        "pvGeneration": { 
          "1746786723034": 第一组光伏的发电量，算法已经写好，沿用源代码已有算法,
          "1746857551990": ，算法已经写好，沿用源代码已有算法,
          ...第N组光伏的发电量
        },
        "storageCharge": 储能充电量，放电为负(该数值是光伏发电总量减去本小时用户用电量，充电最大值不能超过储能装机容量减去上一个小时的储能剩余电量，放电最大值（负的最小值）不能超过上个小时的储能剩余电量),
        "storageCapacity": 储能本小时结束时的电量（等于前一个小时的数值加上本小时的充电量"storageCharge",）,
        "electricityConsumption": 用户本小时的用电量，通过"electricityUsage"计算得到，算法已经有了，沿用,
        "gridExport": （只能正值或0）上网电量，数值等于光伏发电总量-用户用电量-储能的充电量，如果小于0，那么此值写0，"gridImport"写负的此值,
        "gridImport": （只能正值或0）电网购电电量，数值等于用户用电量+储能的充电量-光伏发电总量，如果小于0，那么此值写0，"gridExport"写负的此值
      },
      剩余8759个小时的数据
   }

以上是保存在项目数据文件的数据内容，其他数据通过前端页面的运算得到，其中：

1. 本小时每组光伏的收益：这里的收益指的是仅光伏的收益，也就是不考虑储能时的光伏收益，算法是：当光伏总发电量小于等于用户用电量，每组光伏收益=本小时电价*每组光伏发电量；当光伏总发电量大于本地用电量，总收益=本小时电价*用户用电量+本小时上网电价*（光伏发电总量-用户用电量），每组光伏收益=本组光伏发电量比例*总收益。

2. 本小时储能收益：通过"storageCharge"的数值得到，算法：当"storageCharge"大于零，也就是充电，收益=-上网电价*"storageCharge"；当"storageCharge"小于零，也就是放电，收益=本小时电网电价*（-"storageCharge"）

3. 上网收入：本小时上网电价*"gridExport"

以上涉及到数据结构的调整，以及数据的计算方法的调整，请充分阅读现有代码，将涉及到的代码全部更新。
最后输入一个“数据结构重构.md”文档，记录本次调整的详细情况

另外，所有的数据，在json文件里面只保留3位小数。
按照



这个截图上的标注，整个项目详情json要删除"analysisResults"中的"dailyData"、"monthlyData"、"yearlyData"、"moduleResults"

# 项目数据结构重构文档

## 重构目标

本次重构的主要目标是简化项目数据结构，特别是分析结果部分，使其更加清晰和高效。具体目标包括：

1. 简化项目分析结果，只保留小时数据，删除日数据、月数据、年数据和模块结果
2. 修改小时数据结构，使其包含每个光伏组件的发电量
3. 确保所有数值在JSON文件中只保留3位小数
4. 前端页面需要的结论都由8760个小时数据即时计算得到

## 数据结构变更

### 项目小时数据 (ProjectHourlyData)

**变更前：**

```typescript
export interface ProjectHourlyData {
  // 时间标识
  hour: number;       // 小时 (0-23)
  day: number;        // 日 (1-31)
  month: number;      // 月 (1-12)

  // 光伏发电数据
  pvGeneration: number;  // 本小时光伏发电量 (kWh)

  // 储能数据
  storageCharge: number;     // 本小时储能充电量 (kWh，负数表示放电)
  storageCapacity: number;   // 本小时结束时储能的存储电量 (kWh)

  // 用电数据 - 可能是数字或对象
  electricityConsumption: number | {
    pv: number;      // 光伏供电 (kWh)
    storage: number; // 储能供电 (kWh)
    grid: number;    // 电网供电 (kWh)
  };  // 本小时用户用电量 (kWh)

  // 电网交互数据
  gridExport: number;  // 本小时上网电量 (kWh)
  gridImport: number;  // 本小时从电网购电量 (kWh)

  // 经济效益数据
  gridExportIncome: number;  // 本小时上网售电收入 (JPY)
  pvBenefit: number;         // 本小时光伏收益 (JPY)
  storageBenefit: number;    // 本小时储能收益 (JPY)

  // 总收益
  totalBenefit: number;      // 本小时总收益 (JPY)
}
```

**变更后：**

```typescript
export interface ProjectHourlyData {
  // 时间标识
  hour: number;       // 小时 (0-23)
  day: number;        // 日 (1-31)
  month: number;      // 月 (1-12)

  // 光伏发电数据 - 每个组件的发电量
  pvGeneration: {
    [moduleId: string]: number;  // 每个光伏组件的发电量 (kWh)
  };  

  // 储能数据
  storageCharge: number;     // 本小时储能充电量 (kWh，正值表示充电，负值表示放电)
  storageCapacity: number;   // 本小时结束时储能的存储电量 (kWh)

  // 用电数据
  electricityConsumption: number;  // 本小时用户用电量 (kWh)

  // 电网交互数据
  gridExport: number;  // 本小时上网电量 (kWh)，只能为正值或0
  gridImport: number;  // 本小时从电网购电量 (kWh)，只能为正值或0
}
```

### 项目分析结果 (ProjectAnalysisResults)

**变更前：**

```typescript
export interface ProjectAnalysisResults {
  // 详细数据
  hourlyData: ProjectHourlyData[];   // 8760个小时数据点
  dailyData: ProjectDailyData[];     // 365个日数据点
  monthlyData: ProjectMonthlyData[]; // 12个月数据点
  yearlyData: ProjectYearlyData;     // 年度汇总数据

  // 光伏组件单独分析结果
  moduleResults?: {
    [moduleId: string]: {
      hourlyData: any[]; // 每个组件的8760个小时数据点
      dailyData: any[];  // 每个组件的365个日数据点
      monthlyData: any[]; // 每个组件的12个月数据点
      yearlyData: {
        pvGeneration: number; // 组件全年发电量
        pvBenefit: number;    // 组件全年收益
      };
    }
  };

  // 分析状态
  analysisCompleted: boolean;        // 分析是否完成
  analysisDate: string;              // 分析日期
}
```

**变更后：**

```typescript
export interface ProjectAnalysisResults {
  // 详细数据 - 只保留小时数据
  hourlyData: ProjectHourlyData[];   // 8760个小时数据点

  // 分析状态
  analysisCompleted: boolean;        // 分析是否完成
  analysisDate: string;              // 分析日期
}
```

## 计算逻辑变更

### 光伏发电量计算

- 变更前：每个小时的光伏发电量是所有组件发电量的总和，存储为单个数值
- 变更后：每个小时的光伏发电量按组件ID分别存储，形成一个对象

### 储能充放电计算

- 变更前：储能充电和放电分别计算，放电量存储为负值
- 变更后：统一使用storageCharge字段，正值表示充电，负值表示放电

### 电网交互计算

- 变更前：电网交互可能同时有导入和导出
- 变更后：电网交互在任一时刻只能有导入或导出，不能同时存在

### 数据精度控制

- 所有数值在JSON文件中只保留3位小数，使用parseFloat(value.toFixed(3))实现

## 前端计算逻辑

以下数据将由前端根据小时数据即时计算得到：

1. **光伏收益计算**：
   - 当光伏总发电量小于等于用户用电量时：每组光伏收益 = 本小时电价 * 每组光伏发电量
   - 当光伏总发电量大于用户用电量时：总收益 = 本小时电价 * 用户用电量 + 本小时上网电价 * (光伏发电总量 - 用户用电量)，每组光伏收益 = 本组光伏发电量比例 * 总收益

2. **储能收益计算**：
   - 当storageCharge大于零（充电）：收益 = -上网电价 * storageCharge
   - 当storageCharge小于零（放电）：收益 = 本小时电网电价 * (-storageCharge)

3. **上网收入计算**：
   - 上网收入 = 本小时上网电价 * gridExport

## 删除的数据

本次重构删除了以下数据：

1. 日数据 (dailyData)
2. 月数据 (monthlyData)
3. 年数据 (yearlyData)
4. 模块结果 (moduleResults)
5. 经济效益数据 (gridExportIncome, pvBenefit, storageBenefit, totalBenefit)

这些数据将由前端根据小时数据即时计算得到，不再存储在项目数据中。

## 影响范围

本次重构主要影响以下文件：

1. `src/types/projectData.ts`：修改数据类型定义
2. `src/services/projectAnalysisService.ts`：修改分析逻辑

前端页面需要相应调整，以适应新的数据结构，并实现即时计算功能。
