#!/bin/bash

# 超级监控守护进程 - 监控监控脚本本身
# 确保监控脚本始终运行，如果监控脚本崩溃则自动重启

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 配置变量
MONITOR_SCRIPT="./monitor_server.sh"
CHECK_INTERVAL=60  # 检查间隔（秒）
LOG_FILE="super_monitor.log"
PID_FILE=".super_monitor.pid"

# 打印函数
print_info() {
    echo -e "${BLUE}[SUPER]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUPER]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[SUPER]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[SUPER]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 检查监控脚本是否运行
check_monitor() {
    if [ -f "server/.monitor.pid" ]; then
        local monitor_pid=$(cat server/.monitor.pid 2>/dev/null)
        if [ -n "$monitor_pid" ] && ps -p "$monitor_pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 检查服务器是否运行
check_server() {
    if [ -f "server/.server.pid" ]; then
        local server_pid=$(cat server/.server.pid 2>/dev/null)
        if [ -n "$server_pid" ] && ps -p "$server_pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 启动监控脚本
start_monitor() {
    print_info "启动监控脚本..."
    
    # 清理旧的监控进程
    if [ -f "server/.monitor.pid" ]; then
        local old_pid=$(cat server/.monitor.pid 2>/dev/null)
        if [ -n "$old_pid" ]; then
            kill "$old_pid" 2>/dev/null || true
        fi
        rm -f server/.monitor.pid
    fi
    
    # 启动新的监控进程
    nohup $MONITOR_SCRIPT monitor > /dev/null 2>&1 &
    local monitor_pid=$!
    
    # 等待监控脚本启动
    sleep 3
    
    if ps -p "$monitor_pid" > /dev/null 2>&1; then
        print_success "监控脚本启动成功 (PID: $monitor_pid)"
        return 0
    else
        print_error "监控脚本启动失败"
        return 1
    fi
}

# 启动服务器
start_server() {
    print_info "启动服务器..."
    if ./start_server.sh; then
        print_success "服务器启动成功"
        return 0
    else
        print_error "服务器启动失败"
        return 1
    fi
}

# 超级监控循环
super_monitor_loop() {
    print_info "超级监控守护进程启动"
    
    local monitor_restart_count=0
    local server_restart_count=0
    local last_monitor_restart=0
    local last_server_restart=0
    
    # 信号处理
    trap 'print_info "超级监控收到终止信号，正在退出..."; exit 0' TERM INT
    
    while true; do
        local current_time=$(date +%s)
        
        # 检查监控脚本
        if ! check_monitor; then
            print_warning "监控脚本未运行"
            
            # 避免频繁重启（至少间隔2分钟）
            if [ $((current_time - last_monitor_restart)) -gt 120 ]; then
                if start_monitor; then
                    monitor_restart_count=$((monitor_restart_count + 1))
                    last_monitor_restart=$current_time
                    print_info "监控脚本重启次数: $monitor_restart_count"
                fi
            else
                print_warning "监控脚本重启冷却期，等待中..."
            fi
        fi
        
        # 检查服务器（作为备用检查）
        if ! check_server; then
            print_warning "服务器未运行"
            
            # 如果监控脚本正在运行，让它处理服务器重启
            if check_monitor; then
                print_info "监控脚本正在运行，由其处理服务器重启"
            else
                # 监控脚本也不在运行，直接重启服务器
                if [ $((current_time - last_server_restart)) -gt 300 ]; then
                    if start_server; then
                        server_restart_count=$((server_restart_count + 1))
                        last_server_restart=$current_time
                        print_info "服务器重启次数: $server_restart_count"
                    fi
                fi
            fi
        fi
        
        # 定期报告状态（每小时）
        if [ $((current_time % 3600)) -eq 0 ]; then
            print_info "状态报告 - 监控重启: $monitor_restart_count 次, 服务器重启: $server_restart_count 次"
        fi
        
        sleep $CHECK_INTERVAL
    done
}

# 停止超级监控
stop_super_monitor() {
    if [ -f "$PID_FILE" ]; then
        local super_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$super_pid" ] && ps -p "$super_pid" > /dev/null 2>&1; then
            print_info "停止超级监控进程 (PID: $super_pid)"
            kill "$super_pid" 2>/dev/null || true
        fi
        rm -f "$PID_FILE"
    fi
    print_success "超级监控已停止"
}

# 显示状态
show_status() {
    print_info "=== 超级监控状态 ==="
    
    # 检查超级监控
    if [ -f "$PID_FILE" ]; then
        local super_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$super_pid" ] && ps -p "$super_pid" > /dev/null 2>&1; then
            print_success "超级监控运行中 (PID: $super_pid)"
        else
            print_error "超级监控进程不存在"
        fi
    else
        print_error "超级监控PID文件不存在"
    fi
    
    # 检查监控脚本
    if check_monitor; then
        local monitor_pid=$(cat server/.monitor.pid 2>/dev/null)
        print_success "监控脚本运行中 (PID: $monitor_pid)"
    else
        print_error "监控脚本未运行"
    fi
    
    # 检查服务器
    if check_server; then
        local server_pid=$(cat server/.server.pid 2>/dev/null)
        print_success "服务器运行中 (PID: $server_pid)"
    else
        print_error "服务器未运行"
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            # 保存超级监控PID
            echo $$ > "$PID_FILE"
            super_monitor_loop
            ;;
        "stop")
            stop_super_monitor
            ;;
        "status")
            show_status
            ;;
        "restart")
            stop_super_monitor
            sleep 2
            nohup "$0" start > /dev/null 2>&1 &
            print_success "超级监控重启完成"
            ;;
        *)
            echo "用法: $0 {start|stop|status|restart}"
            echo "  start   - 启动超级监控"
            echo "  stop    - 停止超级监控"
            echo "  status  - 显示状态"
            echo "  restart - 重启超级监控"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
