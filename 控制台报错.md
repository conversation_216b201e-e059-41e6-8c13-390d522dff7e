index-hE8dWJkj.js:510 缓存管理器: 设置最大缓存大小为 25.6 MB
index-hE8dWJkj.js:578 开始获取设备列表
index-hE8dWJkj.js:510 🚀 发送GET请求到: http://localhost:3001/api/equipment {page: 1, pageSize: 10}
index-hE8dWJkj.js:798 开始获取系统设置
index-hE8dWJkj.js:510 🚀 发送GET请求到: http://localhost:3001/api/settings undefined
lookup.js:42 [Deprecation] Listener added for a 'DOMNodeInserted' mutation event. Support for this event type has been removed, and this event will no longer be fired. See https://chromestatus.com/feature/5083947249172480 for more information.
（匿名） @ lookup.js:42
index-hE8dWJkj.js:510 ❌ 响应错误: /equipment aa {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 📡 请求已发送但没有收到响应: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 30000, withCredentials: false, upload: XMLHttpRequestUpload, …}
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 📡 请求配置: {transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 30000, …}
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 🔍 正在检查服务器连接...
index-hE8dWJkj.js:510 网络错误，无法连接到服务器
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:578 获取设备列表失败: aa {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
$y @ index-hE8dWJkj.js:578
await in $y
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:578 从本地获取到数据条数: 0
index-hE8dWJkj.js:507 
            
            
           GET http://localhost:3001/api/equipment?page=1&pageSize=10 net::ERR_CONNECTION_REFUSED
（匿名） @ index-hE8dWJkj.js:507
xhr @ index-hE8dWJkj.js:507
JK @ index-hE8dWJkj.js:509
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 ❌ 响应错误: /settings aa {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 📡 请求已发送但没有收到响应: XMLHttpRequest {onreadystatechange: null, readyState: 4, timeout: 30000, withCredentials: false, upload: XMLHttpRequestUpload, …}
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 📡 请求配置: {transitional: {…}, adapter: Array(3), transformRequest: Array(1), transformResponse: Array(1), timeout: 30000, …}
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 🔍 正在检查服务器连接...
index-hE8dWJkj.js:510 网络错误，无法连接到服务器
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:798 获取系统设置失败: aa {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
p1e @ index-hE8dWJkj.js:798
await in p1e
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:798 从服务器加载设置失败: aa {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
（匿名） @ index-hE8dWJkj.js:798
await in （匿名）
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:507 
            
            
           GET http://localhost:3001/api/settings net::ERR_CONNECTION_REFUSED
（匿名） @ index-hE8dWJkj.js:507
xhr @ index-hE8dWJkj.js:507
JK @ index-hE8dWJkj.js:509
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 
            
            
           GET http://localhost:3001/api/health net::ERR_CONNECTION_REFUSED
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 ❌ 服务器连接测试失败: TypeError: Failed to fetch
    at index-hE8dWJkj.js:510:6002
    at async Lm.request (index-hE8dWJkj.js:509:1978)
    at async $y (index-hE8dWJkj.js:578:9230)
    at async C (index-hE8dWJkj.js:798:35088)
（匿名） @ index-hE8dWJkj.js:510
Promise.catch
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
$y @ index-hE8dWJkj.js:578
C @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 💡 提示: 请确保服务器正在运行，并且端口为3001
index-hE8dWJkj.js:510 
            
            
           GET http://localhost:3001/api/health net::ERR_CONNECTION_REFUSED
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 ❌ 服务器连接测试失败: TypeError: Failed to fetch
    at index-hE8dWJkj.js:510:6002
    at async Lm.request (index-hE8dWJkj.js:509:1978)
    at async Module.p1e [as getSettings] (index-hE8dWJkj.js:798:51504)
    at async index-hE8dWJkj.js:798:76123
（匿名） @ index-hE8dWJkj.js:510
Promise.catch
（匿名） @ index-hE8dWJkj.js:510
Promise.then
_request @ index-hE8dWJkj.js:510
request @ index-hE8dWJkj.js:509
Lm.<computed> @ index-hE8dWJkj.js:510
（匿名） @ index-hE8dWJkj.js:505
as @ index-hE8dWJkj.js:510
p1e @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
Promise.then
（匿名） @ index-hE8dWJkj.js:798
（匿名） @ index-hE8dWJkj.js:798
IS @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
xf @ index-hE8dWJkj.js:48
c9 @ index-hE8dWJkj.js:48
T9 @ index-hE8dWJkj.js:48
uB @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
$9 @ index-hE8dWJkj.js:48
$S @ index-hE8dWJkj.js:48
_9 @ index-hE8dWJkj.js:48
C9 @ index-hE8dWJkj.js:48
v9 @ index-hE8dWJkj.js:48
g9 @ index-hE8dWJkj.js:48
O9 @ index-hE8dWJkj.js:48
M @ index-hE8dWJkj.js:25
index-hE8dWJkj.js:510 💡 提示: 请确保服务器正在运行，并且端口为3001