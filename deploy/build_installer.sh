#!/bin/bash

# 光伏+储能项目经济性分析系统安装包构建脚本
# 此脚本用于构建macOS和Windows安装包

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 当前目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$SCRIPT_DIR/build"
DIST_DIR="$SCRIPT_DIR/dist"

# 输出函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "光伏+储能项目经济性分析系统安装包构建脚本"
    echo ""
    echo "用法: ./build_installer.sh [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                显示帮助信息"
    echo "  --macos                   仅构建macOS安装包"
    echo "  --windows                 仅构建Windows安装包"
    echo ""
    echo "示例:"
    echo "  ./build_installer.sh                构建所有平台的安装包"
    echo "  ./build_installer.sh --macos        仅构建macOS安装包"
    echo "  ./build_installer.sh --windows      仅构建Windows安装包"
    echo ""
}

# 解析命令行参数
parse_args() {
    BUILD_MACOS=true
    BUILD_WINDOWS=true

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --macos)
                BUILD_MACOS=true
                BUILD_WINDOWS=false
                shift
                ;;
            --windows)
                BUILD_MACOS=false
                BUILD_WINDOWS=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 准备构建环境
prepare_build_env() {
    print_info "准备构建环境..."

    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    mkdir -p "$DIST_DIR"

    # 清理旧文件
    rm -rf "$BUILD_DIR"/*
    rm -rf "$DIST_DIR"/*

    print_success "构建环境准备完成"
}

# 构建项目
build_project() {
    print_info "构建项目..."

    # 进入项目根目录
    cd "$PROJECT_ROOT"

    # 安装依赖
    print_info "安装依赖..."
    npm install

    if [ $? -ne 0 ]; then
        print_error "依赖安装失败"
        exit 1
    fi

    # 构建项目
    print_info "构建项目..."
    npm run build

    if [ $? -ne 0 ]; then
        print_error "项目构建失败"
        exit 1
    fi

    print_success "项目构建完成"
}

# 准备安装包文件
prepare_installer_files() {
    print_info "准备安装包文件..."

    # 创建安装包目录结构
    mkdir -p "$BUILD_DIR/pv-analyzer"
    mkdir -p "$BUILD_DIR/pv-analyzer/server"

    # 复制前端构建文件
    cp -r "$PROJECT_ROOT/dist" "$BUILD_DIR/pv-analyzer/"

    # 复制服务器文件
    cp -r "$PROJECT_ROOT/server/src" "$BUILD_DIR/pv-analyzer/server/"
    cp "$PROJECT_ROOT/server/package.json" "$BUILD_DIR/pv-analyzer/server/"
    cp "$PROJECT_ROOT/server/package-lock.json" "$BUILD_DIR/pv-analyzer/server/"

    # 复制部署脚本
    cp "$SCRIPT_DIR/deploy_macos.sh" "$BUILD_DIR/pv-analyzer/"
    cp "$SCRIPT_DIR/deploy_windows.bat" "$BUILD_DIR/pv-analyzer/"
    cp "$SCRIPT_DIR/README.md" "$BUILD_DIR/pv-analyzer/"

    # 创建数据目录
    mkdir -p "$BUILD_DIR/pv-analyzer/server/data"
    mkdir -p "$BUILD_DIR/pv-analyzer/server/uploads"

    print_success "安装包文件准备完成"
}

# 构建macOS安装包
build_macos_installer() {
    if [ "$BUILD_MACOS" = false ]; then
        return
    fi

    print_info "构建macOS安装包..."

    # 创建macOS安装包目录
    mkdir -p "$BUILD_DIR/macos"

    # 复制文件到macOS安装包目录
    cp -r "$BUILD_DIR/pv-analyzer" "$BUILD_DIR/macos/"

    # 创建安装脚本
    cat > "$BUILD_DIR/macos/install.sh" << 'EOF'
#!/bin/bash

# 光伏+储能项目经济性分析系统安装脚本 (macOS版)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 输出函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 安装目录
INSTALL_DIR="$HOME/Applications/PVAnalyzer"

# 显示欢迎信息
echo "=================================================="
echo "  光伏+储能项目经济性分析系统安装程序 (macOS版)"
echo "=================================================="
echo ""

# 创建安装目录
print_info "创建安装目录: $INSTALL_DIR"
mkdir -p "$INSTALL_DIR"

# 复制文件
print_info "复制文件..."
cp -r pv-analyzer/* "$INSTALL_DIR/"

# 设置权限
print_info "设置权限..."
chmod +x "$INSTALL_DIR/deploy_macos.sh"

# 创建桌面快捷方式
print_info "创建桌面快捷方式..."
cat > "$HOME/Desktop/PVAnalyzer.command" << 'EOL'
#!/bin/bash
cd "$HOME/Applications/PVAnalyzer"
./deploy_macos.sh
EOL

chmod +x "$HOME/Desktop/PVAnalyzer.command"

print_success "安装完成！"
echo ""
echo "您可以通过以下方式启动应用："
echo "1. 双击桌面上的 PVAnalyzer 图标"
echo "2. 在终端中运行："
echo "   cd $INSTALL_DIR"
echo "   ./deploy_macos.sh"
echo ""
echo "感谢您使用光伏+储能项目经济性分析系统！"
EOF

    # 设置权限
    chmod +x "$BUILD_DIR/macos/install.sh"

    # 创建DMG文件
    if command -v hdiutil >/dev/null 2>&1; then
        print_info "创建DMG文件..."
        hdiutil create -volname "PVAnalyzer" -srcfolder "$BUILD_DIR/macos" -ov -format UDZO "$DIST_DIR/PVAnalyzer-macOS.dmg"
        print_success "DMG文件创建成功: $DIST_DIR/PVAnalyzer-macOS.dmg"
    else
        print_warning "未找到hdiutil命令，跳过DMG文件创建"
        # 创建ZIP文件作为替代
        print_info "创建ZIP文件..."
        cd "$BUILD_DIR"
        zip -r "$DIST_DIR/PVAnalyzer-macOS.zip" macos
        print_success "ZIP文件创建成功: $DIST_DIR/PVAnalyzer-macOS.zip"
    fi
}

# 构建Windows安装包
build_windows_installer() {
    if [ "$BUILD_WINDOWS" = false ]; then
        return
    fi

    print_info "构建Windows安装包..."

    # 创建Windows安装包目录
    mkdir -p "$BUILD_DIR/windows"

    # 复制文件到Windows安装包目录
    cp -r "$BUILD_DIR/pv-analyzer" "$BUILD_DIR/windows/"

    # 创建安装脚本
    cat > "$BUILD_DIR/windows/install.bat" << 'EOF'
@echo off
setlocal enabledelayedexpansion

:: 光伏+储能项目经济性分析系统安装脚本 (Windows版)

:: 设置颜色
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 输出函数
:print_info
echo %BLUE%[信息]%NC% %~1
exit /b 0

:print_success
echo %GREEN%[成功]%NC% %~1
exit /b 0

:print_warning
echo %YELLOW%[警告]%NC% %~1
exit /b 0

:print_error
echo %RED%[错误]%NC% %~1
exit /b 0

:: 安装目录
set "INSTALL_DIR=%USERPROFILE%\PVAnalyzer"

:: 显示欢迎信息
echo ==================================================
echo   光伏+储能项目经济性分析系统安装程序 (Windows版)
echo ==================================================
echo.

:: 创建安装目录
call :print_info "创建安装目录: %INSTALL_DIR%"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

:: 复制文件
call :print_info "复制文件..."
xcopy /E /I /Y "pv-analyzer\*" "%INSTALL_DIR%\"

:: 创建桌面快捷方式
call :print_info "创建桌面快捷方式..."
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\PVAnalyzer.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\deploy_windows.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

call :print_success "安装完成！"
echo.
echo 您可以通过以下方式启动应用：
echo 1. 双击桌面上的 PVAnalyzer 图标
echo 2. 在命令提示符中运行：
echo    cd %INSTALL_DIR%
echo    deploy_windows.bat
echo.
echo 感谢您使用光伏+储能项目经济性分析系统！

pause
exit /b 0
EOF

    # 创建ZIP文件
    print_info "创建ZIP文件..."
    cd "$BUILD_DIR"
    zip -r "$DIST_DIR/PVAnalyzer-Windows.zip" windows
    print_success "ZIP文件创建成功: $DIST_DIR/PVAnalyzer-Windows.zip"
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 显示欢迎信息
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统安装包构建脚本"
    echo "=================================================="
    echo ""
    
    # 准备构建环境
    prepare_build_env
    
    # 构建项目
    build_project
    
    # 准备安装包文件
    prepare_installer_files
    
    # 构建macOS安装包
    build_macos_installer
    
    # 构建Windows安装包
    build_windows_installer
    
    print_success "安装包构建完成！"
    echo ""
    echo "安装包位置: $DIST_DIR"
    echo ""
}

# 执行主函数
main "$@"
