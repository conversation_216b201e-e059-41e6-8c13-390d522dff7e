#!/bin/bash

# 设置系统服务脚本
# 光伏+储能项目经济性分析系统

set -e

# 配置变量
SERVICE_NAME="pv-analysis-server"
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
MONITOR_SCRIPT="$PROJECT_DIR/server_monitor.sh"
CRON_JOB="*/5 * * * * $MONITOR_SCRIPT check || $MONITOR_SCRIPT restart"

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 设置cron任务
setup_cron() {
    print_info "设置cron任务..."
    
    # 检查是否已存在相同的cron任务
    if crontab -l 2>/dev/null | grep -q "$SERVICE_NAME"; then
        print_warning "cron任务已存在，先删除旧任务"
        crontab -l 2>/dev/null | grep -v "$SERVICE_NAME" | crontab -
    fi
    
    # 添加新的cron任务
    (crontab -l 2>/dev/null; echo "# $SERVICE_NAME monitor") | crontab -
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    
    print_success "cron任务设置完成"
}

# 创建启动脚本
create_startup_script() {
    print_info "创建启动脚本..."
    
    cat > "$PROJECT_DIR/start_server.sh" << 'EOF'
#!/bin/bash

# 服务器启动脚本
# 光伏+储能项目经济性分析系统

PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
SERVER_DIR="$PROJECT_DIR/server"

# 加载NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# 切换到项目目录
cd "$PROJECT_DIR"

# 启动监控脚本
nohup ./server_monitor.sh monitor > /dev/null 2>&1 &

echo "服务器监控已启动"
EOF

    chmod +x "$PROJECT_DIR/start_server.sh"
    print_success "启动脚本创建完成"
}

# 创建停止脚本
create_stop_script() {
    print_info "创建停止脚本..."
    
    cat > "$PROJECT_DIR/stop_server.sh" << 'EOF'
#!/bin/bash

# 服务器停止脚本
# 光伏+储能项目经济性分析系统

PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
SERVER_DIR="$PROJECT_DIR/server"

# 停止监控脚本
pkill -f "server_monitor.sh" || true

# 停止服务器进程
if [ -f "$SERVER_DIR/.server.pid" ]; then
    SERVER_PID=$(cat "$SERVER_DIR/.server.pid")
    if ps -p "$SERVER_PID" > /dev/null 2>&1; then
        echo "停止服务器进程 PID: $SERVER_PID"
        kill "$SERVER_PID"
        sleep 2
        if ps -p "$SERVER_PID" > /dev/null 2>&1; then
            kill -9 "$SERVER_PID"
        fi
    fi
    rm -f "$SERVER_DIR/.server.pid"
fi

# 停止PM2进程（如果存在）
if command -v pm2 > /dev/null 2>&1; then
    pm2 delete pv-server 2>/dev/null || true
fi

# 清理端口占用
pkill -f "node.*src/index.js" || true

echo "服务器已停止"
EOF

    chmod +x "$PROJECT_DIR/stop_server.sh"
    print_success "停止脚本创建完成"
}

# 创建状态检查脚本
create_status_script() {
    print_info "创建状态检查脚本..."
    
    cat > "$PROJECT_DIR/server_status.sh" << 'EOF'
#!/bin/bash

# 服务器状态检查脚本
# 光伏+储能项目经济性分析系统

PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
SERVER_DIR="$PROJECT_DIR/server"
API_PORT="3001"
HEALTH_URL="http://localhost:$API_PORT/api/health"

echo "=== 光伏+储能项目经济性分析系统 - 服务器状态 ==="
echo "时间: $(date)"
echo

# 检查API健康状态
echo "1. API健康检查:"
if curl -s --max-time 5 "$HEALTH_URL" > /dev/null 2>&1; then
    echo "   ✓ API服务正常运行"
    echo "   响应: $(curl -s --max-time 5 "$HEALTH_URL" | jq -r '.message' 2>/dev/null || echo '无法解析响应')"
else
    echo "   ✗ API服务异常"
fi
echo

# 检查进程状态
echo "2. 进程状态:"
if [ -f "$SERVER_DIR/.server.pid" ]; then
    SERVER_PID=$(cat "$SERVER_DIR/.server.pid")
    if ps -p "$SERVER_PID" > /dev/null 2>&1; then
        echo "   ✓ 服务器进程运行中 (PID: $SERVER_PID)"
        echo "   内存使用: $(ps -p "$SERVER_PID" -o rss= | awk '{print $1/1024 " MB"}')"
        echo "   CPU使用: $(ps -p "$SERVER_PID" -o %cpu= | awk '{print $1 "%"}')"
    else
        echo "   ✗ 服务器进程未运行 (PID文件存在但进程不存在)"
    fi
else
    echo "   ✗ 未找到PID文件"
fi
echo

# 检查PM2状态
echo "3. PM2状态:"
if command -v pm2 > /dev/null 2>&1; then
    if pm2 list | grep -q "pv-server"; then
        echo "   ✓ PM2进程运行中"
        pm2 list | grep pv-server
    else
        echo "   ✗ PM2进程未运行"
    fi
else
    echo "   - PM2未安装"
fi
echo

# 检查端口占用
echo "4. 端口状态:"
if netstat -tlnp 2>/dev/null | grep -q ":$API_PORT "; then
    echo "   ✓ 端口 $API_PORT 已被占用"
    netstat -tlnp 2>/dev/null | grep ":$API_PORT " | head -1
else
    echo "   ✗ 端口 $API_PORT 未被占用"
fi
echo

# 检查日志
echo "5. 最近日志:"
if [ -f "$SERVER_DIR/server.log" ]; then
    echo "   最后10行日志:"
    tail -10 "$SERVER_DIR/server.log" | sed 's/^/   /'
else
    echo "   ✗ 未找到日志文件"
fi
echo

# 检查监控脚本
echo "6. 监控脚本状态:"
if pgrep -f "server_monitor.sh" > /dev/null; then
    echo "   ✓ 监控脚本运行中"
    echo "   PID: $(pgrep -f "server_monitor.sh")"
else
    echo "   ✗ 监控脚本未运行"
fi
echo

echo "=== 状态检查完成 ==="
EOF

    chmod +x "$PROJECT_DIR/server_status.sh"
    print_success "状态检查脚本创建完成"
}

# 主函数
main() {
    print_info "开始设置服务..."
    
    # 确保监控脚本可执行
    chmod +x "$MONITOR_SCRIPT"
    
    # 创建各种脚本
    create_startup_script
    create_stop_script
    create_status_script
    
    # 设置cron任务
    setup_cron
    
    print_success "服务设置完成！"
    print_info "可用命令："
    print_info "  启动服务: ./start_server.sh"
    print_info "  停止服务: ./stop_server.sh"
    print_info "  检查状态: ./server_status.sh"
    print_info "  监控服务: ./server_monitor.sh monitor"
    print_info "  手动重启: ./server_monitor.sh restart"
}

# 执行主函数
main "$@"
