# 光伏倾角分析功能

## 功能概述

新增的光伏倾角分析功能允许用户对选中的光伏组件进行不同倾角下的发电量和收益分析，帮助用户找到最优的光伏板安装角度。

## 功能特点

### 1. 倾角范围分析
- 分析范围：0度到90度，每1度计算一次
- 总共91个数据点，提供详细的倾角性能曲线

### 2. 多指标分析
系统会计算以下5个关键指标：
- **年发电量** (kWh) - 该倾角下的年度总发电量
- **年光伏收益** (JPY) - 该倾角下的年度总收益
- **每千瓦发电量** (kWh/kW) - 单位装机容量的发电量
- **每千瓦收益** (JPY/kW) - 单位装机容量的收益
- **光伏年收益率** (%) - 投资回报率

### 3. 可视化图表
- 基于ECharts的交互式图表
- 支持多指标同时显示
- 可选择显示/隐藏不同指标
- 支持图表缩放和数据点查看
- 显示最优倾角信息

### 4. 智能计算
- 基于真实的太阳辐射数据
- 考虑直射和漫射辐射
- 结合光伏组件的朝向和效率
- 使用项目实际的电价政策

## 使用方法

### 1. 进入光伏分析页面
1. 打开已完成分析的项目
2. 切换到"光伏分析"标签页

### 2. 选择分析组件
1. 在组件列表中勾选要分析的光伏组件
2. 可以选择一个或多个组件进行组合分析

### 3. 启动倾角分析
1. 点击组件列表右上角的"光伏倾角分析"按钮
2. 系统开始计算，显示进度提示
3. 计算完成后在页面底部显示分析结果

### 4. 查看分析结果
1. **最优角度信息**：显示最优倾角、最大发电量和最大收益
2. **指标选择**：勾选要显示的指标类型
3. **交互式图表**：查看不同倾角下的性能曲线

## 技术实现

### 1. 核心算法
- 太阳位置计算：基于时间和地理位置
- 辐射量计算：考虑太阳高度角和方位角
- 发电量计算：结合组件效率和逆变器效率
- 收益计算：基于实际电价政策和用电模式

### 2. 文件结构
```
src/
├── services/
│   └── pvTiltAnalysisService.ts     # 倾角分析计算服务
├── components/
│   └── analysis/
│       ├── PVAnalysisTab.tsx        # 光伏分析主页面（已修改）
│       └── PVTiltAnalysisChart.tsx  # 倾角分析图表组件
└── locales/
    ├── zh.json                      # 中文翻译（已更新）
    ├── en.json                      # 英文翻译（已更新）
    └── ja.json                      # 日文翻译（已更新）
```

### 3. 主要组件

#### PVTiltAnalysisChart
- 负责倾角分析结果的可视化
- 支持多指标选择和图表交互
- 显示最优角度信息

#### pvTiltAnalysisService
- 核心计算逻辑
- 处理0-90度的倾角计算
- 返回详细的分析结果

## 性能优化

### 1. 计算优化
- 异步计算，不阻塞UI
- 显示计算进度
- 错误处理和重试机制

### 2. 用户体验
- 实时进度提示
- 清晰的错误信息
- 直观的结果展示

## 注意事项

1. **数据要求**：需要项目已完成分析，包含完整的光照数据和电价政策
2. **组件选择**：必须至少选择一个光伏组件才能进行分析
3. **计算时间**：91个角度的计算可能需要几秒到几十秒，取决于数据量
4. **结果精度**：计算结果保留适当的小数位数，确保可读性

## 未来扩展

1. **更多分析维度**：可以扩展到方位角分析
2. **批量分析**：支持多个项目的批量倾角分析
3. **优化建议**：基于分析结果提供具体的安装建议
4. **导出功能**：支持分析结果的导出和报告生成

## 多语言支持

功能完全支持中文、英文和日文界面，所有文本都已进行国际化处理。
