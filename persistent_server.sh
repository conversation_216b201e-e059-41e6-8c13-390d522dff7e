#!/bin/bash

# 持久化服务器启动脚本
# 光伏+储能项目经济性分析系统
# 彻底解决Hostinger共享主机环境中的服务器持续运行问题

set -e

# 配置变量
PROJECT_DIR="/home/<USER>/domains/pv-analysis.top/public_html"
SERVER_DIR="$PROJECT_DIR/server"
API_PORT="3001"
HEALTH_URL="http://localhost:$API_PORT/api/health"
LOG_FILE="$SERVER_DIR/persistent.log"
PID_FILE="$SERVER_DIR/.persistent.pid"
MONITOR_PID_FILE="$SERVER_DIR/.monitor.pid"
RESTART_DELAY=5
MAX_RESTART_ATTEMPTS=3
HEALTH_CHECK_INTERVAL=30
RESTART_COOLDOWN=300  # 5分钟冷却期

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 设置环境
setup_environment() {
    # 加载NVM
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

    # 使用Node.js
    nvm use node > /dev/null 2>&1

    # 设置PATH确保node和npm可用
    export PATH="$HOME/.nvm/versions/node/$(nvm current)/bin:$PATH"

    # 进入服务器目录
    cd "$SERVER_DIR"
}

# 检查服务器是否运行
check_server() {
    if curl -s --max-time 5 "$HEALTH_URL" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 检查进程是否运行
check_process() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 停止服务器
stop_server() {
    print_info "停止服务器..."

    # 停止主进程
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            print_info "停止服务器进程 PID: $pid"
            kill "$pid" 2>/dev/null || true
            sleep 2
            if ps -p "$pid" > /dev/null 2>&1; then
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "$PID_FILE"
    fi

    # 停止监控进程
    if [ -f "$MONITOR_PID_FILE" ]; then
        local monitor_pid=$(cat "$MONITOR_PID_FILE")
        if ps -p "$monitor_pid" > /dev/null 2>&1; then
            print_info "停止监控进程 PID: $monitor_pid"
            kill "$monitor_pid" 2>/dev/null || true
        fi
        rm -f "$MONITOR_PID_FILE"
    fi

    # 清理所有相关进程
    pkill -f "node.*src/index.js" 2>/dev/null || true
    pkill -f "persistent_server.sh" 2>/dev/null || true

    print_success "服务器已停止"
}

# 启动服务器
start_server() {
    print_info "启动服务器..."

    setup_environment

    # 停止现有进程
    stop_server

    # 启动服务器进程
    print_info "启动Node.js服务器..."
    nohup node src/index.js > "$LOG_FILE" 2>&1 &
    local server_pid=$!
    echo "$server_pid" > "$PID_FILE"

    # 等待服务器启动
    sleep 3

    # 验证启动
    local attempts=0
    while [ $attempts -lt 10 ]; do
        if check_server; then
            print_success "服务器启动成功，PID: $server_pid"
            return 0
        fi
        attempts=$((attempts + 1))
        print_info "等待服务器启动... ($attempts/10)"
        sleep 2
    done

    print_error "服务器启动失败"
    return 1
}

# 重启服务器
restart_server() {
    print_warning "重启服务器..."
    stop_server
    sleep 2
    start_server
}

# 监控循环
monitor_loop() {
    print_info "开始监控服务器..."

    local restart_count=0
    local last_restart_time=0

    while true; do
        local current_time=$(date +%s)

        if check_server; then
            # 服务器正常，重置重启计数
            if [ $restart_count -gt 0 ]; then
                print_info "服务器恢复正常，重置重启计数"
                restart_count=0
            fi
        else
            print_warning "服务器健康检查失败"

            # 检查是否在重启冷却期内
            local time_since_restart=$((current_time - last_restart_time))
            if [ $time_since_restart -lt $RESTART_DELAY ]; then
                print_info "在重启冷却期内，等待..."
                sleep $((RESTART_DELAY - time_since_restart))
                continue
            fi

            # 检查重启次数
            if [ $restart_count -lt $MAX_RESTART_ATTEMPTS ]; then
                restart_count=$((restart_count + 1))
                last_restart_time=$current_time

                print_warning "尝试重启服务器 (第 $restart_count 次)"

                if start_server; then
                    print_success "服务器重启成功"
                else
                    print_error "服务器重启失败"
                fi
            else
                print_error "达到最大重启次数 ($MAX_RESTART_ATTEMPTS)"
                print_error "服务器可能存在严重问题，停止自动重启"

                # 等待一段时间后重置计数器
                print_info "等待5分钟后重置重启计数器..."
                sleep 300
                restart_count=0
                last_restart_time=0
            fi
        fi

        # 等待下次检查
        sleep 30
    done
}

# 启动监控
start_monitor() {
    print_info "启动监控进程..."

    # 停止现有监控
    if [ -f "$MONITOR_PID_FILE" ]; then
        local old_monitor_pid=$(cat "$MONITOR_PID_FILE")
        if ps -p "$old_monitor_pid" > /dev/null 2>&1; then
            kill "$old_monitor_pid" 2>/dev/null || true
        fi
    fi

    # 启动新的监控进程
    nohup bash "$0" monitor_loop > /dev/null 2>&1 &
    local monitor_pid=$!
    echo "$monitor_pid" > "$MONITOR_PID_FILE"

    print_success "监控进程已启动，PID: $monitor_pid"
}

# 显示状态
show_status() {
    echo "=== 光伏+储能项目经济性分析系统 - 服务器状态 ==="
    echo "时间: $(date)"
    echo

    # 检查API健康状态
    echo "1. API健康检查:"
    if check_server; then
        echo "   ✓ API服务正常运行"
        local response=$(curl -s --max-time 5 "$HEALTH_URL" 2>/dev/null || echo '{"message":"无法获取响应"}')
        echo "   响应: $(echo "$response" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)"
    else
        echo "   ✗ API服务异常"
    fi
    echo

    # 检查进程状态
    echo "2. 进程状态:"
    if check_process; then
        local pid=$(cat "$PID_FILE")
        echo "   ✓ 服务器进程运行中 (PID: $pid)"
        if ps -p "$pid" > /dev/null 2>&1; then
            local mem=$(ps -p "$pid" -o rss= 2>/dev/null | awk '{print $1/1024 " MB"}' || echo "未知")
            local cpu=$(ps -p "$pid" -o %cpu= 2>/dev/null | awk '{print $1 "%"}' || echo "未知")
            echo "   内存使用: $mem"
            echo "   CPU使用: $cpu"
        fi
    else
        echo "   ✗ 服务器进程未运行"
    fi
    echo

    # 检查监控进程
    echo "3. 监控进程状态:"
    if [ -f "$MONITOR_PID_FILE" ]; then
        local monitor_pid=$(cat "$MONITOR_PID_FILE")
        if ps -p "$monitor_pid" > /dev/null 2>&1; then
            echo "   ✓ 监控进程运行中 (PID: $monitor_pid)"
        else
            echo "   ✗ 监控进程未运行"
        fi
    else
        echo "   ✗ 未找到监控PID文件"
    fi
    echo

    # 检查端口占用
    echo "4. 端口状态:"
    if netstat -tlnp 2>/dev/null | grep -q ":$API_PORT "; then
        echo "   ✓ 端口 $API_PORT 已被占用"
        local port_info=$(netstat -tlnp 2>/dev/null | grep ":$API_PORT " | head -1)
        echo "   $port_info"
    else
        echo "   ✗ 端口 $API_PORT 未被占用"
    fi
    echo

    # 检查最近日志
    echo "5. 最近日志 (最后10行):"
    if [ -f "$LOG_FILE" ]; then
        tail -10 "$LOG_FILE" | sed 's/^/   /'
    else
        echo "   ✗ 未找到日志文件"
    fi
    echo

    echo "=== 状态检查完成 ==="
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"

    case "${1:-start}" in
        "start")
            start_server && start_monitor
            ;;
        "stop")
            stop_server
            ;;
        "restart")
            restart_server && start_monitor
            ;;
        "monitor")
            start_monitor
            ;;
        "monitor_loop")
            monitor_loop
            ;;
        "status")
            show_status
            ;;
        "check")
            if check_server; then
                print_success "服务器运行正常"
                exit 0
            else
                print_error "服务器异常"
                exit 1
            fi
            ;;
        *)
            echo "用法: $0 {start|stop|restart|monitor|status|check}"
            echo "  start   - 启动服务器和监控"
            echo "  stop    - 停止服务器和监控"
            echo "  restart - 重启服务器和监控"
            echo "  monitor - 仅启动监控"
            echo "  status  - 显示详细状态"
            echo "  check   - 检查服务器状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
