#!/bin/bash

# 启动Node.js服务器脚本
# 光伏+储能项目经济性分析系统
# 专为Hostinger共享主机环境设计

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 环境设置
setup_environment() {
    # 检查是否在Hostinger环境
    if [[ "$PWD" == *"/domains/pv-analysis.top/"* ]]; then
        print_info "检测到Hostinger环境"
        
        # 设置Node.js环境
        export NVM_DIR="$HOME/.nvm"
        if [ -s "$NVM_DIR/nvm.sh" ]; then
            source "$NVM_DIR/nvm.sh"
            nvm use node 2>/dev/null || nvm use default 2>/dev/null || true
        fi
        
        # 设置PATH
        export PATH="$HOME/.nvm/versions/node/$(nvm current)/bin:$PATH"
        
        # 验证Node.js
        if ! command -v node >/dev/null 2>&1; then
            print_error "Node.js未找到，请检查NVM安装"
            exit 1
        fi
        
        print_success "Node.js环境已设置: $(node -v)"
    else
        print_info "本地开发环境"
    fi
}

# 停止现有服务器
stop_existing_server() {
    print_info "停止现有服务器进程..."
    
    # 停止PID文件中的进程
    if [ -f ".server.pid" ]; then
        local pid=$(cat .server.pid)
        if ps -p "$pid" > /dev/null 2>&1; then
            print_info "停止服务器进程 PID: $pid"
            kill "$pid" 2>/dev/null || true
            sleep 2
            if ps -p "$pid" > /dev/null 2>&1; then
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f .server.pid
    fi
    
    # 清理端口占用
    pkill -f "node.*src/index.js" 2>/dev/null || true
    pkill -f "nodemon.*src/index.js" 2>/dev/null || true
    
    # 停止PM2进程（如果存在）
    if command -v pm2 >/dev/null 2>&1; then
        pm2 delete pv-server 2>/dev/null || true
        pm2 kill 2>/dev/null || true
    fi
    
    sleep 2
    print_success "现有服务器进程已停止"
}

# 启动服务器
start_server() {
    print_info "启动Node.js服务器..."
    
    # 设置环境变量
    export PORT=3001
    export NODE_ENV=production
    
    # 启动服务器
    nohup node src/index.js > server.log 2>&1 &
    local server_pid=$!
    
    # 保存PID
    echo "$server_pid" > .server.pid
    
    print_info "服务器已启动，PID: $server_pid"
    
    # 等待启动
    sleep 3
    
    # 验证启动
    if ps -p "$server_pid" > /dev/null 2>&1; then
        print_success "服务器启动成功"
        
        # 健康检查
        local attempts=0
        while [ $attempts -lt 10 ]; do
            if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
                print_success "服务器健康检查通过"
                return 0
            fi
            attempts=$((attempts + 1))
            print_info "等待服务器就绪... ($attempts/10)"
            sleep 2
        done
        
        print_warning "服务器启动但健康检查未通过"
        return 0
    else
        print_error "服务器启动失败"
        return 1
    fi
}

# 主函数
main() {
    print_info "光伏+储能项目经济性分析系统 - 服务器启动"
    
    # 进入服务器目录
    cd "$(dirname "$0")"
    if [ -d "server" ]; then
        cd server
    fi
    
    # 设置环境
    setup_environment
    
    # 停止现有服务器
    stop_existing_server
    
    # 启动服务器
    if start_server; then
        print_success "服务器启动完成"
        print_info "访问地址: http://localhost:3001"
        print_info "日志文件: $(pwd)/server.log"
        print_info "PID文件: $(pwd)/.server.pid"
    else
        print_error "服务器启动失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
