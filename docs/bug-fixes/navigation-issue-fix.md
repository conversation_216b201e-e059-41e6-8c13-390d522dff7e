# 项目创建流程导航问题修复记录

## 问题描述

在项目创建流程中，除了在"项目基础数据"节点，点击左侧导航栏可以切换到对应页面外，其他节点（光照数据、电价政策、用电数据等）都无法正常切换页面。具体表现为：

1. 点击导航栏时，浏览器地址栏的URL会变成错误的格式，例如`http://localhost:5173/projects/new/settings`而不是`http://localhost:5173/settings`。
2. 虽然URL已经改变，但页面内容没有更新，仍然停留在项目创建流程中。
3. 如果此时切换到"项目基础数据"节点，页面会闪过基础信息页，然后马上跳到地址栏已经改好的目标页面。
4. 点击"返回列表"按钮可以正常退出项目创建流程。

## 问题原因

经过深入分析，发现了以下根本原因：

1. **URL格式错误与导航拦截机制冲突**：
   - 当用户在项目创建流程中点击导航栏时，URL变成了错误的嵌套格式。
   - 导航拦截机制没有正确处理这种嵌套路径的情况，导致URL已经改变但页面内容没有更新。

2. **React Router与组件生命周期冲突**：
   - 当URL改变时，React Router尝试渲染新的组件，但由于导航拦截机制的存在，`NewProject`组件没有被卸载，导致两个组件的状态冲突。
   - 这种冲突在包含复杂图表组件的步骤中尤为明显，可能导致无限渲染循环。

3. **使用window.location与React Router混用**：
   - 在多处代码中混用了`window.location.href`和React Router的`navigate`函数，导致导航行为不一致。
   - "返回列表"按钮可以正常工作是因为它使用了`window.location.href`强制刷新页面。

4. **基础信息页面可以正常切换的原因**：
   - 在基础信息页面中，可能没有复杂的图表组件或大量的`useEffect`依赖项，不会触发无限渲染循环。
   - 当从基础信息页面切换到其他页面时，React Router能够正确地卸载`NewProject`组件并渲染目标组件。

## 解决方案

实施了以下解决方案：

1. **改进导航拦截函数**：
   - 在`MainLayout.tsx`中，修改了导航拦截函数，使其能够检测当前URL是否已经是嵌套路径（如`/projects/new/settings`）。
   - 如果检测到嵌套路径，会使用`window.location.href`强制导航，完全刷新页面，确保组件能够正确卸载和重新渲染。

2. **改进导航事件处理函数**：
   - 在`NewProject.tsx`中，修改了导航事件处理函数，使其能够检测当前URL是否已经是嵌套路径。
   - 如果检测到嵌套路径，会使用`window.location.href`强制导航，确保页面完全刷新。

3. **统一使用window.location导航**：
   - 在所有导航函数中，统一使用`window.location.href`进行导航，确保页面完全刷新，避免React Router的导航机制与组件生命周期冲突。

4. **添加路径检测和修正逻辑**：
   - 添加了路径检测逻辑，能够识别嵌套路径和特殊格式的URL。
   - 在导航前进行路径检测和修正，确保导航到正确的目标页面。

## 具体修改

### 1. MainLayout.tsx中的导航拦截函数

```javascript
// 导航拦截函数
const interceptNavigation = (path: string) => {
  // 确保路径是绝对路径，而不是相对于当前路径
  const absolutePath = path.startsWith('/') ? path : `/${path}`;
  
  // 检查当前路径是否为新建项目页面或编辑项目页面
  const isInProjectCreation = location.pathname === '/projects/new' || location.pathname.startsWith('/projects/edit/');
  
  // 检查当前路径是否已经是错误的嵌套路径（如/projects/new/settings）
  const isInNestedPath = location.pathname.includes('/projects/new/') || 
                        (location.pathname.includes('/projects/edit/') && location.pathname.split('/').length > 4);
  
  console.log('当前路径:', location.pathname, '目标路径:', absolutePath);
  console.log('是否在项目创建流程中:', isInProjectCreation, '是否在嵌套路径中:', isInNestedPath);
  
  // 如果当前在嵌套路径中（如/projects/new/settings），需要特殊处理
  if (isInNestedPath) {
    console.log('检测到嵌套路径，强制导航到:', absolutePath);
    // 使用window.location强制导航，完全刷新页面
    window.location.href = absolutePath;
    return;
  }
  
  // 如果在项目创建流程中，使用自定义事件通知项目页面
  if (isInProjectCreation) {
    console.log('触发导航拦截，目标路径:', absolutePath);
    
    // 使用自定义事件通知项目页面
    const event = new CustomEvent('navigation-attempt', {
      detail: { targetPath: absolutePath },
      bubbles: true,
      cancelable: true
    });
    window.dispatchEvent(event);

    // 不立即导航，等待项目页面处理
    return;
  }

  // 如果不是项目编辑页面，直接导航
  console.log('直接导航到:', absolutePath);
  navigate(absolutePath);
};
```

### 2. NewProject.tsx中的导航事件处理函数

```javascript
// 监听全局导航尝试事件
useEffect(() => {
  const handleNavigationAttempt = (event: any) => {
    // 获取目标路径
    const targetPath = event.detail.targetPath;
    console.log('收到导航尝试事件，目标路径:', targetPath);

    // 检查当前URL是否已经是嵌套路径（如/projects/new/settings）
    const currentPath = window.location.pathname;
    const isInNestedPath = currentPath.includes('/projects/new/') || 
                          (currentPath.includes('/projects/edit/') && currentPath.split('/').length > 4);
    
    console.log('当前路径:', currentPath, '是否在嵌套路径中:', isInNestedPath);
    
    // 如果当前在嵌套路径中，需要特殊处理
    if (isInNestedPath) {
      console.log('检测到嵌套路径，强制导航到:', targetPath);
      // 使用window.location强制导航，完全刷新页面
      window.location.href = targetPath;
      return;
    }

    // 检查是否是离开项目创建/编辑流程的导航
    // 只有当导航到项目创建/编辑流程外部时才显示确认对话框
    const isLeavingProjectCreation = typeof targetPath === 'string' &&
      !targetPath.startsWith('/projects/new') &&
      !targetPath.startsWith('/projects/edit');

    if (hasChanges && isLeavingProjectCreation) {
      // 阻止默认导航行为
      if (event.cancelable) {
        event.preventDefault();
      }

      // 设置目标路由
      setTargetRoute(targetPath);

      // 显示确认对话框
      setShowNavigationDialog(true);
      console.log('尝试离开项目创建流程，显示确认对话框');
    } else if (!hasChanges && isLeavingProjectCreation) {
      // 如果没有更改，直接导航
      console.log('无更改，直接导航到:', targetPath);
      // 使用window.location强制导航，确保页面完全刷新
      window.location.href = targetPath;
    } else {
      // 这是项目创建流程内部的导航，例如切换步骤
      console.log('项目创建流程内部导航');
      // 不需要特殊处理
    }
  };

  // 添加事件监听
  window.addEventListener('navigation-attempt', handleNavigationAttempt as EventListener);

  // 清理函数
  return () => {
    window.removeEventListener('navigation-attempt', handleNavigationAttempt as EventListener);
  };
}, [hasChanges, navigate]);
```

## 经验教训

1. **避免混用导航方式**：
   - 在React应用中，应该统一使用React Router的导航机制，避免混用`window.location.href`和`navigate`函数。
   - 如果必须使用`window.location.href`，应该明确知道这会导致整个页面重新加载。

2. **谨慎处理导航拦截**：
   - 导航拦截机制应该考虑各种边缘情况，特别是URL已经改变但组件没有卸载的情况。
   - 应该提供一种机制来强制刷新页面，确保组件能够正确卸载和重新渲染。

3. **注意组件生命周期**：
   - 在复杂的单页应用中，组件的挂载和卸载是关键的生命周期事件，应该确保它们能够正确执行。
   - 特别是包含复杂图表组件的页面，更容易出现无限渲染循环和组件状态冲突。

4. **路径格式检查**：
   - 在处理路由时，应该检查路径格式是否正确，避免生成错误的嵌套路径。
   - 应该提供路径修正逻辑，确保导航到正确的目标页面。

## 结论

通过改进导航拦截函数和导航事件处理函数，添加路径检测和修正逻辑，统一使用`window.location.href`进行导航，成功解决了项目创建流程中无法正常切换页面的问题。这些修改确保了在项目创建流程中，用户可以随时切换到其他页面，提高了用户体验和系统的可用性。
